package querymarket

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetActiveMarketsQuery struct {
	// No parameters needed for getting all active markets
}

type GetActiveMarketsQueryHandler decorator.QueryHandler[GetActiveMarketsQuery, []*Market]

type getActiveMarketsQueryHandler struct {
	repo MarketReadModel
}

func NewGetActiveMarketsQueryHandler(
	repo MarketReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetActiveMarketsQueryHandler {

	return decorator.ApplyQueryDecorators[GetActiveMarketsQuery, []*Market](
		getActiveMarketsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getActiveMarketsQueryHandler) Handle(ctx context.Context, query GetActiveMarketsQuery) ([]*Market, error) {
	return h.repo.GetActiveMarkets(ctx)
}
