package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateLastUsed struct {
	ID     int
	UsedBy string
}

type UpdateLastUsedHandler decorator.CommandHandler[UpdateLastUsed]

type updateLastUsedHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewUpdateLastUsedHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateLastUsedHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateLastUsed](
		updateLastUsedHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateLastUsedHandler) Handle(ctx context.Context, cmd UpdateLastUsed) error {
	return h.repo.Update(ctx, cmd.ID, func(c *entity.CrawlConfig) (*entity.CrawlConfig, error) {
		c.UpdateLastUsedAt()
		return c, nil
	})
}
