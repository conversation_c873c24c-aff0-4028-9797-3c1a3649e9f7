package repository

import (
	"context"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/job/sqlc"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/pkg/database"
)

type jobQueryRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewJobQueryRepository creates a new job query repository
func NewJobQueryRepository(db database.DBTX) queryjob.JobReadModel {
	return &jobQueryRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// GetAllJobs retrieves all jobs with pagination and ordering
func (r *jobQueryRepository) GetAllJobs(ctx context.Context, q queryjob.GetAllJobsQuery) ([]*queryjob.Job, error) {
	// Calculate offset from page and page size
	offset := (q.Page - 1) * q.PageSize

	params := sqlc.GetAllJobsWithPaginationParams{
		Limit:   int32(q.PageSize),
		Offset:  int32(offset),
		Column3: q.OrderBy,
	}

	sqlcJobs, err := r.queries.GetAllJobsWithPagination(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get all jobs: %w", err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetJobById retrieves a single job by ID
func (r *jobQueryRepository) GetJobById(ctx context.Context, id int) (*queryjob.Job, error) {
	sqlcJob, err := r.queries.GetJobByIdQuery(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get job by id %d: %w", id, err)
	}

	job, err := FromSQLCModelToQueryModel(sqlcJob)
	if err != nil {
		return nil, fmt.Errorf("failed to convert job model: %w", err)
	}

	return job, nil
}

// GetJobsByStatus retrieves jobs by status
func (r *jobQueryRepository) GetJobsByStatus(ctx context.Context, status string) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetJobsByStatusQuery(ctx, status)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by status %s: %w", status, err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetJobsByType retrieves jobs by type
func (r *jobQueryRepository) GetJobsByType(ctx context.Context, jobType string) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetJobsByTypeQuery(ctx, jobType)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by type %s: %w", jobType, err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetPendingJobs retrieves all pending jobs
func (r *jobQueryRepository) GetPendingJobs(ctx context.Context) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetPendingJobsQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get pending jobs: %w", err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetRunningJobs retrieves all running jobs
func (r *jobQueryRepository) GetRunningJobs(ctx context.Context) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetRunningJobsQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get running jobs: %w", err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetJobsByConfigId retrieves jobs by crawl config ID
func (r *jobQueryRepository) GetJobsByConfigId(ctx context.Context, configId int) ([]*queryjob.Job, error) {
	configIdPtr := int32(configId)
	sqlcJobs, err := r.queries.GetJobsByConfigIdQuery(ctx, &configIdPtr)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs by config id %d: %w", configId, err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// FilterJobs retrieves jobs based on filter criteria
func (r *jobQueryRepository) FilterJobs(ctx context.Context, q queryjob.FilterJobsQuery) ([]*queryjob.Job, error) {
	panic("implement me")
}

// GetJobsNeedingRetry retrieves jobs that need retry
func (r *jobQueryRepository) GetJobsNeedingRetry(ctx context.Context) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetJobsNeedingRetryQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get jobs needing retry: %w", err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// GetExpiredJobs retrieves expired jobs
func (r *jobQueryRepository) GetExpiredJobs(ctx context.Context) ([]*queryjob.Job, error) {
	sqlcJobs, err := r.queries.GetExpiredJobsQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get expired jobs: %w", err)
	}

	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, fmt.Errorf("failed to convert job model: %w", err)
		}
		jobs[i] = job
	}

	return jobs, nil
}

// CountJobs returns the total count of jobs
func (r *jobQueryRepository) CountJobs(ctx context.Context) (int64, error) {
	count, err := r.queries.CountJobsQuery(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs: %w", err)
	}
	return count, nil
}

// CountJobsByStatus returns the count of jobs by status
func (r *jobQueryRepository) CountJobsByStatus(ctx context.Context, status string) (int64, error) {
	count, err := r.queries.CountJobsByStatusQuery(ctx, status)
	if err != nil {
		return 0, fmt.Errorf("failed to count jobs by status %s: %w", status, err)
	}
	return count, nil
}
