package pagination

import (
	"net/http"
	"strconv"
)

const (
	DefaultPageSize = 10
	MaxPageSize     = 100
)

type Pagination struct {
	Page     int   `json:"page" form:"page"`
	PageSize int   `json:"page_size" form:"page_size"`
	Total    int64 `json:"total"`
	Pages    int   `json:"pages"`
}

type PaginatedResponse[T any] struct {
	Data       []*T       `json:"data"`
	Pagination Pagination `json:"pagination"`
}

func NewPagination(page int, pageSize int) *Pagination {
	p := &Pagination{
		Page:     page,
		PageSize: pageSize,
	}
	p.Validate()
	return p
}

func FromRequest(r *http.Request) *Pagination {
	page, err := strconv.Atoi(r.URL.Query().Get("page"))
	if err != nil {
		page = 1
	}

	pageSize, err := strconv.Atoi(r.URL.Query().Get("page_size"))
	if err != nil {
		pageSize = DefaultPageSize
	}

	return NewPagination(page, pageSize)
}

func (p *Pagination) Validate() {
	if p.Page < 1 {
		p.Page = 1
	}
	if p.PageSize < 1 {
		p.PageSize = DefaultPageSize
	}
	if p.PageSize > MaxPageSize {
		p.PageSize = MaxPageSize
	}
}

func (p *Pagination) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

func (p *Pagination) GetLimit() int {
	return p.PageSize
}

func (p *Pagination) SetTotal(total int64) {
	p.Total = total
	p.Pages = int((total + int64(p.PageSize) - 1) / int64(p.PageSize))
}

func (p *Pagination) HasNextPage() bool {
	return p.Page < p.Pages
}

func (p *Pagination) HasPreviousPage() bool {
	return p.Page > 1
}

func (p *Pagination) GetNextPage() int {
	if p.HasNextPage() {
		return p.Page + 1
	}
	return p.Page
}

func (p *Pagination) GetPreviousPage() int {
	if p.HasPreviousPage() {
		return p.Page - 1
	}
	return p.Page
}
func CreateResponse[T any](p *Pagination, data []*T) *PaginatedResponse[T] {
	return &PaginatedResponse[T]{
		Data:       data,
		Pagination: *p,
	}
}
