package repository

import (
	"context"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/market/sqlc"
	querymarket "go_core_market/internal/crawler/app/query/market"
	"go_core_market/pkg/database"
	"log"
)

type marketQueryRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewMarketQueryRepository creates a new market query repository
func NewMarketQueryRepository(db database.DBTX) querymarket.MarketReadModel {
	return &marketQueryRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// GetAllMarkets retrieves all markets with pagination and ordering
func (r *marketQueryRepository) GetAllMarkets(ctx context.Context, q querymarket.GetAllMarketsQuery) ([]*querymarket.Market, error) {
	// Calculate offset from page and page size
	offset := (q.Page - 1) * q.PageSize

	params := sqlc.GetAllMarketsWithPaginationParams{
		Limit:   int32(q.PageSize),
		Offset:  int32(offset),
		Column3: q.OrderBy,
	}
	log.Printf("params: %v", params)
	sqlcMarkets, err := r.queries.GetAllMarketsWithPagination(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get all markets: %w", err)
	}

	markets := make([]*querymarket.Market, len(sqlcMarkets))
	for i, sqlcMarket := range sqlcMarkets {
		market, err := FromSQLCModelToQueryModel(sqlcMarket)
		if err != nil {
			return nil, fmt.Errorf("failed to convert market model: %w", err)
		}
		markets[i] = market
	}

	return markets, nil
}

// GetMarketById retrieves a single market by ID
func (r *marketQueryRepository) GetMarketById(ctx context.Context, id int) (*querymarket.Market, error) {
	sqlcMarket, err := r.queries.GetMarketByIdQuery(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get market by id %d: %w", id, err)
	}

	market, err := FromSQLCModelToQueryModel(sqlcMarket)
	if err != nil {
		return nil, fmt.Errorf("failed to convert market model: %w", err)
	}

	return market, nil
}

// GetActiveMarkets retrieves all active markets
func (r *marketQueryRepository) GetActiveMarkets(ctx context.Context) ([]*querymarket.Market, error) {
	sqlcMarkets, err := r.queries.GetActiveMarketsQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active markets: %w", err)
	}

	markets := make([]*querymarket.Market, len(sqlcMarkets))
	for i, sqlcMarket := range sqlcMarkets {
		market, err := FromSQLCModelToQueryModel(sqlcMarket)
		if err != nil {
			return nil, fmt.Errorf("failed to convert market model: %w", err)
		}
		markets[i] = market
	}

	return markets, nil
}

// FilterMarkets retrieves markets based on filter criteria
func (r *marketQueryRepository) FilterMarkets(ctx context.Context, q querymarket.FilterMarketsQuery) ([]*querymarket.Market, error) {
	// Convert slice filters to single values (take first element if exists, empty string otherwise)
	var marketType, status, currency string
	if len(q.Types) > 0 {
		marketType = q.Types[0]
	}
	if len(q.Statuses) > 0 {
		status = q.Statuses[0]
	}
	if len(q.Currencies) > 0 {
		currency = q.Currencies[0]
	}

	// Handle IsActive parameter - if nil, pass false (SQL will ignore it)
	isActiveValue := false
	if q.IsActive != nil {
		isActiveValue = *q.IsActive
	}

	params := sqlc.FilterMarketsQueryParams{
		Column1: marketType,
		Column2: status,
		Column3: currency,
		Column4: isActiveValue,
		Column5: q.CountryCode,
		Column6: q.OrderBy,
	}

	sqlcMarkets, err := r.queries.FilterMarketsQuery(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to filter markets: %w", err)
	}

	markets := make([]*querymarket.Market, len(sqlcMarkets))
	for i, sqlcMarket := range sqlcMarkets {
		market, err := FromSQLCModelToQueryModel(sqlcMarket)
		if err != nil {
			return nil, fmt.Errorf("failed to convert market model: %w", err)
		}
		markets[i] = market
	}

	return markets, nil
}

// CountMarkets returns the total number of markets
func (r *marketQueryRepository) CountMarkets(ctx context.Context) (int64, error) {
	count, err := r.queries.CountMarketsQuery(ctx)

	if err != nil {
		return 0, fmt.Errorf("failed to count markets: %w", err)
	}

	return count, nil
}
