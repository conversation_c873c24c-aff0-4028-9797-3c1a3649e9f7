package database

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
)

type PostgresManager struct {
	pool *pgxpool.Pool
}

func NewPostgresManager(ctx context.Context, databaseURL string) (*PostgresManager, error) {
	config, err := pgxpool.ParseConfig(databaseURL)
	if err != nil {
		return nil, fmt.Errorf("failed to parse database URL: %w", err)
	}

	// Cấu hình connection pool
	config.MaxConns = 30
	config.MinConns = 5
	config.MaxConnLifetime = time.Hour
	config.MaxConnIdleTime = time.Minute * 30
	config.HealthCheckPeriod = time.Minute * 5

	pool, err := pgxpool.NewWithConfig(ctx, config)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection pool: %w", err)
	}

	return &PostgresManager{pool: pool}, nil
}

func (pm *PostgresManager) GetDB() DBTX {
	return pm.pool
}

func (pm *PostgresManager) Close() error {
	pm.pool.Close()
	return nil
}

func (pm *PostgresManager) Ping(ctx context.Context) error {
	return pm.pool.Ping(ctx)
}

func (pm *PostgresManager) Health(ctx context.Context) error {
	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	return pm.pool.Ping(ctx)
}

func (pm *PostgresManager) DriverName() string {
	return "postgres"
}

func (pm *PostgresManager) Stats() interface{} {
	return pm.pool.Stat()
}
