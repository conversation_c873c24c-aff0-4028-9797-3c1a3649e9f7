package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateRequestCrawlParams struct {
	ID                     int
	PerRequestDelaySeconds int
	TimeoutSeconds         int
	MaxRetries             int
	RetryDelaySeconds      int
	MaxConcurrent          int
	RequestsPerMinute      int
	UpdatedBy              string
}

type UpdateRequestCrawlHandler decorator.CommandHandler[UpdateRequestCrawlParams]

type updateRequestCrawlHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewUpdateRequestCrawlHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateRequestCrawlHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateRequestCrawlParams](
		updateRequestCrawlHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateRequestCrawlHandler) Handle(ctx context.Context, cmd UpdateRequestCrawlParams) error {
	return h.repo.Update(ctx, cmd.ID, func(c *entity.CrawlConfig) (*entity.CrawlConfig, error) {
		// Update config information
		err := c.UpdateRequests(entity.UpdateRequestParams{
			RequestsPerMinute: cmd.RequestsPerMinute,
			PerRequestDelay:   cmd.PerRequestDelaySeconds,
			MaxRetries:        cmd.MaxRetries,
			RetryDelay:        cmd.RetryDelaySeconds,
			MaxConcurrent:     cmd.MaxConcurrent,
			Timeout:           cmd.TimeoutSeconds,
		})
		if err != nil {
			return nil, err
		}
		return c, nil
	})
}
