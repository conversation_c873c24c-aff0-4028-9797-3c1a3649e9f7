package commandproxy

import (
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ProxyCommands struct {
	CreateProxy        CreateProxyHandler
	CreateProxyBatch   CreateProxyBatchHandler
	UpdateProxy        UpdateProxyHandler
	DeleteProxy        DeleteProxyHandler
	ActivateProxy      ActivateProxyHandler
	DeactivateProxy    DeactivateProxyHandler
	MarkProxyUsed      MarkProxyUsedHandler
	MarkProxiesUsed    MarkProxiesUsedHandler
	ReleaseProxy       ReleaseProxyHandler
	ReleaseManyProxies ReleaseManyProxiesHandler
}

func NewProxyCommands(repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) *ProxyCommands {
	return &ProxyCommands{
		CreateProxy:        NewCreateProxyHandler(repo, logger, metricsClient, broker),
		CreateProxyBatch:   NewCreateProxyBatchHandler(repo, logger, metricsClient, broker),
		UpdateProxy:        NewUpdateProxyHandler(repo, logger, metricsClient, broker),
		DeleteProxy:        NewDeleteProxyHandler(repo, logger, metricsClient, broker),
		ActivateProxy:      NewActivateProxyHandler(repo, logger, metricsClient, broker),
		DeactivateProxy:    NewDeactivateProxyHandler(repo, logger, metricsClient, broker),
		MarkProxyUsed:      NewMarkProxyUsedHandler(repo, logger, metricsClient, broker),
		ReleaseProxy:       NewReleaseProxyHandler(repo, logger, metricsClient, broker),
		ReleaseManyProxies: NewReleaseManyProxiesHandler(repo, logger, metricsClient, broker),
	}
}
