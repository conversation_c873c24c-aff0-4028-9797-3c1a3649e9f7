package querymarket

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/pagination"
)

type GetAllMarketsQuery struct {
	Page     int
	PageSize int
	OrderBy  string
}

type GetAllMarketsQueryHandler decorator.QueryHandler[GetAllMarketsQuery, *pagination.PaginatedResponse[Market]]

type getAllMarketsQueryHandler struct {
	repo MarketReadModel
}

func NewGetAllMarketsQueryHandler(
	repo MarketReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAllMarketsQueryHandler {

	return decorator.ApplyQueryDecorators[GetAllMarketsQuery, *pagination.PaginatedResponse[Market]](
		getAllMarketsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAllMarketsQueryHandler) Handle(ctx context.Context, query GetAllMarketsQuery) (*pagination.PaginatedResponse[Market], error) {
	p := pagination.NewPagination(query.Page, query.PageSize)
	total, err := h.repo.CountMarkets(ctx)
	if err != nil {
		return nil, err
	}
	p.SetTotal(total)

	markets, err := h.repo.GetAllMarkets(ctx, query)
	if err != nil {
		return nil, err
	}
	return pagination.CreateResponse[Market](p, markets), nil
}
