package event

import (
	"time"
)

// ====== Event Types Constants ======
const (
	AuthCreatedEventType     = "auth_crawl.created"
	AuthUsedEventType        = "auth_crawl.used"
	AuthReleasedEventType    = "auth_crawl.released"
	AuthDeactivatedEventType = "auth_crawl.deactivated"
	AuthActivatedEventType   = "auth_crawl.activated"
	AuthExpiredEventType     = "auth_crawl.expired"
)

// ====== Auth Created Event ======
type AuthCreatedData struct {
	AuthID    int        `json:"auth_id"`
	MarketID  int        `json:"market_id"`
	Type      string     `json:"type"`
	Value     string     `json:"value"`
	IsActive  bool       `json:"is_active"`
	ExpiredAt *time.Time `json:"expired_at,omitempty"`
	CreatedAt time.Time  `json:"created_at"`
}

type AuthCreatedEvent struct {
	*BaseEvent[AuthCreatedData]
}

func NewAuthCreatedEvent(aggregateID string, data AuthCreatedData) *AuthCreatedEvent {
	return &AuthCreatedEvent{
		BaseEvent: NewBaseEvent(AuthCreatedEventType, aggregateID, data),
	}
}

// ====== Auth Used Event ======
type AuthUsedData struct {
	AuthID    int       `json:"auth_id"`
	MarketID  int       `json:"market_id"`
	UsedAt    time.Time `json:"used_at"`
	UsedBy    string    `json:"used_by,omitempty"`    // Optional: service/process that used it
	Purpose   string    `json:"purpose,omitempty"`    // Optional: what it was used for
	RequestID string    `json:"request_id,omitempty"` // Optional: trace request
}

type AuthUsedEvent struct {
	*BaseEvent[AuthUsedData]
}

func NewAuthUsedEvent(aggregateID string, data AuthUsedData) *AuthUsedEvent {
	return &AuthUsedEvent{
		BaseEvent: NewBaseEvent(AuthUsedEventType, aggregateID, data),
	}
}

// ====== Auth Released Event ======
type AuthReleasedData struct {
	AuthID     int       `json:"auth_id"`
	MarketID   int       `json:"market_id"`
	ReleasedAt time.Time `json:"released_at"`
	ReleasedBy string    `json:"released_by,omitempty"` // Optional: service/process that released it
	Duration   int64     `json:"duration,omitempty"`    // Optional: how long it was used (in seconds)
	Success    bool      `json:"success"`               // Whether the usage was successful
	ErrorMsg   string    `json:"error_msg,omitempty"`   // Optional: error message if failed
}

type AuthReleasedEvent struct {
	*BaseEvent[AuthReleasedData]
}

func NewAuthReleasedEvent(aggregateID string, data AuthReleasedData) *AuthReleasedEvent {
	return &AuthReleasedEvent{
		BaseEvent: NewBaseEvent(AuthReleasedEventType, aggregateID, data),
	}
}

// ====== Auth Deactivated Event ======
type AuthDeactivatedData struct {
	AuthID        int       `json:"auth_id"`
	MarketID      int       `json:"market_id"`
	DeactivatedAt time.Time `json:"deactivated_at"`
	DeactivatedBy string    `json:"deactivated_by,omitempty"` // Optional: who/what deactivated it
	Reason        string    `json:"reason,omitempty"`         // Optional: reason for deactivation
}

type AuthDeactivatedEvent struct {
	*BaseEvent[AuthDeactivatedData]
}

func NewAuthDeactivatedEvent(aggregateID string, data AuthDeactivatedData) *AuthDeactivatedEvent {
	return &AuthDeactivatedEvent{
		BaseEvent: NewBaseEvent(AuthDeactivatedEventType, aggregateID, data),
	}
}

// ====== Auth Activated Event ======
type AuthActivatedData struct {
	AuthID      int       `json:"auth_id"`
	MarketID    int       `json:"market_id"`
	ActivatedAt time.Time `json:"activated_at"`
	ActivatedBy string    `json:"activated_by,omitempty"` // Optional: who/what activated it
	Reason      string    `json:"reason,omitempty"`       // Optional: reason for activation
}

type AuthActivatedEvent struct {
	*BaseEvent[AuthActivatedData]
}

func NewAuthActivatedEvent(aggregateID string, data AuthActivatedData) *AuthActivatedEvent {
	return &AuthActivatedEvent{
		BaseEvent: NewBaseEvent(AuthActivatedEventType, aggregateID, data),
	}
}

// ====== Auth Expired Event ======
type AuthExpiredData struct {
	AuthID       int       `json:"auth_id"`
	MarketID     int       `json:"market_id"`
	ExpiredAt    time.Time `json:"expired_at"`
	DetectedAt   time.Time `json:"detected_at"`   // When the expiration was detected
	AutoDetected bool      `json:"auto_detected"` // Whether it was auto-detected or manually checked
}

type AuthExpiredEvent struct {
	*BaseEvent[AuthExpiredData]
}

func NewAuthExpiredEvent(aggregateID string, data AuthExpiredData) *AuthExpiredEvent {
	return &AuthExpiredEvent{
		BaseEvent: NewBaseEvent(AuthExpiredEventType, aggregateID, data),
	}
}
