package queryjob

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/pagination"
)

type GetAllJobsQuery struct {
	Page     int
	PageSize int
	OrderBy  string
}

type GetAllJobsQueryHandler decorator.QueryHandler[GetAllJobsQuery, *pagination.PaginatedResponse[Job]]

type getAllJobsQueryHandler struct {
	repo JobReadModel
}

func NewGetAllJobsQueryHandler(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAllJobsQueryHandler {

	return decorator.ApplyQueryDecorators[GetAllJobsQuery, *pagination.PaginatedResponse[Job]](
		getAllJobsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAllJobsQueryHandler) Handle(ctx context.Context, query GetAllJobsQuery) (*pagination.PaginatedResponse[Job], error) {
	p := pagination.NewPagination(query.Page, query.PageSize)
	total, err := h.repo.CountJobs(ctx)
	if err != nil {
		return nil, err
	}
	p.SetTotal(total)
	
	jobs, err := h.repo.GetAllJobs(ctx, query)
	if err != nil {
		return nil, err
	}
	
	return pagination.CreateResponse[Job](p, jobs), nil
}
