package querycrawlconfig

import (
	"time"
)

// CrawlConfig represents a crawl config in the query model
type CrawlConfig struct {
	Id                     int        `json:"id"`
	MarketId               int        `json:"market_id"`
	Description            string     `json:"description"`
	TypeConfig             string     `json:"type_config"`
	RequestsPerMinute      int        `json:"requests_per_minute"`
	RequireAuth            bool       `json:"require_auth"`
	MaxNumberAuth          int        `json:"max_number_auth"`
	MaxNumberProxy         int        `json:"max_number_proxy"`
	PerRequestDelaySeconds int        `json:"per_request_delay_seconds"`
	TimeoutSeconds         int        `json:"timeout_seconds"`
	MaxRetries             int        `json:"max_retries"`
	RetryDelaySeconds      int        `json:"retry_delay_seconds"`
	MaxConcurrent          int        `json:"max_concurrent"`
	IsActive               bool       `json:"is_active"`
	LastUsedAt             *time.Time `json:"last_used_at,omitempty"`
	CreatedAt              time.Time  `json:"created_at"`
	UpdatedAt              time.Time  `json:"updated_at"`
}
