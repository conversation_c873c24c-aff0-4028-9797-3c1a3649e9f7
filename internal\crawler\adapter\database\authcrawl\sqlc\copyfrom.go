// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateMany implements pgx.CopyFromSource.
type iteratorForCreateMany struct {
	rows                 []CreateManyParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateMany) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateMany) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].MarketID,
		r.rows[0].AuthType,
		r.rows[0].Value,
		r.rows[0].IsActive,
		r.rows[0].IsUse,
		r.rows[0].ExpiredAt,
	}, nil
}

func (r iteratorForCreateMany) Err() error {
	return nil
}

func (q *Queries) CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"auth_crawl"}, []string{"market_id", "auth_type", "value", "is_active", "is_use", "expired_at"}, &iteratorForCreateMany{rows: arg})
}
