-- =====================================================
-- CREATE ALL TABLES WITH RELATIONSHIPS
-- Go Core Market Database Schema
-- =====================================================

-- Drop existing tables in reverse dependency order
DROP TABLE IF EXISTS jobs CASCADE;
DROP TABLE IF EXISTS auth_crawl CASCADE;
DROP TABLE IF EXISTS crawl_configs CASCADE;
DROP TABLE IF EXISTS proxies CASCADE;
DROP TABLE IF EXISTS markets CASCADE;

-- Drop functions if they exist
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS update_auth_crawl_updated_at() CASCADE;

-- =====================================================
-- UTILITY FUNCTIONS
-- =====================================================

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Function for auth_crawl table
CREATE OR REPLACE FUNCTION update_auth_crawl_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- =====================================================
-- MAIN TABLES
-- =====================================================

-- 1. MARKETS TABLE (Main reference table)
CREATE TABLE markets (
    id serial PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    market_type VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    base_url varchar(200) NOT NULL,
    currency VARCHAR(10) NOT NULL,
    buyer_fee_percent DECIMAL(3,2) NOT NULL DEFAULT 0.00,
    seller_fee_percent DECIMAL(3,2) NOT NULL DEFAULT 0.00,
    country_code VARCHAR(3) NOT NULL,
    language VARCHAR(10) NOT NULL,
    description TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_crawl_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 2. PROXIES TABLE (Independent table)
CREATE TABLE proxies (
    id SERIAL PRIMARY KEY,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL CHECK (port > 0 AND port <= 65535),
    user_name VARCHAR(255) NOT NULL DEFAULT '',
    password VARCHAR(255) NOT NULL DEFAULT '',
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_use BOOLEAN NOT NULL DEFAULT false,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- 3. AUTH_CRAWL TABLE (References markets)
CREATE TABLE auth_crawl (
    id SERIAL PRIMARY KEY,
    market_id INTEGER NOT NULL,
    auth_type VARCHAR(50) NOT NULL CHECK (auth_type IN ('api_key', 'cookie')),
    value TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_use BOOLEAN NOT NULL DEFAULT false,
    last_used_at TIMESTAMPTZ,
    expired_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Foreign key constraint
    CONSTRAINT fk_auth_crawl_market_id FOREIGN KEY (market_id) REFERENCES markets(id) ON DELETE CASCADE
);

-- 4. CRAWL_CONFIGS TABLE (References markets)
CREATE TABLE crawl_configs (
    id serial PRIMARY KEY,
    market_id INTEGER NOT NULL,
    description TEXT NOT NULL,
    type_config VARCHAR(50) NOT NULL,
    requests_per_minute INTEGER NOT NULL,
    require_auth BOOLEAN NOT NULL DEFAULT false,
    max_number_auth INTEGER NOT NULL DEFAULT 0,
    max_number_proxy INTEGER NOT NULL DEFAULT 0,
    per_request_delay_seconds INTEGER NOT NULL DEFAULT 1,
    timeout_seconds INTEGER NOT NULL DEFAULT 30,
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_delay_seconds INTEGER NOT NULL DEFAULT 5,
    max_concurrent INTEGER NOT NULL DEFAULT 1,
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT crawl_configs_type_config_check CHECK (type_config IN ('normal', 'doppler', 'float', 'fade', 'blue_gem', 'item')),
    CONSTRAINT crawl_configs_requests_per_minute_check CHECK (requests_per_minute > 0),
    CONSTRAINT crawl_configs_max_number_auth_check CHECK (max_number_auth >= 0),
    CONSTRAINT crawl_configs_max_number_proxy_check CHECK (max_number_proxy >= 0),
    CONSTRAINT crawl_configs_per_request_delay_check CHECK (per_request_delay_seconds >= 0),
    CONSTRAINT crawl_configs_timeout_check CHECK (timeout_seconds > 0),
    CONSTRAINT crawl_configs_max_retries_check CHECK (max_retries >= 0),
    CONSTRAINT crawl_configs_retry_delay_check CHECK (retry_delay_seconds >= 0),
    CONSTRAINT crawl_configs_max_concurrent_check CHECK (max_concurrent > 0),
    
    -- Foreign key constraint
    CONSTRAINT fk_crawl_configs_market_id FOREIGN KEY (market_id) REFERENCES markets(id) ON DELETE CASCADE
);

-- 5. JOBS TABLE (References crawl_configs)
CREATE TABLE jobs (
    id serial PRIMARY KEY,
    job_type VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    crawl_config_id INTEGER,
    scheduled_at TIMESTAMPTZ,
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Progress tracking
    current_step INTEGER DEFAULT 0,
    total_steps INTEGER DEFAULT 0,
    progress_message TEXT,
    progress_percentage INTEGER DEFAULT 0,
    
    -- Error handling
    max_retries INTEGER NOT NULL DEFAULT 3,
    retry_count INTEGER NOT NULL DEFAULT 0,
    last_error TEXT,
    timeout_seconds INTEGER NOT NULL DEFAULT 3600,
    
    -- Metadata
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT jobs_job_type_check CHECK (job_type IN ('crawl', 'analysis', 'cleanup', 'export')),
    CONSTRAINT jobs_status_check CHECK (status IN ('pending', 'scheduled', 'running', 'paused', 'completed', 'failed', 'cancelled')),
    CONSTRAINT jobs_progress_percentage_check CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    CONSTRAINT jobs_max_retries_check CHECK (max_retries >= 0),
    CONSTRAINT jobs_retry_count_check CHECK (retry_count >= 0),
    CONSTRAINT jobs_timeout_check CHECK (timeout_seconds > 0),
    
    -- Foreign key constraint
    CONSTRAINT fk_jobs_crawl_config_id FOREIGN KEY (crawl_config_id) REFERENCES crawl_configs(id) ON DELETE SET NULL
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- Markets indexes
CREATE INDEX idx_markets_name ON markets(name);
CREATE INDEX idx_markets_is_active ON markets(is_active);
CREATE INDEX idx_markets_status ON markets(status);

-- Proxies indexes
CREATE INDEX idx_proxies_host_port ON proxies(host, port);
CREATE INDEX idx_proxies_is_active ON proxies(is_active);
CREATE INDEX idx_proxies_is_use ON proxies(is_use);
CREATE INDEX idx_proxies_available ON proxies(is_active, is_use) WHERE is_active = true AND is_use = false;

-- Auth_crawl indexes
CREATE INDEX idx_auth_crawl_market_id ON auth_crawl(market_id);
CREATE INDEX idx_auth_crawl_is_active ON auth_crawl(is_active);
CREATE INDEX idx_auth_crawl_is_use ON auth_crawl(is_use);
CREATE INDEX idx_auth_crawl_auth_type ON auth_crawl(auth_type);

-- Crawl_configs indexes
CREATE INDEX idx_crawl_configs_market_id ON crawl_configs(market_id);
CREATE INDEX idx_crawl_configs_type_config ON crawl_configs(type_config);
CREATE INDEX idx_crawl_configs_is_active ON crawl_configs(is_active);
CREATE INDEX idx_crawl_configs_last_used_at ON crawl_configs(last_used_at);
CREATE INDEX idx_crawl_configs_created_at ON crawl_configs(created_at);
CREATE INDEX idx_crawl_configs_market_type_active ON crawl_configs(market_id, type_config, is_active);

-- Jobs indexes
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_job_type ON jobs(job_type);
CREATE INDEX idx_jobs_scheduled_at ON jobs(scheduled_at);
CREATE INDEX idx_jobs_created_at ON jobs(created_at);
CREATE INDEX idx_jobs_crawl_config_id ON jobs(crawl_config_id);

-- =====================================================
-- TRIGGERS
-- =====================================================

-- Markets trigger
CREATE TRIGGER update_markets_updated_at
    BEFORE UPDATE ON markets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Proxies trigger
CREATE TRIGGER update_proxies_updated_at
    BEFORE UPDATE ON proxies
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Auth_crawl trigger
CREATE TRIGGER update_auth_crawl_updated_at_trigger
    BEFORE UPDATE ON auth_crawl
    FOR EACH ROW
    EXECUTE FUNCTION update_auth_crawl_updated_at();

-- Crawl_configs trigger
CREATE TRIGGER update_crawl_configs_updated_at 
    BEFORE UPDATE ON crawl_configs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Jobs trigger
CREATE TRIGGER update_jobs_updated_at 
    BEFORE UPDATE ON jobs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE markets IS 'Main table storing market information and configuration';
COMMENT ON TABLE proxies IS 'Table storing proxy server configurations for crawling';
COMMENT ON TABLE auth_crawl IS 'Table storing authentication credentials for market crawling';
COMMENT ON TABLE crawl_configs IS 'Table storing crawl configuration parameters for each market';
COMMENT ON TABLE jobs IS 'Table storing job information including status and progress tracking';

-- =====================================================
-- END OF SCHEMA
-- =====================================================
