package queryproxy

import (
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type ProxyQueries struct {
	GetProxyByID        GetProxyByIDHandler
	GetAllProxies       GetAllProxiesHandler
	GetAvailableProxies GetAvailableProxiesQueryHandler
}

func NewProxyQueries(
	repo ProxyReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) *ProxyQueries {
	return &ProxyQueries{
		GetProxyByID:        NewGetProxyByIDHandler(repo, logger, metricsClient),
		GetAllProxies:       NewGetAllProxiesHandler(repo, logger, metricsClient),
		GetAvailableProxies: NewGetAvailableProxiesQueryHandler(repo, logger, metricsClient),
	}
}
