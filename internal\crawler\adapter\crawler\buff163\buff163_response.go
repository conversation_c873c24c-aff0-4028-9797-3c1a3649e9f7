package buff163

type BuffItemResponse[T any] struct {
	Code    string           `json:"code"`
	Message *string          `json:"msg"`
	Data    *BuffItemPage[T] `json:"data"`
	Error   *string          `json:"error"`
}

type BuffItemPage[T any] struct {
	PageNum    int `json:"page_num"`
	PageSize   int `json:"page_size"`
	TotalPage  int `json:"total_page"`
	TotalCount int `json:"total_count"`
	Items      []T `json:"items"`
}
