// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: api/protobuf/market.proto

package market

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MarketService_CreateMarket_FullMethodName         = "/market.v1.MarketService/CreateMarket"
	MarketService_UpdateMarket_FullMethodName         = "/market.v1.MarketService/UpdateMarket"
	MarketService_DeleteMarket_FullMethodName         = "/market.v1.MarketService/DeleteMarket"
	MarketService_ActiveMarket_FullMethodName         = "/market.v1.MarketService/ActiveMarket"
	MarketService_DeactiveMarket_FullMethodName       = "/market.v1.MarketService/DeactiveMarket"
	MarketService_SetMarketMaintenance_FullMethodName = "/market.v1.MarketService/SetMarketMaintenance"
	MarketService_GetAllMarkets_FullMethodName        = "/market.v1.MarketService/GetAllMarkets"
	MarketService_GetMarketById_FullMethodName        = "/market.v1.MarketService/GetMarketById"
	MarketService_GetActiveMarkets_FullMethodName     = "/market.v1.MarketService/GetActiveMarkets"
	MarketService_FilterMarkets_FullMethodName        = "/market.v1.MarketService/FilterMarkets"
)

// MarketServiceClient is the client API for MarketService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC Service
type MarketServiceClient interface {
	// Commands
	CreateMarket(ctx context.Context, in *CreateMarketRequest, opts ...grpc.CallOption) (*CreateMarketResponse, error)
	UpdateMarket(ctx context.Context, in *UpdateMarketRequest, opts ...grpc.CallOption) (*UpdateMarketResponse, error)
	DeleteMarket(ctx context.Context, in *DeleteMarketRequest, opts ...grpc.CallOption) (*DeleteMarketResponse, error)
	ActiveMarket(ctx context.Context, in *ActiveMarketRequest, opts ...grpc.CallOption) (*ActiveMarketResponse, error)
	DeactiveMarket(ctx context.Context, in *DeactiveMarketRequest, opts ...grpc.CallOption) (*DeactiveMarketResponse, error)
	SetMarketMaintenance(ctx context.Context, in *SetMarketMaintenanceRequest, opts ...grpc.CallOption) (*SetMarketMaintenanceResponse, error)
	// Queries
	GetAllMarkets(ctx context.Context, in *GetAllMarketsRequest, opts ...grpc.CallOption) (*GetAllMarketsResponse, error)
	GetMarketById(ctx context.Context, in *GetMarketByIdRequest, opts ...grpc.CallOption) (*GetMarketByIdResponse, error)
	GetActiveMarkets(ctx context.Context, in *GetActiveMarketsRequest, opts ...grpc.CallOption) (*GetActiveMarketsResponse, error)
	FilterMarkets(ctx context.Context, in *FilterMarketsRequest, opts ...grpc.CallOption) (*FilterMarketsResponse, error)
}

type marketServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMarketServiceClient(cc grpc.ClientConnInterface) MarketServiceClient {
	return &marketServiceClient{cc}
}

func (c *marketServiceClient) CreateMarket(ctx context.Context, in *CreateMarketRequest, opts ...grpc.CallOption) (*CreateMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateMarketResponse)
	err := c.cc.Invoke(ctx, MarketService_CreateMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) UpdateMarket(ctx context.Context, in *UpdateMarketRequest, opts ...grpc.CallOption) (*UpdateMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateMarketResponse)
	err := c.cc.Invoke(ctx, MarketService_UpdateMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) DeleteMarket(ctx context.Context, in *DeleteMarketRequest, opts ...grpc.CallOption) (*DeleteMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteMarketResponse)
	err := c.cc.Invoke(ctx, MarketService_DeleteMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) ActiveMarket(ctx context.Context, in *ActiveMarketRequest, opts ...grpc.CallOption) (*ActiveMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActiveMarketResponse)
	err := c.cc.Invoke(ctx, MarketService_ActiveMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) DeactiveMarket(ctx context.Context, in *DeactiveMarketRequest, opts ...grpc.CallOption) (*DeactiveMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeactiveMarketResponse)
	err := c.cc.Invoke(ctx, MarketService_DeactiveMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) SetMarketMaintenance(ctx context.Context, in *SetMarketMaintenanceRequest, opts ...grpc.CallOption) (*SetMarketMaintenanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SetMarketMaintenanceResponse)
	err := c.cc.Invoke(ctx, MarketService_SetMarketMaintenance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetAllMarkets(ctx context.Context, in *GetAllMarketsRequest, opts ...grpc.CallOption) (*GetAllMarketsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllMarketsResponse)
	err := c.cc.Invoke(ctx, MarketService_GetAllMarkets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetMarketById(ctx context.Context, in *GetMarketByIdRequest, opts ...grpc.CallOption) (*GetMarketByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetMarketByIdResponse)
	err := c.cc.Invoke(ctx, MarketService_GetMarketById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) GetActiveMarkets(ctx context.Context, in *GetActiveMarketsRequest, opts ...grpc.CallOption) (*GetActiveMarketsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveMarketsResponse)
	err := c.cc.Invoke(ctx, MarketService_GetActiveMarkets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *marketServiceClient) FilterMarkets(ctx context.Context, in *FilterMarketsRequest, opts ...grpc.CallOption) (*FilterMarketsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterMarketsResponse)
	err := c.cc.Invoke(ctx, MarketService_FilterMarkets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MarketServiceServer is the server API for MarketService service.
// All implementations must embed UnimplementedMarketServiceServer
// for forward compatibility.
//
// gRPC Service
type MarketServiceServer interface {
	// Commands
	CreateMarket(context.Context, *CreateMarketRequest) (*CreateMarketResponse, error)
	UpdateMarket(context.Context, *UpdateMarketRequest) (*UpdateMarketResponse, error)
	DeleteMarket(context.Context, *DeleteMarketRequest) (*DeleteMarketResponse, error)
	ActiveMarket(context.Context, *ActiveMarketRequest) (*ActiveMarketResponse, error)
	DeactiveMarket(context.Context, *DeactiveMarketRequest) (*DeactiveMarketResponse, error)
	SetMarketMaintenance(context.Context, *SetMarketMaintenanceRequest) (*SetMarketMaintenanceResponse, error)
	// Queries
	GetAllMarkets(context.Context, *GetAllMarketsRequest) (*GetAllMarketsResponse, error)
	GetMarketById(context.Context, *GetMarketByIdRequest) (*GetMarketByIdResponse, error)
	GetActiveMarkets(context.Context, *GetActiveMarketsRequest) (*GetActiveMarketsResponse, error)
	FilterMarkets(context.Context, *FilterMarketsRequest) (*FilterMarketsResponse, error)
	mustEmbedUnimplementedMarketServiceServer()
}

// UnimplementedMarketServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMarketServiceServer struct{}

func (UnimplementedMarketServiceServer) CreateMarket(context.Context, *CreateMarketRequest) (*CreateMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMarket not implemented")
}
func (UnimplementedMarketServiceServer) UpdateMarket(context.Context, *UpdateMarketRequest) (*UpdateMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateMarket not implemented")
}
func (UnimplementedMarketServiceServer) DeleteMarket(context.Context, *DeleteMarketRequest) (*DeleteMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMarket not implemented")
}
func (UnimplementedMarketServiceServer) ActiveMarket(context.Context, *ActiveMarketRequest) (*ActiveMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActiveMarket not implemented")
}
func (UnimplementedMarketServiceServer) DeactiveMarket(context.Context, *DeactiveMarketRequest) (*DeactiveMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactiveMarket not implemented")
}
func (UnimplementedMarketServiceServer) SetMarketMaintenance(context.Context, *SetMarketMaintenanceRequest) (*SetMarketMaintenanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SetMarketMaintenance not implemented")
}
func (UnimplementedMarketServiceServer) GetAllMarkets(context.Context, *GetAllMarketsRequest) (*GetAllMarketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllMarkets not implemented")
}
func (UnimplementedMarketServiceServer) GetMarketById(context.Context, *GetMarketByIdRequest) (*GetMarketByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMarketById not implemented")
}
func (UnimplementedMarketServiceServer) GetActiveMarkets(context.Context, *GetActiveMarketsRequest) (*GetActiveMarketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveMarkets not implemented")
}
func (UnimplementedMarketServiceServer) FilterMarkets(context.Context, *FilterMarketsRequest) (*FilterMarketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterMarkets not implemented")
}
func (UnimplementedMarketServiceServer) mustEmbedUnimplementedMarketServiceServer() {}
func (UnimplementedMarketServiceServer) testEmbeddedByValue()                       {}

// UnsafeMarketServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MarketServiceServer will
// result in compilation errors.
type UnsafeMarketServiceServer interface {
	mustEmbedUnimplementedMarketServiceServer()
}

func RegisterMarketServiceServer(s grpc.ServiceRegistrar, srv MarketServiceServer) {
	// If the following call pancis, it indicates UnimplementedMarketServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MarketService_ServiceDesc, srv)
}

func _MarketService_CreateMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).CreateMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_CreateMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).CreateMarket(ctx, req.(*CreateMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_UpdateMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).UpdateMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_UpdateMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).UpdateMarket(ctx, req.(*UpdateMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_DeleteMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).DeleteMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_DeleteMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).DeleteMarket(ctx, req.(*DeleteMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_ActiveMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActiveMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).ActiveMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_ActiveMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).ActiveMarket(ctx, req.(*ActiveMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_DeactiveMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactiveMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).DeactiveMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_DeactiveMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).DeactiveMarket(ctx, req.(*DeactiveMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_SetMarketMaintenance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SetMarketMaintenanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).SetMarketMaintenance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_SetMarketMaintenance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).SetMarketMaintenance(ctx, req.(*SetMarketMaintenanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetAllMarkets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllMarketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetAllMarkets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetAllMarkets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetAllMarkets(ctx, req.(*GetAllMarketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetMarketById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMarketByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetMarketById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetMarketById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetMarketById(ctx, req.(*GetMarketByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_GetActiveMarkets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveMarketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).GetActiveMarkets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_GetActiveMarkets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).GetActiveMarkets(ctx, req.(*GetActiveMarketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MarketService_FilterMarkets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterMarketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MarketServiceServer).FilterMarkets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MarketService_FilterMarkets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MarketServiceServer).FilterMarkets(ctx, req.(*FilterMarketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MarketService_ServiceDesc is the grpc.ServiceDesc for MarketService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MarketService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "market.v1.MarketService",
	HandlerType: (*MarketServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMarket",
			Handler:    _MarketService_CreateMarket_Handler,
		},
		{
			MethodName: "UpdateMarket",
			Handler:    _MarketService_UpdateMarket_Handler,
		},
		{
			MethodName: "DeleteMarket",
			Handler:    _MarketService_DeleteMarket_Handler,
		},
		{
			MethodName: "ActiveMarket",
			Handler:    _MarketService_ActiveMarket_Handler,
		},
		{
			MethodName: "DeactiveMarket",
			Handler:    _MarketService_DeactiveMarket_Handler,
		},
		{
			MethodName: "SetMarketMaintenance",
			Handler:    _MarketService_SetMarketMaintenance_Handler,
		},
		{
			MethodName: "GetAllMarkets",
			Handler:    _MarketService_GetAllMarkets_Handler,
		},
		{
			MethodName: "GetMarketById",
			Handler:    _MarketService_GetMarketById_Handler,
		},
		{
			MethodName: "GetActiveMarkets",
			Handler:    _MarketService_GetActiveMarkets_Handler,
		},
		{
			MethodName: "FilterMarkets",
			Handler:    _MarketService_FilterMarkets_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protobuf/market.proto",
}
