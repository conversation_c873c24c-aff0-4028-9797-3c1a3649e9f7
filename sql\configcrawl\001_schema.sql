-- Create crawl_configs table for configcrawl domain
-- This table stores all crawl configuration information

CREATE TABLE crawl_configs (
                               id serial PRIMARY KEY,
                               market_id INTEGER NOT NULL,
                               description TEXT NOT NULL,
                               type_config VARCHAR(50) NOT NULL,
                               requests_per_minute INTEGER NOT NULL,
                               require_auth BOOLEAN NOT NULL DEFAULT false,
                               max_number_auth INTEGER NOT NULL DEFAULT 0,
                               max_number_proxy INTEGER NOT NULL DEFAULT 0,
                               per_request_delay_seconds INTEGER NOT NULL DEFAULT 1,
                               timeout_seconds INTEGER NOT NULL DEFAULT 30,
                               max_retries INTEGER NOT NULL DEFAULT 3,
                               retry_delay_seconds INTEGER NOT NULL DEFAULT 5,
                               max_concurrent INTEGER NOT NULL DEFAULT 1,
                               is_active BOOLEAN NOT NULL DEFAULT true,
                               last_used_at TIMESTAMPTZ,
                               created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                               updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()

);
