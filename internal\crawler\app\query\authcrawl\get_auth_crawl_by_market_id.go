package queryauthcral

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetAuthCrawlByMarketIdQuery struct {
	MarketId int
}

type GetAuthCrawlByMarketIdQueryHandler decorator.QueryHandler[GetAuthCrawlByMarketIdQuery, *AuthCrawl]

type getAuthCrawlByMarketIdQueryHandler struct {
	repo RepositoryQueryAuth
}

func NewGetAuthCrawlByMarketIdQueryHandler(
	repo RepositoryQueryAuth,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAuthCrawlByMarketIdQueryHandler {

	return decorator.ApplyQueryDecorators[GetAuthCrawlByMarketIdQuery, *AuthCrawl](
		getAuthCrawlByMarketIdQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAuthCrawlByMarketIdQueryHandler) Handle(ctx context.Context, query GetAuthCrawlByMarketIdQuery) (*AuthCrawl, error) {
	return h.repo.GetAuthCrawlByMarketId(ctx, query.MarketId)
}
