package querymarket

import "context"

type MarketReadModel interface {
	GetAllMarkets(ctx context.Context, query GetAllMarketsQuery) ([]*Market, error)
	GetMarketById(ctx context.Context, id int) (*Market, error)
	GetActiveMarkets(ctx context.Context) ([]*Market, error)
	FilterMarkets(ctx context.Context, query FilterMarketsQuery) ([]*Market, error)
	CountMarkets(ctx context.Context) (int64, error)
}
