package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeleteMarket struct {
	ID        int
	DeletedBy string
}

type DeleteMarketHandler decorator.CommandHandler[DeleteMarket]

type deleteMarketHandler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewDeleteMarketHandler(
	repo repository.RepositoryMarket,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeleteMarketHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeleteMarket](
		deleteMarketHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deleteMarketHandler) Handle(ctx context.Context, cmd DeleteMarket) error {
	return h.repo.Delete(ctx, cmd.ID)
}
