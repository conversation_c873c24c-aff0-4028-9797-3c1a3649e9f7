package queryproxy

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetAvailableProxiesQuery struct {
	Limit int
}

type GetAvailableProxiesQueryHandler decorator.QueryHandler[GetAvailableProxiesQuery, []*Proxy]

type getAvailableProxiesQueryHandler struct {
	repo ProxyReadModel
}

func NewGetAvailableProxiesQueryHandler(
	repo ProxyReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAvailableProxiesQueryHandler {

	return decorator.ApplyQueryDecorators[GetAvailableProxiesQuery, []*Proxy](
		getAvailableProxiesQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAvailableProxiesQueryHandler) Handle(ctx context.Context, query GetAvailableProxiesQuery) ([]*Proxy, error) {
	proxies, err := h.repo.GetAvailableProxies(ctx, query.Limit)
	if err != nil {
		return nil, err
	}
	return proxies, nil
}
