package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeleteCrawlConfig struct {
	ID        int
	DeletedBy string
}

type DeleteCrawlConfigHandler decorator.CommandHandler[DeleteCrawlConfig]

type deleteCrawlConfigHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewDeleteCrawlConfigHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeleteCrawlConfigHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeleteCrawlConfig](
		deleteCrawlConfigHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deleteCrawlConfigHandler) Handle(ctx context.Context, cmd DeleteCrawlConfig) error {
	return h.repo.Delete(ctx, cmd.ID)
}
