package event

import (
	"context"
	"time"
	"go_core_market/internal/crawler/domain/value_object"
)

// ExampleUsagePricesCrawledEvent demonstrates how to create and publish a PricesCrawledEvent
func ExampleUsagePricesCrawledEvent(
	ctx context.Context,
	marketID int,
	marketName string,
	crawlType string,
	domainPrices []*value_object.CrawledPrice,
	crawledBy string,
	requestID string,
	page int,
	duration time.Duration,
	success bool,
	errorMessage string,
) *PricesCrawledEvent {
	
	// Convert domain objects to DTOs
	pricesDTO := ConvertCrawledPricesToDTOs(domainPrices)
	
	// Create event data
	data := PricesCrawledData{
		MarketID:     marketID,
		MarketName:   marketName,
		CrawlType:    crawlType,
		Prices:       pricesDTO,
		TotalPrices:  len(pricesDTO),
		CrawledAt:    time.Now(),
		CrawledBy:    crawledBy,
		RequestID:    requestID,
		Page:         page,
		Duration:     duration,
		Success:      success,
		ErrorMessage: errorMessage,
	}
	
	// Create event
	aggregateID := "market-" + string(rune(marketID))
	event := NewPricesCrawledEvent(aggregateID, data)
	
	return event
}

// ExampleUsageItemsCrawledEvent demonstrates how to create and publish an ItemsCrawledEvent
func ExampleUsageItemsCrawledEvent(
	ctx context.Context,
	marketID int,
	marketName string,
	domainItems []*value_object.CrawledItem,
	crawledBy string,
	requestID string,
	page int,
	duration time.Duration,
	success bool,
	errorMessage string,
) *ItemsCrawledEvent {
	
	// Convert domain objects to DTOs
	itemsDTO := ConvertCrawledItemsToDTOs(domainItems)
	
	// Create event data
	data := ItemsCrawledData{
		MarketID:     marketID,
		MarketName:   marketName,
		Items:        itemsDTO,
		TotalItems:   len(itemsDTO),
		CrawledAt:    time.Now(),
		CrawledBy:    crawledBy,
		RequestID:    requestID,
		Page:         page,
		Duration:     duration,
		Success:      success,
		ErrorMessage: errorMessage,
	}
	
	// Create event
	aggregateID := "market-" + string(rune(marketID))
	event := NewItemsCrawledEvent(aggregateID, data)
	
	return event
}

// ExampleUsageNormalCrawl demonstrates usage for normal price crawling
func ExampleUsageNormalCrawl(
	ctx context.Context,
	marketID int,
	domainPrices []*value_object.CrawledPrice,
	page int,
	totalPages int,
) *PricesCrawledEvent {
	
	return ExampleUsagePricesCrawledEvent(
		ctx,
		marketID,
		"Buff163", // or get from config
		"normal",
		domainPrices,
		"crawler-service",
		"req-" + string(rune(time.Now().UnixNano())),
		page,
		time.Second*30, // example duration
		true,
		"",
	)
}

// ExampleUsageDopplerCrawl demonstrates usage for doppler price crawling
func ExampleUsageDopplerCrawl(
	ctx context.Context,
	marketID int,
	itemID int,
	itemName string,
	domainPrices []*value_object.CrawledPrice,
	conditions []value_object.ConditionDoppler,
) *PricesCrawledEvent {
	
	// Convert conditions to DTOs
	conditionsDTO := ConvertDopplerConditionsToDTOs(conditions)
	
	// Convert prices to DTOs
	pricesDTO := ConvertCrawledPricesToDTOs(domainPrices)
	
	// Create event data with specific fields for doppler crawl
	data := PricesCrawledData{
		MarketID:     marketID,
		MarketName:   "Buff163",
		CrawlType:    "doppler",
		ItemID:       itemID,
		ItemName:     itemName,
		Prices:       pricesDTO,
		TotalPrices:  len(pricesDTO),
		CrawledAt:    time.Now(),
		CrawledBy:    "crawler-service",
		RequestID:    "req-doppler-" + string(rune(time.Now().UnixNano())),
		Conditions:   conditionsDTO,
		Duration:     time.Second*45,
		Success:      true,
		ErrorMessage: "",
	}
	
	aggregateID := "market-" + string(rune(marketID))
	return NewPricesCrawledEvent(aggregateID, data)
}

// ExampleUsageFloatCrawl demonstrates usage for float price crawling
func ExampleUsageFloatCrawl(
	ctx context.Context,
	marketID int,
	itemID int,
	itemName string,
	domainPrices []*value_object.CrawledPrice,
	conditions []value_object.ConditionFloat,
) *PricesCrawledEvent {
	
	// Convert conditions to DTOs
	conditionsDTO := ConvertFloatConditionsToDTOs(conditions)
	
	// Convert prices to DTOs
	pricesDTO := ConvertCrawledPricesToDTOs(domainPrices)
	
	// Create event data
	data := PricesCrawledData{
		MarketID:     marketID,
		MarketName:   "Buff163",
		CrawlType:    "float",
		ItemID:       itemID,
		ItemName:     itemName,
		Prices:       pricesDTO,
		TotalPrices:  len(pricesDTO),
		CrawledAt:    time.Now(),
		CrawledBy:    "crawler-service",
		RequestID:    "req-float-" + string(rune(time.Now().UnixNano())),
		Conditions:   conditionsDTO,
		Duration:     time.Second*60,
		Success:      true,
		ErrorMessage: "",
	}
	
	aggregateID := "market-" + string(rune(marketID))
	return NewPricesCrawledEvent(aggregateID, data)
}

// ExampleUsageFailedCrawl demonstrates usage for failed crawl events
func ExampleUsageFailedCrawl(
	ctx context.Context,
	marketID int,
	crawlType string,
	errorMessage string,
	duration time.Duration,
) *PricesCrawledEvent {
	
	data := PricesCrawledData{
		MarketID:     marketID,
		MarketName:   "Buff163",
		CrawlType:    crawlType,
		Prices:       []CrawledPriceDTO{}, // empty for failed crawl
		TotalPrices:  0,
		CrawledAt:    time.Now(),
		CrawledBy:    "crawler-service",
		RequestID:    "req-failed-" + string(rune(time.Now().UnixNano())),
		Duration:     duration,
		Success:      false,
		ErrorMessage: errorMessage,
	}
	
	aggregateID := "market-" + string(rune(marketID))
	return NewPricesCrawledEvent(aggregateID, data)
}
