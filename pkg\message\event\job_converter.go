package event

import (
	"go_core_market/internal/crawler/domain/entity"
	"time"
)

// ConvertJobProgressToDTO converts domain JobProgress to DTO
func ConvertJobProgressToDTO(progress *entity.JobProgress) JobProgressDTO {
	if progress == nil {
		return JobProgressDTO{}
	}
	
	return JobProgressDTO{
		CurrentStep: progress.CurrentStep,
		TotalSteps:  progress.TotalSteps,
		Message:     progress.Message,
		Percentage:  progress.Percentage,
	}
}

// CreateJobCreatedEventFromDomain creates JobCreatedEvent from domain Job entity
func CreateJobCreatedEventFromDomain(job *entity.Job) *JobCreatedEvent {
	data := JobCreatedData{
		JobID:         job.ID(),
		JobType:       string(job.JobType()),
		Name:          job.Name(),
		MaxRetries:    job.MaxRetries(),
		Timeout:       int(job.Timeout().Seconds()),
		CreatedBy:     job.CreatedBy(),
		CreatedAt:     time.Now(),
		ScheduledAt:   job.ScheduledAt(),
	}
	
	if job.Description() != nil {
		data.Description = *job.Description()
	}
	
	if job.CrawlConfigId() != nil {
		data.CrawlConfigID = *job.CrawlConfigId()
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobCreatedEvent(aggregateID, data)
}

// CreateJobScheduledEventFromDomain creates JobScheduledEvent from domain Job entity
func CreateJobScheduledEventFromDomain(job *entity.Job, scheduledBy string) *JobScheduledEvent {
	data := JobScheduledData{
		JobID:       job.ID(),
		JobType:     string(job.JobType()),
		Name:        job.Name(),
		ScheduledAt: *job.ScheduledAt(),
		ScheduledBy: scheduledBy,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobScheduledEvent(aggregateID, data)
}

// CreateJobStartedEventFromDomain creates JobStartedEvent from domain Job entity
func CreateJobStartedEventFromDomain(job *entity.Job, startedBy string) *JobStartedEvent {
	data := JobStartedData{
		JobID:     job.ID(),
		JobType:   string(job.JobType()),
		Name:      job.Name(),
		StartedAt: *job.StartedAt(),
		StartedBy: startedBy,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobStartedEvent(aggregateID, data)
}

// CreateJobPausedEventFromDomain creates JobPausedEvent from domain Job entity
func CreateJobPausedEventFromDomain(job *entity.Job, pausedBy, reason string) *JobPausedEvent {
	data := JobPausedData{
		JobID:    job.ID(),
		JobType:  string(job.JobType()),
		Name:     job.Name(),
		PausedAt: time.Now(),
		PausedBy: pausedBy,
		Reason:   reason,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobPausedEvent(aggregateID, data)
}

// CreateJobResumedEventFromDomain creates JobResumedEvent from domain Job entity
func CreateJobResumedEventFromDomain(job *entity.Job, resumedBy string) *JobResumedEvent {
	data := JobResumedData{
		JobID:     job.ID(),
		JobType:   string(job.JobType()),
		Name:      job.Name(),
		ResumedAt: time.Now(),
		ResumedBy: resumedBy,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobResumedEvent(aggregateID, data)
}

// CreateJobCompletedEventFromDomain creates JobCompletedEvent from domain Job entity
func CreateJobCompletedEventFromDomain(job *entity.Job, results map[string]interface{}) *JobCompletedEvent {
	var duration time.Duration
	if job.StartedAt() != nil && job.CompletedAt() != nil {
		duration = job.CompletedAt().Sub(*job.StartedAt())
	}
	
	data := JobCompletedData{
		JobID:       job.ID(),
		JobType:     string(job.JobType()),
		Name:        job.Name(),
		CompletedAt: *job.CompletedAt(),
		Duration:    duration,
		Progress:    ConvertJobProgressToDTO(job.Progress()),
		Results:     results,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobCompletedEvent(aggregateID, data)
}

// CreateJobFailedEventFromDomain creates JobFailedEvent from domain Job entity
func CreateJobFailedEventFromDomain(job *entity.Job) *JobFailedEvent {
	errorMessage := ""
	if job.LastError() != nil {
		errorMessage = *job.LastError()
	}
	
	canRetry := job.RetryCount() < job.MaxRetries()
	
	data := JobFailedData{
		JobID:        job.ID(),
		JobType:      string(job.JobType()),
		Name:         job.Name(),
		FailedAt:     time.Now(),
		ErrorMessage: errorMessage,
		RetryCount:   job.RetryCount(),
		MaxRetries:   job.MaxRetries(),
		CanRetry:     canRetry,
		Progress:     ConvertJobProgressToDTO(job.Progress()),
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobFailedEvent(aggregateID, data)
}

// CreateJobCancelledEventFromDomain creates JobCancelledEvent from domain Job entity
func CreateJobCancelledEventFromDomain(job *entity.Job, cancelledBy, reason string) *JobCancelledEvent {
	data := JobCancelledData{
		JobID:       job.ID(),
		JobType:     string(job.JobType()),
		Name:        job.Name(),
		CancelledAt: time.Now(),
		CancelledBy: cancelledBy,
		Reason:      reason,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobCancelledEvent(aggregateID, data)
}

// CreateJobRetriedEventFromDomain creates JobRetriedEvent from domain Job entity
func CreateJobRetriedEventFromDomain(job *entity.Job, previousError string) *JobRetriedEvent {
	data := JobRetriedData{
		JobID:         job.ID(),
		JobType:       string(job.JobType()),
		Name:          job.Name(),
		RetriedAt:     time.Now(),
		RetryCount:    job.RetryCount(),
		MaxRetries:    job.MaxRetries(),
		PreviousError: previousError,
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobRetriedEvent(aggregateID, data)
}

// CreateJobProgressUpdatedEventFromDomain creates JobProgressUpdatedEvent from domain Job entity
func CreateJobProgressUpdatedEventFromDomain(job *entity.Job) *JobProgressUpdatedEvent {
	data := JobProgressUpdatedData{
		JobID:     job.ID(),
		JobType:   string(job.JobType()),
		Name:      job.Name(),
		Progress:  ConvertJobProgressToDTO(job.Progress()),
		UpdatedAt: time.Now(),
	}
	
	aggregateID := "job-" + string(rune(job.ID()))
	return NewJobProgressUpdatedEvent(aggregateID, data)
}

// Helper function to create aggregate ID from job ID
func CreateJobAggregateID(jobID int) string {
	return "job-" + string(rune(jobID))
}
