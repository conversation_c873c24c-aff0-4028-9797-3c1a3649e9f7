// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateProxies implements pgx.CopyFromSource.
type iteratorForCreateProxies struct {
	rows                 []CreateProxiesParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateProxies) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateProxies) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].Host,
		r.rows[0].Port,
		r.rows[0].UserName,
		r.rows[0].Password,
		r.rows[0].IsActive,
		r.rows[0].IsUse,
		r.rows[0].LastUsedAt,
	}, nil
}

func (r iteratorForCreateProxies) Err() error {
	return nil
}

func (q *Queries) CreateProxies(ctx context.Context, arg []CreateProxiesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"proxies"}, []string{"host", "port", "user_name", "password", "is_active", "is_use", "last_used_at"}, &iteratorForCreateProxies{rows: arg})
}
