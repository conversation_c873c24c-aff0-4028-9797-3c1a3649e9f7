syntax = "proto3";

package proxy.v1;

option go_package = "go_core_market/pkg/pb/proxy";

import "google/protobuf/timestamp.proto";

// Proxy message
message Proxy {
  int32 id = 1;
  string host = 2;
  int32 port = 3;
  string user_name = 4;
  string password = 5;
  bool is_active = 6;
  bool is_use = 7;
  google.protobuf.Timestamp last_used_at = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// Request/Response messages for Commands
message CreateProxyRequest {
  string host = 1;
  int32 port = 2;
  string user_name = 3;
  string password = 4;
  bool is_active = 5;
  string created_by = 6;
}

message CreateProxyResponse {
  bool success = 1;
  string message = 2;
}

message CreateProxyBatchRequest {
  repeated CreateProxyRequest proxies = 1;
  string created_by = 2;
}

message CreateProxyBatchResponse {
  bool success = 1;
  string message = 2;
}

message UpdateProxyRequest {
  int32 id = 1;
  string host = 2;
  int32 port = 3;
  string user_name = 4;
  string password = 5;
  string updated_by = 6;
}

message UpdateProxyResponse {
  bool success = 1;
  string message = 2;
}

message DeleteProxyRequest {
  int32 id = 1;
  string deleted_by = 2;
}

message DeleteProxyResponse {
  bool success = 1;
  string message = 2;
}

message ActivateProxyRequest {
  int32 id = 1;
  string activated_by = 2;
}

message ActivateProxyResponse {
  bool success = 1;
  string message = 2;
}

message DeactivateProxyRequest {
  int32 id = 1;
  string deactivated_by = 2;
}

message DeactivateProxyResponse {
  bool success = 1;
  string message = 2;
}

message MarkProxyUsedRequest {
  int32 id = 1;
  string used_by = 2;
}

message MarkProxyUsedResponse {
  bool success = 1;
  string message = 2;
}

message MarkProxiesUsedRequest {
  repeated int32 ids = 1;
  string used_by = 2;
}

message MarkProxiesUsedResponse {
  bool success = 1;
  string message = 2;
}

message ReleaseProxyRequest {
  int32 id = 1;
  string released_by = 2;
}

message ReleaseProxyResponse {
  bool success = 1;
  string message = 2;
}

message ReleaseManyProxiesRequest {
  repeated int32 ids = 1;
  string released_by = 2;
}

message ReleaseManyProxiesResponse {
  bool success = 1;
  string message = 2;
}

// Request/Response messages for Queries
message GetAllProxiesRequest {
  int32 page = 1;
  int32 page_size = 2;
  string order_by = 3;
}

message GetAllProxiesResponse {
  repeated Proxy proxies = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
  int32 total_pages = 5;
}

message GetProxyByIdRequest {
  int32 id = 1;
}

message GetProxyByIdResponse {
  Proxy proxy = 1;
}

message GetAvailableProxiesRequest {
  int32 limit = 1;
}

message GetAvailableProxiesResponse {
  repeated Proxy proxies = 1;
}

// gRPC Service
service ProxyService {
  // Commands
  rpc CreateProxy(CreateProxyRequest) returns (CreateProxyResponse);
  rpc CreateProxyBatch(CreateProxyBatchRequest) returns (CreateProxyBatchResponse);
  rpc UpdateProxy(UpdateProxyRequest) returns (UpdateProxyResponse);
  rpc DeleteProxy(DeleteProxyRequest) returns (DeleteProxyResponse);
  rpc ActivateProxy(ActivateProxyRequest) returns (ActivateProxyResponse);
  rpc DeactivateProxy(DeactivateProxyRequest) returns (DeactivateProxyResponse);
  rpc MarkProxyUsed(MarkProxyUsedRequest) returns (MarkProxyUsedResponse);
  rpc MarkProxiesUsed(MarkProxiesUsedRequest) returns (MarkProxiesUsedResponse);
  rpc ReleaseProxy(ReleaseProxyRequest) returns (ReleaseProxyResponse);
  rpc ReleaseManyProxies(ReleaseManyProxiesRequest) returns (ReleaseManyProxiesResponse);

  // Queries
  rpc GetAllProxies(GetAllProxiesRequest) returns (GetAllProxiesResponse);
  rpc GetProxyById(GetProxyByIdRequest) returns (GetProxyByIdResponse);
  rpc GetAvailableProxies(GetAvailableProxiesRequest) returns (GetAvailableProxiesResponse);
}
