package grpc

import (
	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	pb "go_core_market/pkg/pb/proxy"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func ProxyToProto(proxy *queryproxy.Proxy) *pb.Proxy {
	if proxy == nil {
		return nil
	}

	pbProxy := &pb.Proxy{
		Id:        int32(proxy.Id),
		Host:      proxy.Host,
		Port:      int32(proxy.Port),
		UserName:  proxy.UserName,
		Password:  proxy.Password,
		IsActive:  proxy.IsActive,
		IsUse:     proxy.IsUse,
		CreatedAt: timestamppb.New(proxy.CreatedAt),
		UpdatedAt: timestamppb.New(proxy.UpdatedAt),
	}

	if proxy.LastUsedAt != nil {
		pbProxy.LastUsedAt = timestamppb.New(*proxy.LastUsedAt)
	}

	return pbProxy
}
