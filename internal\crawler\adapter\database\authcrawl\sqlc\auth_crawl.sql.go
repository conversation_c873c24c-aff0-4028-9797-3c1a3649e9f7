// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: auth_crawl.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountAuthCrawl = `-- name: CountAuthCrawl :one
SELECT COUNT(*) FROM auth_crawl
`

func (q *Queries) CountAuthCrawl(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountAuthCrawl)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CreateAuth = `-- name: CreateAuth :one
INSERT INTO auth_crawl (market_id, auth_type, value, is_active, is_use, expired_at)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id
`

type CreateAuthParams struct {
	MarketID  int                `db:"market_id" json:"market_id"`
	AuthType  string             `db:"auth_type" json:"auth_type"`
	Value     string             `db:"value" json:"value"`
	IsActive  bool               `db:"is_active" json:"is_active"`
	IsUse     bool               `db:"is_use" json:"is_use"`
	ExpiredAt pgtype.Timestamptz `db:"expired_at" json:"expired_at"`
}

func (q *Queries) CreateAuth(ctx context.Context, arg CreateAuthParams) (int, error) {
	row := q.db.QueryRow(ctx, CreateAuth,
		arg.MarketID,
		arg.AuthType,
		arg.Value,
		arg.IsActive,
		arg.IsUse,
		arg.ExpiredAt,
	)
	var id int
	err := row.Scan(&id)
	return id, err
}

type CreateManyParams struct {
	MarketID  int                `db:"market_id" json:"market_id"`
	AuthType  string             `db:"auth_type" json:"auth_type"`
	Value     string             `db:"value" json:"value"`
	IsActive  bool               `db:"is_active" json:"is_active"`
	IsUse     bool               `db:"is_use" json:"is_use"`
	ExpiredAt pgtype.Timestamptz `db:"expired_at" json:"expired_at"`
}

const DeleteAuth = `-- name: DeleteAuth :exec
DELETE FROM auth_crawl WHERE id = $1
`

func (q *Queries) DeleteAuth(ctx context.Context, id int) error {
	_, err := q.db.Exec(ctx, DeleteAuth, id)
	return err
}

const FindAuthByID = `-- name: FindAuthByID :one
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at FROM auth_crawl WHERE id = $1
`

func (q *Queries) FindAuthByID(ctx context.Context, id int) (*AuthCrawl, error) {
	row := q.db.QueryRow(ctx, FindAuthByID, id)
	var i AuthCrawl
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.AuthType,
		&i.Value,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.ExpiredAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const FindAuthByIDs = `-- name: FindAuthByIDs :many
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl WHERE id = ANY($1::int[])
`

func (q *Queries) FindAuthByIDs(ctx context.Context, ids []int32) ([]*AuthCrawl, error) {
	rows, err := q.db.Query(ctx, FindAuthByIDs, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AuthCrawl{}
	for rows.Next() {
		var i AuthCrawl
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.AuthType,
			&i.Value,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.ExpiredAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const FindByValue = `-- name: FindByValue :one
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at FROM auth_crawl WHERE value = $1 LIMIT 1
`

func (q *Queries) FindByValue(ctx context.Context, value string) (*AuthCrawl, error) {
	row := q.db.QueryRow(ctx, FindByValue, value)
	var i AuthCrawl
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.AuthType,
		&i.Value,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.ExpiredAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetAllAuthCrawl = `-- name: GetAllAuthCrawl :many
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
ORDER BY
    CASE WHEN $3 = 'id' THEN id END,
    CASE WHEN $3 = 'market_id' THEN market_id END,
    CASE WHEN $3 = 'auth_type' THEN auth_type END,
    CASE WHEN $3 = 'created_at' THEN created_at END,
    CASE WHEN $3 = '' OR $3 IS NULL THEN created_at END DESC
LIMIT $1 OFFSET $2
`

type GetAllAuthCrawlParams struct {
	Limit   int32       `db:"limit" json:"limit"`
	Offset  int32       `db:"offset" json:"offset"`
	Column3 interface{} `db:"column_3" json:"column_3"`
}

func (q *Queries) GetAllAuthCrawl(ctx context.Context, arg GetAllAuthCrawlParams) ([]*AuthCrawl, error) {
	rows, err := q.db.Query(ctx, GetAllAuthCrawl, arg.Limit, arg.Offset, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AuthCrawl{}
	for rows.Next() {
		var i AuthCrawl
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.AuthType,
			&i.Value,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.ExpiredAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetAuthCrawlByMarketId = `-- name: GetAuthCrawlByMarketId :one
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
WHERE market_id = $1
LIMIT 1
`

func (q *Queries) GetAuthCrawlByMarketId(ctx context.Context, marketID int) (*AuthCrawl, error) {
	row := q.db.QueryRow(ctx, GetAuthCrawlByMarketId, marketID)
	var i AuthCrawl
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.AuthType,
		&i.Value,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.ExpiredAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetAvailableAuthCrawlByMarket = `-- name: GetAvailableAuthCrawlByMarket :many
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
WHERE market_id = $1 AND is_active = true AND is_use = false
  AND (expired_at IS NULL OR expired_at > NOW())
ORDER BY last_used_at ASC NULLS FIRST
LIMIT $2
`

type GetAvailableAuthCrawlByMarketParams struct {
	MarketID int   `db:"market_id" json:"market_id"`
	Limit    int32 `db:"limit" json:"limit"`
}

func (q *Queries) GetAvailableAuthCrawlByMarket(ctx context.Context, arg GetAvailableAuthCrawlByMarketParams) ([]*AuthCrawl, error) {
	rows, err := q.db.Query(ctx, GetAvailableAuthCrawlByMarket, arg.MarketID, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*AuthCrawl{}
	for rows.Next() {
		var i AuthCrawl
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.AuthType,
			&i.Value,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.ExpiredAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateAuth = `-- name: UpdateAuth :one
UPDATE auth_crawl 
SET market_id = $2, auth_type = $3, value = $4, is_active = $5, is_use = $6, 
    last_used_at = $7, expired_at = $8
WHERE id = $1
RETURNING id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
`

type UpdateAuthParams struct {
	ID         int                `db:"id" json:"id"`
	MarketID   int                `db:"market_id" json:"market_id"`
	AuthType   string             `db:"auth_type" json:"auth_type"`
	Value      string             `db:"value" json:"value"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
	ExpiredAt  pgtype.Timestamptz `db:"expired_at" json:"expired_at"`
}

func (q *Queries) UpdateAuth(ctx context.Context, arg UpdateAuthParams) (*AuthCrawl, error) {
	row := q.db.QueryRow(ctx, UpdateAuth,
		arg.ID,
		arg.MarketID,
		arg.AuthType,
		arg.Value,
		arg.IsActive,
		arg.IsUse,
		arg.LastUsedAt,
		arg.ExpiredAt,
	)
	var i AuthCrawl
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.AuthType,
		&i.Value,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.ExpiredAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
