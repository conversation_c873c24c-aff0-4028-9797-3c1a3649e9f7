package repository

import (
	"go_core_market/internal/crawler/adapter/database/authcrawl/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

// FromSQLCModel converts sqlc.AuthCrawl to domain.Auth
func FromSQLCModel(sqlcAuth sqlc.AuthCrawl) *entity.Auth {
	var lastUsedAt *time.Time
	if sqlcAuth.LastUsedAt.Valid {
		lastUsedAt = &sqlcAuth.LastUsedAt.Time
	}

	var expiredAt *time.Time
	if sqlcAuth.ExpiredAt.Valid {
		expiredAt = &sqlcAuth.ExpiredAt.Time
	}

	return entity.RebuildAuthCrawl(entity.AuthCrawlRebuildParams{
		ID:         sqlcAuth.ID,
		MarketID:   sqlcAuth.MarketID,
		AuthType:   entity.AuthType(sqlcAuth.AuthType),
		Value:      sqlcAuth.Value,
		IsActive:   sqlcAuth.IsActive,
		IsUsed:     sqlcAuth.IsUse,
		ExpiredAt:  expiredAt,
		LastUsedAt: lastUsedAt,
	})
}

// FromSQLCModels converts slice of sqlc.AuthCrawl to slice of domain.Auth
func FromSQLCModels(sqlcAuths []sqlc.AuthCrawl) []*entity.Auth {
	auths := make([]*entity.Auth, len(sqlcAuths))
	for i, sqlcAuth := range sqlcAuths {
		auths[i] = FromSQLCModel(sqlcAuth)
	}
	return auths
}

// ToCreateAuthParams converts domain.Auth to sqlc.CreateAuthParams
func ToCreateAuthParams(auth *entity.Auth) sqlc.CreateAuthParams {
	var expiredAt pgtype.Timestamptz
	if auth.ExpiredAt() != nil {
		expiredAt = pgtype.Timestamptz{
			Time:  *auth.ExpiredAt(),
			Valid: true,
		}
	}

	return sqlc.CreateAuthParams{
		MarketID:  auth.MarketID(),
		AuthType:  string(auth.AuthType()),
		Value:     auth.Value(),
		IsActive:  auth.IsActive(),
		IsUse:     auth.IsUse(),
		ExpiredAt: expiredAt,
	}
}

// ToCreateAuthBatchParams converts slice of domain.Auth to slice of sqlc.CreateAuthBatchParams
func ToCreateAuthBatchParams(auths []*entity.Auth) []sqlc.CreateManyParams {
	params := make([]sqlc.CreateManyParams, len(auths))

	for i, auth := range auths {
		var expiredAt pgtype.Timestamptz
		if auth.ExpiredAt() != nil {
			expiredAt = pgtype.Timestamptz{
				Time:  *auth.ExpiredAt(),
				Valid: true,
			}
		}

		params[i] = sqlc.CreateManyParams{
			MarketID:  auth.MarketID(),
			AuthType:  string(auth.AuthType()),
			Value:     auth.Value(),
			IsActive:  auth.IsActive(),
			IsUse:     auth.IsUse(),
			ExpiredAt: expiredAt,
		}
	}
	return params
}

// ToUpdateAuthParams converts domain.Auth to sqlc.UpdateAuthParams
func ToUpdateAuthParams(auth *entity.Auth) sqlc.UpdateAuthParams {
	var lastUsedAt pgtype.Timestamptz
	if auth.LastUsedAt() != nil {
		lastUsedAt = pgtype.Timestamptz{
			Time:  *auth.LastUsedAt(),
			Valid: true,
		}
	}

	var expiredAt pgtype.Timestamptz
	if auth.ExpiredAt() != nil {
		expiredAt = pgtype.Timestamptz{
			Time:  *auth.ExpiredAt(),
			Valid: true,
		}
	}

	return sqlc.UpdateAuthParams{
		ID:         auth.ID(),
		MarketID:   auth.MarketID(),
		AuthType:   string(auth.AuthType()),
		Value:      auth.Value(),
		IsActive:   auth.IsActive(),
		IsUse:      auth.IsUse(),
		LastUsedAt: lastUsedAt,
		ExpiredAt:  expiredAt,
	}
}
