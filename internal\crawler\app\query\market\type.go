package querymarket

import "time"

type Market struct {
	Id               int
	Name             string
	DisplayName      string
	Type             string
	Status           string
	BaseURL          string
	Currency         string
	BuyerFeePercent  float64
	SellerFeePercent float64
	CountryCode      string
	Language         string
	Description      string
	IsActive         bool
	LastCrawlAt      *time.Time
	CreatedAt        time.Time
	UpdatedAt        time.Time
}
