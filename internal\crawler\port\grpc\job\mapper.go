package grpc

import (
	queryjob "go_core_market/internal/crawler/app/query/job"
	pb "go_core_market/pkg/pb/job"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func JobToProto(job *queryjob.Job) *pb.Job {
	if job == nil {
		return nil
	}

	pbJob := &pb.Job{
		Id:             int32(job.Id),
		JobType:        job.JobType,
		Name:           job.Name,
		Description:    job.Description,
		Status:         job.Status,
		MaxRetries:     int32(job.MaxRetries),
		RetryCount:     int32(job.RetryCount),
		TimeoutSeconds: int32(job.Timeout),
		CreatedBy:      job.CreatedBy,
		CreatedAt:      timestamppb.New(job.CreatedAt),
		UpdatedAt:      timestamppb.New(job.UpdatedAt),
	}

	// Handle optional fields
	if job.CrawlConfigId != nil {
		configId := int32(*job.CrawlConfigId)
		pbJob.CrawlConfigId = &configId
	}

	if job.ScheduledAt != nil {
		pbJob.ScheduledAt = timestamppb.New(*job.ScheduledAt)
	}

	if job.StartedAt != nil {
		pbJob.StartedAt = timestamppb.New(*job.StartedAt)
	}

	if job.CompletedAt != nil {
		pbJob.CompletedAt = timestamppb.New(*job.CompletedAt)
	}

	if job.CurrentStep != nil {
		currentStep := int32(*job.CurrentStep)
		pbJob.CurrentStep = &currentStep
	}

	if job.TotalSteps != nil {
		totalSteps := int32(*job.TotalSteps)
		pbJob.TotalSteps = &totalSteps
	}

	if job.ProgressMessage != nil {
		progressMessage := *job.ProgressMessage
		pbJob.ProgressMessage = &progressMessage
	}

	if job.ProgressPercentage != nil {
		progressPercentage := int32(*job.ProgressPercentage)
		pbJob.ProgressPercentage = &progressPercentage
	}

	if job.LastError != nil {
		lastError := *job.LastError
		pbJob.LastError = &lastError
	}

	return pbJob
}

func JobsToProto(jobs []*queryjob.Job) []*pb.Job {
	pbJobs := make([]*pb.Job, len(jobs))
	for i, job := range jobs {
		pbJobs[i] = JobToProto(job)
	}
	return pbJobs
}
