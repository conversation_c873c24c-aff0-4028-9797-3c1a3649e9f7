package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CreateAuthBatch struct {
	Auths     []CreateAuth
	CreatedBy string
}

type CreateAuthBatchHandler decorator.CommandHandler[CreateAuthBatch]

type createAuthBatchHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewCreateAuthBatchHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateAuthBatchHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateAuthBatch](
		createAuthBatchHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createAuthBatchHandler) Handle(ctx context.Context, cmd CreateAuthBatch) error {
	// Create domain auths
	var auths []*entity.Auth
	for _, authCmd := range cmd.Auths {
		authParams := entity.AuthParams{
			MarketID:  authCmd.MarketID,
			AuthType:  authCmd.AuthType,
			Value:     authCmd.Value,
			IsActive:  authCmd.IsActive,
			ExpiredAt: authCmd.ExpiredAt,
		}

		auth, err := entity.NewAuth(authParams)
		if err != nil {
			return err
		}
		auths = append(auths, auth)
	}
	err := h.repo.CreateMany(ctx, auths)
	if err != nil {
		return err
	}

	// Publish events (optional - could be done asynchronously)
	// In a real system, you might want to publish a single batch event
	// or handle this in a separate event handler

	return nil
}
