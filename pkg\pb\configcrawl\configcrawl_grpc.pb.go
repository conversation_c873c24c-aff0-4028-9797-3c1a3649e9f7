// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: configcrawl.proto

package configcrawl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CrawlConfigService_CreateCrawlConfig_FullMethodName                    = "/configcrawl.v1.CrawlConfigService/CreateCrawlConfig"
	CrawlConfigService_UpdateCrawlConfig_FullMethodName                    = "/configcrawl.v1.CrawlConfigService/UpdateCrawlConfig"
	CrawlConfigService_ActivateCrawlConfig_FullMethodName                  = "/configcrawl.v1.CrawlConfigService/ActivateCrawlConfig"
	CrawlConfigService_DeactivateCrawlConfig_FullMethodName                = "/configcrawl.v1.CrawlConfigService/DeactivateCrawlConfig"
	CrawlConfigService_UpdateLastUsed_FullMethodName                       = "/configcrawl.v1.CrawlConfigService/UpdateLastUsed"
	CrawlConfigService_DeleteCrawlConfig_FullMethodName                    = "/configcrawl.v1.CrawlConfigService/DeleteCrawlConfig"
	CrawlConfigService_GetAllCrawlConfigs_FullMethodName                   = "/configcrawl.v1.CrawlConfigService/GetAllCrawlConfigs"
	CrawlConfigService_GetCrawlConfigById_FullMethodName                   = "/configcrawl.v1.CrawlConfigService/GetCrawlConfigById"
	CrawlConfigService_GetActiveCrawlConfigs_FullMethodName                = "/configcrawl.v1.CrawlConfigService/GetActiveCrawlConfigs"
	CrawlConfigService_GetCrawlConfigsByMarketId_FullMethodName            = "/configcrawl.v1.CrawlConfigService/GetCrawlConfigsByMarketId"
	CrawlConfigService_GetCrawlConfigsByType_FullMethodName                = "/configcrawl.v1.CrawlConfigService/GetCrawlConfigsByType"
	CrawlConfigService_GetActiveCrawlConfigsByMarket_FullMethodName        = "/configcrawl.v1.CrawlConfigService/GetActiveCrawlConfigsByMarket"
	CrawlConfigService_GetActiveCrawlConfigsByType_FullMethodName          = "/configcrawl.v1.CrawlConfigService/GetActiveCrawlConfigsByType"
	CrawlConfigService_GetActiveCrawlConfigsByMarketAndType_FullMethodName = "/configcrawl.v1.CrawlConfigService/GetActiveCrawlConfigsByMarketAndType"
	CrawlConfigService_FilterCrawlConfigs_FullMethodName                   = "/configcrawl.v1.CrawlConfigService/FilterCrawlConfigs"
	CrawlConfigService_GetLeastUsedActiveConfig_FullMethodName             = "/configcrawl.v1.CrawlConfigService/GetLeastUsedActiveConfig"
	CrawlConfigService_GetLeastUsedActiveConfigByMarket_FullMethodName     = "/configcrawl.v1.CrawlConfigService/GetLeastUsedActiveConfigByMarket"
	CrawlConfigService_GetLeastUsedActiveConfigByType_FullMethodName       = "/configcrawl.v1.CrawlConfigService/GetLeastUsedActiveConfigByType"
)

// CrawlConfigServiceClient is the client API for CrawlConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC Service
type CrawlConfigServiceClient interface {
	// Commands
	CreateCrawlConfig(ctx context.Context, in *CreateCrawlConfigRequest, opts ...grpc.CallOption) (*CreateCrawlConfigResponse, error)
	UpdateCrawlConfig(ctx context.Context, in *UpdateCrawlConfigRequest, opts ...grpc.CallOption) (*UpdateCrawlConfigResponse, error)
	ActivateCrawlConfig(ctx context.Context, in *ActivateCrawlConfigRequest, opts ...grpc.CallOption) (*ActivateCrawlConfigResponse, error)
	DeactivateCrawlConfig(ctx context.Context, in *DeactivateCrawlConfigRequest, opts ...grpc.CallOption) (*DeactivateCrawlConfigResponse, error)
	UpdateLastUsed(ctx context.Context, in *UpdateLastUsedRequest, opts ...grpc.CallOption) (*UpdateLastUsedResponse, error)
	DeleteCrawlConfig(ctx context.Context, in *DeleteCrawlConfigRequest, opts ...grpc.CallOption) (*DeleteCrawlConfigResponse, error)
	// Queries
	GetAllCrawlConfigs(ctx context.Context, in *GetAllCrawlConfigsRequest, opts ...grpc.CallOption) (*GetAllCrawlConfigsResponse, error)
	GetCrawlConfigById(ctx context.Context, in *GetCrawlConfigByIdRequest, opts ...grpc.CallOption) (*GetCrawlConfigByIdResponse, error)
	GetActiveCrawlConfigs(ctx context.Context, in *GetActiveCrawlConfigsRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsResponse, error)
	GetCrawlConfigsByMarketId(ctx context.Context, in *GetCrawlConfigsByMarketIdRequest, opts ...grpc.CallOption) (*GetCrawlConfigsByMarketIdResponse, error)
	GetCrawlConfigsByType(ctx context.Context, in *GetCrawlConfigsByTypeRequest, opts ...grpc.CallOption) (*GetCrawlConfigsByTypeResponse, error)
	GetActiveCrawlConfigsByMarket(ctx context.Context, in *GetActiveCrawlConfigsByMarketRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByMarketResponse, error)
	GetActiveCrawlConfigsByType(ctx context.Context, in *GetActiveCrawlConfigsByTypeRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByTypeResponse, error)
	GetActiveCrawlConfigsByMarketAndType(ctx context.Context, in *GetActiveCrawlConfigsByMarketAndTypeRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByMarketAndTypeResponse, error)
	FilterCrawlConfigs(ctx context.Context, in *FilterCrawlConfigsRequest, opts ...grpc.CallOption) (*FilterCrawlConfigsResponse, error)
	GetLeastUsedActiveConfig(ctx context.Context, in *GetLeastUsedActiveConfigRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigResponse, error)
	GetLeastUsedActiveConfigByMarket(ctx context.Context, in *GetLeastUsedActiveConfigByMarketRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigByMarketResponse, error)
	GetLeastUsedActiveConfigByType(ctx context.Context, in *GetLeastUsedActiveConfigByTypeRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigByTypeResponse, error)
}

type crawlConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCrawlConfigServiceClient(cc grpc.ClientConnInterface) CrawlConfigServiceClient {
	return &crawlConfigServiceClient{cc}
}

func (c *crawlConfigServiceClient) CreateCrawlConfig(ctx context.Context, in *CreateCrawlConfigRequest, opts ...grpc.CallOption) (*CreateCrawlConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCrawlConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_CreateCrawlConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) UpdateCrawlConfig(ctx context.Context, in *UpdateCrawlConfigRequest, opts ...grpc.CallOption) (*UpdateCrawlConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCrawlConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_UpdateCrawlConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) ActivateCrawlConfig(ctx context.Context, in *ActivateCrawlConfigRequest, opts ...grpc.CallOption) (*ActivateCrawlConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateCrawlConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_ActivateCrawlConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) DeactivateCrawlConfig(ctx context.Context, in *DeactivateCrawlConfigRequest, opts ...grpc.CallOption) (*DeactivateCrawlConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeactivateCrawlConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_DeactivateCrawlConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) UpdateLastUsed(ctx context.Context, in *UpdateLastUsedRequest, opts ...grpc.CallOption) (*UpdateLastUsedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLastUsedResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_UpdateLastUsed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) DeleteCrawlConfig(ctx context.Context, in *DeleteCrawlConfigRequest, opts ...grpc.CallOption) (*DeleteCrawlConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteCrawlConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_DeleteCrawlConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetAllCrawlConfigs(ctx context.Context, in *GetAllCrawlConfigsRequest, opts ...grpc.CallOption) (*GetAllCrawlConfigsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllCrawlConfigsResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetAllCrawlConfigs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetCrawlConfigById(ctx context.Context, in *GetCrawlConfigByIdRequest, opts ...grpc.CallOption) (*GetCrawlConfigByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCrawlConfigByIdResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetCrawlConfigById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetActiveCrawlConfigs(ctx context.Context, in *GetActiveCrawlConfigsRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveCrawlConfigsResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetActiveCrawlConfigs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetCrawlConfigsByMarketId(ctx context.Context, in *GetCrawlConfigsByMarketIdRequest, opts ...grpc.CallOption) (*GetCrawlConfigsByMarketIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCrawlConfigsByMarketIdResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetCrawlConfigsByMarketId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetCrawlConfigsByType(ctx context.Context, in *GetCrawlConfigsByTypeRequest, opts ...grpc.CallOption) (*GetCrawlConfigsByTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCrawlConfigsByTypeResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetCrawlConfigsByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetActiveCrawlConfigsByMarket(ctx context.Context, in *GetActiveCrawlConfigsByMarketRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveCrawlConfigsByMarketResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetActiveCrawlConfigsByMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetActiveCrawlConfigsByType(ctx context.Context, in *GetActiveCrawlConfigsByTypeRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveCrawlConfigsByTypeResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetActiveCrawlConfigsByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetActiveCrawlConfigsByMarketAndType(ctx context.Context, in *GetActiveCrawlConfigsByMarketAndTypeRequest, opts ...grpc.CallOption) (*GetActiveCrawlConfigsByMarketAndTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetActiveCrawlConfigsByMarketAndTypeResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetActiveCrawlConfigsByMarketAndType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) FilterCrawlConfigs(ctx context.Context, in *FilterCrawlConfigsRequest, opts ...grpc.CallOption) (*FilterCrawlConfigsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterCrawlConfigsResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_FilterCrawlConfigs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetLeastUsedActiveConfig(ctx context.Context, in *GetLeastUsedActiveConfigRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeastUsedActiveConfigResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetLeastUsedActiveConfig_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetLeastUsedActiveConfigByMarket(ctx context.Context, in *GetLeastUsedActiveConfigByMarketRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigByMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeastUsedActiveConfigByMarketResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetLeastUsedActiveConfigByMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *crawlConfigServiceClient) GetLeastUsedActiveConfigByType(ctx context.Context, in *GetLeastUsedActiveConfigByTypeRequest, opts ...grpc.CallOption) (*GetLeastUsedActiveConfigByTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetLeastUsedActiveConfigByTypeResponse)
	err := c.cc.Invoke(ctx, CrawlConfigService_GetLeastUsedActiveConfigByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CrawlConfigServiceServer is the server API for CrawlConfigService service.
// All implementations must embed UnimplementedCrawlConfigServiceServer
// for forward compatibility.
//
// gRPC Service
type CrawlConfigServiceServer interface {
	// Commands
	CreateCrawlConfig(context.Context, *CreateCrawlConfigRequest) (*CreateCrawlConfigResponse, error)
	UpdateCrawlConfig(context.Context, *UpdateCrawlConfigRequest) (*UpdateCrawlConfigResponse, error)
	ActivateCrawlConfig(context.Context, *ActivateCrawlConfigRequest) (*ActivateCrawlConfigResponse, error)
	DeactivateCrawlConfig(context.Context, *DeactivateCrawlConfigRequest) (*DeactivateCrawlConfigResponse, error)
	UpdateLastUsed(context.Context, *UpdateLastUsedRequest) (*UpdateLastUsedResponse, error)
	DeleteCrawlConfig(context.Context, *DeleteCrawlConfigRequest) (*DeleteCrawlConfigResponse, error)
	// Queries
	GetAllCrawlConfigs(context.Context, *GetAllCrawlConfigsRequest) (*GetAllCrawlConfigsResponse, error)
	GetCrawlConfigById(context.Context, *GetCrawlConfigByIdRequest) (*GetCrawlConfigByIdResponse, error)
	GetActiveCrawlConfigs(context.Context, *GetActiveCrawlConfigsRequest) (*GetActiveCrawlConfigsResponse, error)
	GetCrawlConfigsByMarketId(context.Context, *GetCrawlConfigsByMarketIdRequest) (*GetCrawlConfigsByMarketIdResponse, error)
	GetCrawlConfigsByType(context.Context, *GetCrawlConfigsByTypeRequest) (*GetCrawlConfigsByTypeResponse, error)
	GetActiveCrawlConfigsByMarket(context.Context, *GetActiveCrawlConfigsByMarketRequest) (*GetActiveCrawlConfigsByMarketResponse, error)
	GetActiveCrawlConfigsByType(context.Context, *GetActiveCrawlConfigsByTypeRequest) (*GetActiveCrawlConfigsByTypeResponse, error)
	GetActiveCrawlConfigsByMarketAndType(context.Context, *GetActiveCrawlConfigsByMarketAndTypeRequest) (*GetActiveCrawlConfigsByMarketAndTypeResponse, error)
	FilterCrawlConfigs(context.Context, *FilterCrawlConfigsRequest) (*FilterCrawlConfigsResponse, error)
	GetLeastUsedActiveConfig(context.Context, *GetLeastUsedActiveConfigRequest) (*GetLeastUsedActiveConfigResponse, error)
	GetLeastUsedActiveConfigByMarket(context.Context, *GetLeastUsedActiveConfigByMarketRequest) (*GetLeastUsedActiveConfigByMarketResponse, error)
	GetLeastUsedActiveConfigByType(context.Context, *GetLeastUsedActiveConfigByTypeRequest) (*GetLeastUsedActiveConfigByTypeResponse, error)
	mustEmbedUnimplementedCrawlConfigServiceServer()
}

// UnimplementedCrawlConfigServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCrawlConfigServiceServer struct{}

func (UnimplementedCrawlConfigServiceServer) CreateCrawlConfig(context.Context, *CreateCrawlConfigRequest) (*CreateCrawlConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCrawlConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) UpdateCrawlConfig(context.Context, *UpdateCrawlConfigRequest) (*UpdateCrawlConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCrawlConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) ActivateCrawlConfig(context.Context, *ActivateCrawlConfigRequest) (*ActivateCrawlConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateCrawlConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) DeactivateCrawlConfig(context.Context, *DeactivateCrawlConfigRequest) (*DeactivateCrawlConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateCrawlConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) UpdateLastUsed(context.Context, *UpdateLastUsedRequest) (*UpdateLastUsedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLastUsed not implemented")
}
func (UnimplementedCrawlConfigServiceServer) DeleteCrawlConfig(context.Context, *DeleteCrawlConfigRequest) (*DeleteCrawlConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCrawlConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetAllCrawlConfigs(context.Context, *GetAllCrawlConfigsRequest) (*GetAllCrawlConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllCrawlConfigs not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetCrawlConfigById(context.Context, *GetCrawlConfigByIdRequest) (*GetCrawlConfigByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCrawlConfigById not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetActiveCrawlConfigs(context.Context, *GetActiveCrawlConfigsRequest) (*GetActiveCrawlConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveCrawlConfigs not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetCrawlConfigsByMarketId(context.Context, *GetCrawlConfigsByMarketIdRequest) (*GetCrawlConfigsByMarketIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCrawlConfigsByMarketId not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetCrawlConfigsByType(context.Context, *GetCrawlConfigsByTypeRequest) (*GetCrawlConfigsByTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCrawlConfigsByType not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetActiveCrawlConfigsByMarket(context.Context, *GetActiveCrawlConfigsByMarketRequest) (*GetActiveCrawlConfigsByMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveCrawlConfigsByMarket not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetActiveCrawlConfigsByType(context.Context, *GetActiveCrawlConfigsByTypeRequest) (*GetActiveCrawlConfigsByTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveCrawlConfigsByType not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetActiveCrawlConfigsByMarketAndType(context.Context, *GetActiveCrawlConfigsByMarketAndTypeRequest) (*GetActiveCrawlConfigsByMarketAndTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetActiveCrawlConfigsByMarketAndType not implemented")
}
func (UnimplementedCrawlConfigServiceServer) FilterCrawlConfigs(context.Context, *FilterCrawlConfigsRequest) (*FilterCrawlConfigsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCrawlConfigs not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetLeastUsedActiveConfig(context.Context, *GetLeastUsedActiveConfigRequest) (*GetLeastUsedActiveConfigResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeastUsedActiveConfig not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetLeastUsedActiveConfigByMarket(context.Context, *GetLeastUsedActiveConfigByMarketRequest) (*GetLeastUsedActiveConfigByMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeastUsedActiveConfigByMarket not implemented")
}
func (UnimplementedCrawlConfigServiceServer) GetLeastUsedActiveConfigByType(context.Context, *GetLeastUsedActiveConfigByTypeRequest) (*GetLeastUsedActiveConfigByTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetLeastUsedActiveConfigByType not implemented")
}
func (UnimplementedCrawlConfigServiceServer) mustEmbedUnimplementedCrawlConfigServiceServer() {}
func (UnimplementedCrawlConfigServiceServer) testEmbeddedByValue()                            {}

// UnsafeCrawlConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CrawlConfigServiceServer will
// result in compilation errors.
type UnsafeCrawlConfigServiceServer interface {
	mustEmbedUnimplementedCrawlConfigServiceServer()
}

func RegisterCrawlConfigServiceServer(s grpc.ServiceRegistrar, srv CrawlConfigServiceServer) {
	// If the following call pancis, it indicates UnimplementedCrawlConfigServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CrawlConfigService_ServiceDesc, srv)
}

func _CrawlConfigService_CreateCrawlConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCrawlConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).CreateCrawlConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_CreateCrawlConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).CreateCrawlConfig(ctx, req.(*CreateCrawlConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_UpdateCrawlConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCrawlConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).UpdateCrawlConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_UpdateCrawlConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).UpdateCrawlConfig(ctx, req.(*UpdateCrawlConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_ActivateCrawlConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateCrawlConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).ActivateCrawlConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_ActivateCrawlConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).ActivateCrawlConfig(ctx, req.(*ActivateCrawlConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_DeactivateCrawlConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateCrawlConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).DeactivateCrawlConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_DeactivateCrawlConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).DeactivateCrawlConfig(ctx, req.(*DeactivateCrawlConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_UpdateLastUsed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLastUsedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).UpdateLastUsed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_UpdateLastUsed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).UpdateLastUsed(ctx, req.(*UpdateLastUsedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_DeleteCrawlConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCrawlConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).DeleteCrawlConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_DeleteCrawlConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).DeleteCrawlConfig(ctx, req.(*DeleteCrawlConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetAllCrawlConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllCrawlConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetAllCrawlConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetAllCrawlConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetAllCrawlConfigs(ctx, req.(*GetAllCrawlConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetCrawlConfigById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrawlConfigByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetCrawlConfigById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigById(ctx, req.(*GetCrawlConfigByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetActiveCrawlConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveCrawlConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetActiveCrawlConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigs(ctx, req.(*GetActiveCrawlConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetCrawlConfigsByMarketId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrawlConfigsByMarketIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigsByMarketId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetCrawlConfigsByMarketId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigsByMarketId(ctx, req.(*GetCrawlConfigsByMarketIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetCrawlConfigsByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCrawlConfigsByTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigsByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetCrawlConfigsByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetCrawlConfigsByType(ctx, req.(*GetCrawlConfigsByTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetActiveCrawlConfigsByMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveCrawlConfigsByMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetActiveCrawlConfigsByMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByMarket(ctx, req.(*GetActiveCrawlConfigsByMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetActiveCrawlConfigsByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveCrawlConfigsByTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetActiveCrawlConfigsByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByType(ctx, req.(*GetActiveCrawlConfigsByTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetActiveCrawlConfigsByMarketAndType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetActiveCrawlConfigsByMarketAndTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByMarketAndType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetActiveCrawlConfigsByMarketAndType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetActiveCrawlConfigsByMarketAndType(ctx, req.(*GetActiveCrawlConfigsByMarketAndTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_FilterCrawlConfigs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCrawlConfigsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).FilterCrawlConfigs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_FilterCrawlConfigs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).FilterCrawlConfigs(ctx, req.(*FilterCrawlConfigsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetLeastUsedActiveConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeastUsedActiveConfigRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetLeastUsedActiveConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfig(ctx, req.(*GetLeastUsedActiveConfigRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetLeastUsedActiveConfigByMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeastUsedActiveConfigByMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfigByMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetLeastUsedActiveConfigByMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfigByMarket(ctx, req.(*GetLeastUsedActiveConfigByMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CrawlConfigService_GetLeastUsedActiveConfigByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetLeastUsedActiveConfigByTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfigByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CrawlConfigService_GetLeastUsedActiveConfigByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CrawlConfigServiceServer).GetLeastUsedActiveConfigByType(ctx, req.(*GetLeastUsedActiveConfigByTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CrawlConfigService_ServiceDesc is the grpc.ServiceDesc for CrawlConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CrawlConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "configcrawl.v1.CrawlConfigService",
	HandlerType: (*CrawlConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCrawlConfig",
			Handler:    _CrawlConfigService_CreateCrawlConfig_Handler,
		},
		{
			MethodName: "UpdateCrawlConfig",
			Handler:    _CrawlConfigService_UpdateCrawlConfig_Handler,
		},
		{
			MethodName: "ActivateCrawlConfig",
			Handler:    _CrawlConfigService_ActivateCrawlConfig_Handler,
		},
		{
			MethodName: "DeactivateCrawlConfig",
			Handler:    _CrawlConfigService_DeactivateCrawlConfig_Handler,
		},
		{
			MethodName: "UpdateLastUsed",
			Handler:    _CrawlConfigService_UpdateLastUsed_Handler,
		},
		{
			MethodName: "DeleteCrawlConfig",
			Handler:    _CrawlConfigService_DeleteCrawlConfig_Handler,
		},
		{
			MethodName: "GetAllCrawlConfigs",
			Handler:    _CrawlConfigService_GetAllCrawlConfigs_Handler,
		},
		{
			MethodName: "GetCrawlConfigById",
			Handler:    _CrawlConfigService_GetCrawlConfigById_Handler,
		},
		{
			MethodName: "GetActiveCrawlConfigs",
			Handler:    _CrawlConfigService_GetActiveCrawlConfigs_Handler,
		},
		{
			MethodName: "GetCrawlConfigsByMarketId",
			Handler:    _CrawlConfigService_GetCrawlConfigsByMarketId_Handler,
		},
		{
			MethodName: "GetCrawlConfigsByType",
			Handler:    _CrawlConfigService_GetCrawlConfigsByType_Handler,
		},
		{
			MethodName: "GetActiveCrawlConfigsByMarket",
			Handler:    _CrawlConfigService_GetActiveCrawlConfigsByMarket_Handler,
		},
		{
			MethodName: "GetActiveCrawlConfigsByType",
			Handler:    _CrawlConfigService_GetActiveCrawlConfigsByType_Handler,
		},
		{
			MethodName: "GetActiveCrawlConfigsByMarketAndType",
			Handler:    _CrawlConfigService_GetActiveCrawlConfigsByMarketAndType_Handler,
		},
		{
			MethodName: "FilterCrawlConfigs",
			Handler:    _CrawlConfigService_FilterCrawlConfigs_Handler,
		},
		{
			MethodName: "GetLeastUsedActiveConfig",
			Handler:    _CrawlConfigService_GetLeastUsedActiveConfig_Handler,
		},
		{
			MethodName: "GetLeastUsedActiveConfigByMarket",
			Handler:    _CrawlConfigService_GetLeastUsedActiveConfigByMarket_Handler,
		},
		{
			MethodName: "GetLeastUsedActiveConfigByType",
			Handler:    _CrawlConfigService_GetLeastUsedActiveConfigByType_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "configcrawl.proto",
}
