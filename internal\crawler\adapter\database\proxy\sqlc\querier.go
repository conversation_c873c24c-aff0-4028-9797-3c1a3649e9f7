// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	CountProxies(ctx context.Context) (int64, error)
	CreateProxies(ctx context.Context, arg []CreateProxiesParams) (int64, error)
	CreateProxy(ctx context.Context, arg CreateProxyParams) (int32, error)
	DeleteManyProxies(ctx context.Context, dollar_1 []int32) error
	DeleteProxy(ctx context.Context, id int32) error
	FindProxiesByIDs(ctx context.Context, dollar_1 []int32) ([]*Proxy, error)
	FindProxyByID(ctx context.Context, id int32) (*Proxy, error)
	GetAllProxies(ctx context.Context, arg GetAllProxiesParams) ([]*Proxy, error)
	GetAvailableProxies(ctx context.Context, limit int32) ([]*Proxy, error)
	UpdateProxy(ctx context.Context, arg UpdateProxyParams) (*Proxy, error)
}

var _ Querier = (*Queries)(nil)
