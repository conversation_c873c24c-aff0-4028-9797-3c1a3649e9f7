syntax = "proto3";

package configcrawl.v1;

option go_package = "go_core_market/pkg/pb/configcrawl";

import "google/protobuf/timestamp.proto";

// CrawlConfig message
message CrawlConfig {
  int32 id = 1;
  int32 market_id = 2;
  string description = 3;
  string type_config = 4;
  int32 requests_per_minute = 5;
  bool require_auth = 6;
  int32 max_number_auth = 7;
  int32 max_number_proxy = 8;
  int32 per_request_delay_seconds = 9;
  int32 timeout_seconds = 10;
  int32 max_retries = 11;
  int32 retry_delay_seconds = 12;
  int32 max_concurrent = 13;
  bool is_active = 14;
  optional google.protobuf.Timestamp last_used_at = 15;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
}

// Request/Response messages for Commands
message CreateCrawlConfigRequest {
  int32 market_id = 1;
  string description = 2;
  string type_config = 3;
  int32 requests_per_minute = 4;
  bool require_auth = 5;
  int32 max_number_auth = 6;
  int32 max_number_proxy = 7;
  int32 per_request_delay_seconds = 8;
  int32 timeout_seconds = 9;
  int32 max_retries = 10;
  int32 retry_delay_seconds = 11;
  int32 max_concurrent = 12;
  string created_by = 13;
}

message CreateCrawlConfigResponse {
  bool success = 1;
  string message = 2;
  optional int32 config_id = 3;
}

message UpdateCrawlConfigRequest {
  int32 id = 1;
  int32 market_id = 2;
  string description = 3;
  string type_config = 4;
  int32 requests_per_minute = 5;
  bool require_auth = 6;
  int32 max_number_auth = 7;
  int32 max_number_proxy = 8;
  int32 per_request_delay_seconds = 9;
  int32 timeout_seconds = 10;
  int32 max_retries = 11;
  int32 retry_delay_seconds = 12;
  int32 max_concurrent = 13;
  bool is_active = 14;
  string updated_by = 15;
}

message UpdateCrawlConfigResponse {
  bool success = 1;
  string message = 2;
}

message ActivateCrawlConfigRequest {
  int32 id = 1;
  string activated_by = 2;
}

message ActivateCrawlConfigResponse {
  bool success = 1;
  string message = 2;
}

message DeactivateCrawlConfigRequest {
  int32 id = 1;
  string deactivated_by = 2;
}

message DeactivateCrawlConfigResponse {
  bool success = 1;
  string message = 2;
}

message UpdateLastUsedRequest {
  int32 id = 1;
  string used_by = 2;
}

message UpdateLastUsedResponse {
  bool success = 1;
  string message = 2;
}

message DeleteCrawlConfigRequest {
  int32 id = 1;
  string deleted_by = 2;
}

message DeleteCrawlConfigResponse {
  bool success = 1;
  string message = 2;
}

// Request/Response messages for Queries
message GetAllCrawlConfigsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string order_by = 3;
}

message GetAllCrawlConfigsResponse {
  repeated CrawlConfig configs = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
  int32 total_pages = 5;
}

message GetCrawlConfigByIdRequest {
  int32 id = 1;
}

message GetCrawlConfigByIdResponse {
  CrawlConfig config = 1;
}

message GetActiveCrawlConfigsRequest {
  // No parameters needed
}

message GetActiveCrawlConfigsResponse {
  repeated CrawlConfig configs = 1;
}

message GetCrawlConfigsByMarketIdRequest {
  int32 market_id = 1;
}

message GetCrawlConfigsByMarketIdResponse {
  repeated CrawlConfig configs = 1;
}

message GetCrawlConfigsByTypeRequest {
  string type_config = 1;
}

message GetCrawlConfigsByTypeResponse {
  repeated CrawlConfig configs = 1;
}

message GetActiveCrawlConfigsByMarketRequest {
  int32 market_id = 1;
}

message GetActiveCrawlConfigsByMarketResponse {
  repeated CrawlConfig configs = 1;
}

message GetActiveCrawlConfigsByTypeRequest {
  string type_config = 1;
}

message GetActiveCrawlConfigsByTypeResponse {
  repeated CrawlConfig configs = 1;
}

message GetActiveCrawlConfigsByMarketAndTypeRequest {
  int32 market_id = 1;
  string type_config = 2;
}

message GetActiveCrawlConfigsByMarketAndTypeResponse {
  repeated CrawlConfig configs = 1;
}

message FilterCrawlConfigsRequest {
  optional int32 market_id = 1;
  optional string type_config = 2;
  optional bool is_active = 3;
  optional bool require_auth = 4;
  optional google.protobuf.Timestamp created_from = 5;
  optional google.protobuf.Timestamp created_to = 6;
  string order_by = 7;
}

message FilterCrawlConfigsResponse {
  repeated CrawlConfig configs = 1;
}

message GetLeastUsedActiveConfigRequest {
  // No parameters needed
}

message GetLeastUsedActiveConfigResponse {
  CrawlConfig config = 1;
}

message GetLeastUsedActiveConfigByMarketRequest {
  int32 market_id = 1;
}

message GetLeastUsedActiveConfigByMarketResponse {
  CrawlConfig config = 1;
}

message GetLeastUsedActiveConfigByTypeRequest {
  string type_config = 1;
}

message GetLeastUsedActiveConfigByTypeResponse {
  CrawlConfig config = 1;
}

// gRPC Service
service CrawlConfigService {
  // Commands
  rpc CreateCrawlConfig(CreateCrawlConfigRequest) returns (CreateCrawlConfigResponse);
  rpc UpdateCrawlConfig(UpdateCrawlConfigRequest) returns (UpdateCrawlConfigResponse);
  rpc ActivateCrawlConfig(ActivateCrawlConfigRequest) returns (ActivateCrawlConfigResponse);
  rpc DeactivateCrawlConfig(DeactivateCrawlConfigRequest) returns (DeactivateCrawlConfigResponse);
  rpc UpdateLastUsed(UpdateLastUsedRequest) returns (UpdateLastUsedResponse);
  rpc DeleteCrawlConfig(DeleteCrawlConfigRequest) returns (DeleteCrawlConfigResponse);

  // Queries
  rpc GetAllCrawlConfigs(GetAllCrawlConfigsRequest) returns (GetAllCrawlConfigsResponse);
  rpc GetCrawlConfigById(GetCrawlConfigByIdRequest) returns (GetCrawlConfigByIdResponse);
  rpc GetActiveCrawlConfigs(GetActiveCrawlConfigsRequest) returns (GetActiveCrawlConfigsResponse);
  rpc GetCrawlConfigsByMarketId(GetCrawlConfigsByMarketIdRequest) returns (GetCrawlConfigsByMarketIdResponse);
  rpc GetCrawlConfigsByType(GetCrawlConfigsByTypeRequest) returns (GetCrawlConfigsByTypeResponse);
  rpc GetActiveCrawlConfigsByMarket(GetActiveCrawlConfigsByMarketRequest) returns (GetActiveCrawlConfigsByMarketResponse);
  rpc GetActiveCrawlConfigsByType(GetActiveCrawlConfigsByTypeRequest) returns (GetActiveCrawlConfigsByTypeResponse);
  rpc GetActiveCrawlConfigsByMarketAndType(GetActiveCrawlConfigsByMarketAndTypeRequest) returns (GetActiveCrawlConfigsByMarketAndTypeResponse);
  rpc FilterCrawlConfigs(FilterCrawlConfigsRequest) returns (FilterCrawlConfigsResponse);
  rpc GetLeastUsedActiveConfig(GetLeastUsedActiveConfigRequest) returns (GetLeastUsedActiveConfigResponse);
  rpc GetLeastUsedActiveConfigByMarket(GetLeastUsedActiveConfigByMarketRequest) returns (GetLeastUsedActiveConfigByMarketResponse);
  rpc GetLeastUsedActiveConfigByType(GetLeastUsedActiveConfigByTypeRequest) returns (GetLeastUsedActiveConfigByTypeResponse);
}
