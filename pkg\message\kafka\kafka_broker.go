package kafka

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/segmentio/kafka-go"
	"go_core_market/pkg/message"
	"go_core_market/pkg/message/event"
	"go_core_market/pkg/message/serializer"
)

type KafkaBroker struct {
	brokers    []string
	writers    map[string]*kafka.Writer // Per-topic writers
	readers    map[string]*kafka.Reader
	readersMux sync.RWMutex
	writersMux sync.RWMutex
	connected  bool
	mu         sync.RWMutex
	serializer serializer.Serializer
	config     Config
}

type Config struct {
	Brokers        []string
	BatchSize      int
	BatchTimeout   time.Duration
	ReadBatchSize  int
	CommitInterval time.Duration
	StartOffset    int64
	GroupID        string
	Serializer     serializer.Serializer
	MaxRetries     int
	RetryBackoff   time.Duration
	ReadTimeout    time.Duration
	WriteTimeout   time.Duration
	// Thêm cấu hình để xử lý connection issues
	RequiredAcks    int
	Compression     kafka.Compression
	MaxMessageBytes int
}

func NewKafkaBroker(config Config) *KafkaBroker {
	// Set defaults
	if config.BatchSize == 0 {
		config.BatchSize = 1 // Giảm xuống 1 để test
	}
	if config.BatchTimeout == 0 {
		config.BatchTimeout = 100 * time.Millisecond
	}
	if config.ReadBatchSize == 0 {
		config.ReadBatchSize = 10
	}
	if config.CommitInterval == 0 {
		config.CommitInterval = 1 * time.Second
	}
	if config.StartOffset == 0 {
		config.StartOffset = kafka.LastOffset
	}
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.RetryBackoff == 0 {
		config.RetryBackoff = 100 * time.Millisecond
	}
	if config.ReadTimeout == 0 {
		config.ReadTimeout = 10 * time.Second
	}
	if config.WriteTimeout == 0 {
		config.WriteTimeout = 5 * time.Second
	}
	if config.RequiredAcks == 0 {
		config.RequiredAcks = 1
	}
	if config.MaxMessageBytes == 0 {
		config.MaxMessageBytes = 1000000 // 1MB
	}

	return &KafkaBroker{
		brokers:    config.Brokers,
		writers:    make(map[string]*kafka.Writer),
		readers:    make(map[string]*kafka.Reader),
		serializer: config.Serializer,
		config:     config,
	}
}

func (k *KafkaBroker) Connect(ctx context.Context) error {
	k.mu.Lock()
	defer k.mu.Unlock()

	if k.connected {
		return nil
	}

	log.Printf("Connecting to Kafka brokers: %v", k.brokers)

	// Test connection với timeout ngắn
	for i, broker := range k.brokers {
		dialCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		conn, err := kafka.DialContext(dialCtx, "tcp", broker)
		cancel()

		if err != nil {
			log.Printf("Failed to connect to broker %d (%s): %v", i, broker, err)
			continue
		}

		// Test thêm bằng cách get metadata
		partitions, err := conn.ReadPartitions()
		if err != nil {
			log.Printf("Failed to read partitions from broker %s: %v", broker, err)
			conn.Close()
			continue
		}

		log.Printf("Successfully connected to broker %s", broker)
		log.Printf("Available partitions: %d", len(partitions))

		// Log các topics có sẵn
		topicSet := make(map[string]bool)
		for _, partition := range partitions {
			topicSet[partition.Topic] = true
		}

		var topics []string
		for topic := range topicSet {
			topics = append(topics, topic)
		}
		log.Printf("Available topics: %v", topics)

		conn.Close()
		break
	}

	k.connected = true
	return nil
}

func (k *KafkaBroker) getOrCreateWriter(topic string) (*kafka.Writer, error) {
	k.writersMux.Lock()
	defer k.writersMux.Unlock()

	if writer, exists := k.writers[topic]; exists {
		return writer, nil
	}

	// Create new writer cho topic này
	writer := &kafka.Writer{
		Addr:     kafka.TCP(k.brokers...),
		Topic:    topic,
		Balancer: &kafka.LeastBytes{},

		// Cấu hình để xử lý connection issues
		BatchSize:    k.config.BatchSize,
		BatchTimeout: k.config.BatchTimeout,
		ReadTimeout:  k.config.ReadTimeout,
		WriteTimeout: k.config.WriteTimeout,
		Compression:  k.config.Compression,

		// Quan trọng: Async = false để đảm bảo error handling
		Async: false,

		// Error handling
		ErrorLogger: kafka.LoggerFunc(func(msg string, args ...interface{}) {
			log.Printf("Kafka Writer Error: "+msg, args...)
		}),
	}

	k.writers[topic] = writer
	log.Printf("Created new writer for topic: %s", topic)
	return writer, nil
}

func (k *KafkaBroker) Publish(ctx context.Context, evt event.Event) error {
	k.mu.RLock()
	if !k.connected {
		k.mu.RUnlock()
		return fmt.Errorf("broker is not connected")
	}
	k.mu.RUnlock()

	// Serialize event
	msg, err := k.serializer.Serialize(evt)
	if err != nil {
		return fmt.Errorf("failed to serialize event: %w", err)
	}

	// Get or create writer cho topic
	writer, err := k.getOrCreateWriter(msg.Topic)
	if err != nil {
		return fmt.Errorf("failed to get writer: %w", err)
	}

	// Convert to Kafka message
	kafkaMsg := k.messageToKafkaMessage(msg)

	log.Printf("Publishing event %s to topic %s", evt.GetID(), msg.Topic)
	log.Printf("Message key: %s, value length: %d", string(kafkaMsg.Key), len(kafkaMsg.Value))

	// Retry logic với exponential backoff
	var lastErr error
	for i := 0; i < k.config.MaxRetries; i++ {
		if i > 0 {
			backoff := time.Duration(i) * k.config.RetryBackoff
			log.Printf("Retrying publish attempt %d after %v", i+1, backoff)
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(backoff):
			}
		}

		// Create timeout context
		writeCtx, cancel := context.WithTimeout(ctx, k.config.WriteTimeout)
		err := writer.WriteMessages(writeCtx, kafkaMsg)
		cancel()

		if err == nil {
			log.Printf("Successfully published event %s to topic %s", evt.GetID(), msg.Topic)
			return nil
		}

		lastErr = err
		log.Printf("Publish attempt %d failed: %v", i+1, err)

		// Nếu là connection error, thử recreate writer
		if i < k.config.MaxRetries-1 {
			k.writersMux.Lock()
			if oldWriter, exists := k.writers[msg.Topic]; exists {
				oldWriter.Close()
				delete(k.writers, msg.Topic)
			}
			k.writersMux.Unlock()

			// Tạo writer mới
			writer, err = k.getOrCreateWriter(msg.Topic)
			if err != nil {
				log.Printf("Failed to recreate writer: %v", err)
				continue
			}
		}
	}

	return fmt.Errorf("failed to publish after %d retries: %w", k.config.MaxRetries, lastErr)
}

func (k *KafkaBroker) Subscribe(ctx context.Context, topic string, handler message.EventHandler) error {
	k.mu.RLock()
	if !k.connected {
		k.mu.RUnlock()
		return fmt.Errorf("broker is not connected")
	}
	k.mu.RUnlock()

	k.readersMux.Lock()
	defer k.readersMux.Unlock()

	if _, exists := k.readers[topic]; exists {
		return fmt.Errorf("subscription already exists for topic: %s", topic)
	}

	// Create reader với cấu hình cải tiến
	reader := kafka.NewReader(kafka.ReaderConfig{
		Brokers:        k.brokers,
		Topic:          topic,
		GroupID:        k.config.GroupID,
		StartOffset:    k.config.StartOffset,
		CommitInterval: k.config.CommitInterval,
		MinBytes:       1,
		MaxBytes:       10e6,

		// Error handling
		ErrorLogger: kafka.LoggerFunc(func(msg string, args ...interface{}) {
			log.Printf("Kafka Reader Error: "+msg, args...)
		}),
	})

	k.readers[topic] = reader
	go k.consumeMessages(ctx, topic, reader, handler)

	log.Printf("Subscribed to topic: %s", topic)
	return nil
}

func (k *KafkaBroker) consumeMessages(ctx context.Context, topic string, reader *kafka.Reader, handler message.EventHandler) {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered from panic in consumer for topic %s: %v", topic, r)
		}
	}()

	for {
		select {
		case <-ctx.Done():
			log.Printf("Context cancelled, stopping consumer for topic: %s", topic)
			return
		default:
			readCtx, cancel := context.WithTimeout(ctx, k.config.ReadTimeout)
			kafkaMsg, err := reader.ReadMessage(readCtx)
			cancel()

			if err != nil {
				if err == context.DeadlineExceeded {
					continue
				}
				if err == context.Canceled {
					return
				}
				log.Printf("Error reading message from topic %s: %v", topic, err)

				select {
				case <-ctx.Done():
					return
				case <-time.After(k.config.RetryBackoff):
					continue
				}
			}

			if err := k.processMessage(ctx, kafkaMsg, handler); err != nil {
				log.Printf("Error processing message from topic %s: %v", topic, err)
			}
		}
	}
}

func (k *KafkaBroker) processMessage(ctx context.Context, kafkaMsg kafka.Message, handler message.EventHandler) error {
	msg := k.kafkaMessageToMessage(kafkaMsg)

	evt, err := k.serializer.Deserialize(msg)
	if err != nil {
		return fmt.Errorf("failed to deserialize event: %w", err)
	}

	handlerCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	if err := handler(handlerCtx, evt); err != nil {
		return fmt.Errorf("handler error: %w", err)
	}

	log.Printf("Processed event %s from topic %s", evt.GetID(), evt.GetType())
	return nil
}

func (k *KafkaBroker) Disconnect(ctx context.Context) error {
	k.mu.Lock()
	defer k.mu.Unlock()

	if !k.connected {
		return nil
	}

	log.Println("Disconnecting from Kafka...")

	// Close all writers
	k.writersMux.Lock()
	for topic, writer := range k.writers {
		if err := writer.Close(); err != nil {
			log.Printf("Error closing writer for topic %s: %v", topic, err)
		}
	}
	k.writers = make(map[string]*kafka.Writer)
	k.writersMux.Unlock()

	// Close all readers
	k.readersMux.Lock()
	for topic, reader := range k.readers {
		if err := reader.Close(); err != nil {
			log.Printf("Error closing reader for topic %s: %v", topic, err)
		}
	}
	k.readers = make(map[string]*kafka.Reader)
	k.readersMux.Unlock()

	k.connected = false
	log.Println("Disconnected from Kafka")
	return nil
}

func (k *KafkaBroker) CloseSubscription(topic string) error {
	k.readersMux.Lock()
	defer k.readersMux.Unlock()

	reader, exists := k.readers[topic]
	if !exists {
		return fmt.Errorf("subscription for topic %s does not exist", topic)
	}

	if err := reader.Close(); err != nil {
		return fmt.Errorf("failed to close reader for topic %s: %w", topic, err)
	}

	delete(k.readers, topic)
	log.Printf("Closed subscription for topic: %s", topic)
	return nil
}

func (k *KafkaBroker) messageToKafkaMessage(msg *message.Message) kafka.Message {
	headers := make([]kafka.Header, 0, len(msg.Headers))
	for key, value := range msg.Headers {
		headers = append(headers, kafka.Header{
			Key:   key,
			Value: []byte(value),
		})
	}

	return kafka.Message{
		Key:     msg.Key,
		Value:   msg.Value,
		Headers: headers,
	}
}

func (k *KafkaBroker) kafkaMessageToMessage(kafkaMsg kafka.Message) *message.Message {
	headers := make(map[string]string)
	for _, header := range kafkaMsg.Headers {
		headers[header.Key] = string(header.Value)
	}

	contentType := "application/json"
	if ct, exists := headers["content_type"]; exists {
		contentType = ct
	}

	return &message.Message{
		Topic:       kafkaMsg.Topic,
		Key:         kafkaMsg.Key,
		Value:       kafkaMsg.Value,
		ContentType: contentType,
		Headers:     headers,
	}
}

// Utility methods
func (k *KafkaBroker) HealthCheck(ctx context.Context) error {
	k.mu.RLock()
	defer k.mu.RUnlock()

	if !k.connected {
		return fmt.Errorf("broker is not connected")
	}

	dialCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	conn, err := kafka.DialContext(dialCtx, "tcp", k.brokers[0])
	if err != nil {
		return fmt.Errorf("health check failed: %w", err)
	}
	defer conn.Close()

	return nil
}

func (k *KafkaBroker) GetStats() map[string]interface{} {
	k.readersMux.RLock()
	k.writersMux.RLock()
	defer k.readersMux.RUnlock()
	defer k.writersMux.RUnlock()

	return map[string]interface{}{
		"connected":      k.connected,
		"active_readers": len(k.readers),
		"active_writers": len(k.writers),
		"brokers":        k.brokers,
	}
}
