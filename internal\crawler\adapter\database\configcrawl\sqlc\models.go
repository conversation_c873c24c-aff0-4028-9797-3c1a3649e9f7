// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type CrawlConfig struct {
	ID                     int                `db:"id" json:"id"`
	MarketID               int                `db:"market_id" json:"market_id"`
	Description            string             `db:"description" json:"description"`
	TypeConfig             string             `db:"type_config" json:"type_config"`
	RequestsPerMinute      int                `db:"requests_per_minute" json:"requests_per_minute"`
	RequireAuth            bool               `db:"require_auth" json:"require_auth"`
	MaxNumberAuth          int                `db:"max_number_auth" json:"max_number_auth"`
	MaxNumberProxy         int                `db:"max_number_proxy" json:"max_number_proxy"`
	PerRequestDelaySeconds int                `db:"per_request_delay_seconds" json:"per_request_delay_seconds"`
	TimeoutSeconds         int                `db:"timeout_seconds" json:"timeout_seconds"`
	MaxRetries             int                `db:"max_retries" json:"max_retries"`
	RetryDelaySeconds      int32              `db:"retry_delay_seconds" json:"retry_delay_seconds"`
	MaxConcurrent          int32              `db:"max_concurrent" json:"max_concurrent"`
	IsActive               bool               `db:"is_active" json:"is_active"`
	LastUsedAt             pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
	CreatedAt              pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt              pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}
