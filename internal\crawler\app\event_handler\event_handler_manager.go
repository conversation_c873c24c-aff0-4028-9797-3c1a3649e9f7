package eventhandler

import (
	"context"
	"go_core_market/internal/crawler/app/service"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
	"go_core_market/pkg/message/event"
)

// EventHandlerManager manages all event handlers for the crawler service
type EventHandlerManager struct {
	broker             message.Broker
	jobStartHandler    *JobStartEventHandler
	logger            logger.Logger
}

// NewEventHandlerManager creates a new event handler manager
func NewEventHandlerManager(
	broker message.Broker,
	crawlService service.CrawlService,
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries,
	jobQueries *queryjob.JobQueries,
	logger logger.Logger,
) *EventHandlerManager {
	jobStartHandler := NewJobStartEventHandler(
		crawlService,
		crawlConfigQueries,
		jobQueries,
		logger,
	)

	return &EventHandlerManager{
		broker:          broker,
		jobStartHandler: jobStartHandler,
		logger:         logger,
	}
}

// RegisterHandlers registers all event handlers with the message broker
func (m *EventHandlerManager) RegisterHandlers(ctx context.Context) error {
	m.logger.Info("Registering event handlers")

	// Register job start event handler
	err := m.broker.Subscribe(ctx, "job.events", m.handleJobEvents)
	if err != nil {
		return err
	}

	m.logger.Info("Event handlers registered successfully")
	return nil
}

// handleJobEvents handles all job-related events
func (m *EventHandlerManager) handleJobEvents(ctx context.Context, evt event.Event) error {
	m.logger.Debug("Received job event", "event_type", evt.GetType(), "event_id", evt.GetID())

	switch evt.GetType() {
	case event.JobStartedEventType:
		return m.jobStartHandler.Handle(ctx, evt)
	default:
		m.logger.Debug("Unhandled job event type", "event_type", evt.GetType())
		return nil
	}
}

// Shutdown gracefully shuts down the event handler manager
func (m *EventHandlerManager) Shutdown(ctx context.Context) error {
	m.logger.Info("Shutting down event handler manager")
	
	// Close job events subscription
	err := m.broker.CloseSubscription("job.events")
	if err != nil {
		m.logger.Error("Failed to close job events subscription", "error", err)
		return err
	}

	m.logger.Info("Event handler manager shutdown completed")
	return nil
}
