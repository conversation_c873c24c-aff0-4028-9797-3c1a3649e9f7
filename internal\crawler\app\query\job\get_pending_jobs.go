package queryjob

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetPendingJobsQuery struct {
	// No parameters needed
}

type GetPendingJobsQueryHandler decorator.QueryHandler[GetPendingJobsQuery, []*Job]

type getPendingJobsQueryHandler struct {
	repo JobReadModel
}

func NewGetPendingJobsQueryHandler(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetPendingJobsQueryHandler {

	return decorator.ApplyQueryDecorators[GetPendingJobsQuery, []*Job](
		getPendingJobsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getPendingJobsQueryHandler) Handle(ctx context.Context, query GetPendingJobsQuery) ([]*Job, error) {
	return h.repo.GetPendingJobs(ctx)
}
