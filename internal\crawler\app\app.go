package app

import (
	"context"
	"go_core_market/internal/crawler/app/command/authcrawl"
	"go_core_market/internal/crawler/app/command/crawlconfig"
	"go_core_market/internal/crawler/app/command/job"
	"go_core_market/internal/crawler/app/command/market"
	"go_core_market/internal/crawler/app/command/proxy"
	queryauthcrawl "go_core_market/internal/crawler/app/query/authcrawl"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	querymarket "go_core_market/internal/crawler/app/query/market"
	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	"go_core_market/pkg/database"
	"go_core_market/pkg/message"
)

type Application struct {
	Commands CrawlerCommands
	Queries  CrawlerQueries
	Db       database.DatabaseManager
	Broker   message.Broker
}

// CrawlerCommands contains all command handlers for crawler service
type CrawlerCommands struct {
	Market      commandmarket.MarketCommands
	Proxy       commandproxy.ProxyCommands
	AuthCrawl   commandauthcrawl.AuthCrawlCommands
	Job         commandjob.JobCommands
	CrawlConfig commandcrawlconfig.CrawlConfigCommands
}

// CrawlerQueries contains all query handlers for crawler service
type CrawlerQueries struct {
	Market      querymarket.MarketQueries
	Proxy       queryproxy.ProxyQueries
	AuthCrawl   queryauthcrawl.AuthCrawlQueries
	Job         *queryjob.JobQueries
	CrawlConfig *querycrawlconfig.CrawlConfigQueries
}

func (a *Application) CloseApp(ctx context.Context) error {
	err := a.Db.Close()
	if err != nil {
		return err
	}
	err = a.Broker.Disconnect(ctx)
	if err != nil {
		return err
	}
	return nil
}
