package app

import (
	"context"
	"go_core_market/internal/crawler/app/command/authcrawl"
	"go_core_market/internal/crawler/app/command/crawlconfig"
	"go_core_market/internal/crawler/app/command/job"
	"go_core_market/internal/crawler/app/command/market"
	"go_core_market/internal/crawler/app/command/proxy"
	eventhandler "go_core_market/internal/crawler/app/event_handler"
	"go_core_market/internal/crawler/app/service"
	queryauthcrawl "go_core_market/internal/crawler/app/query/authcrawl"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	querymarket "go_core_market/internal/crawler/app/query/market"
	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	"go_core_market/pkg/database"
	"go_core_market/pkg/message"
)

type Application struct {
	Commands     CrawlerCommands
	Queries      CrawlerQueries
	Services     CrawlerServices
	EventManager *eventhandler.EventHandlerManager
	Db           database.DatabaseManager
	Broker       message.Broker
}

// CrawlerCommands contains all command handlers for crawler service
type CrawlerCommands struct {
	Market      commandmarket.MarketCommands
	Proxy       commandproxy.ProxyCommands
	AuthCrawl   commandauthcrawl.AuthCrawlCommands
	Job         commandjob.JobCommands
	CrawlConfig commandcrawlconfig.CrawlConfigCommands
}

// CrawlerQueries contains all query handlers for crawler service
type CrawlerQueries struct {
	Market      querymarket.MarketQueries
	Proxy       queryproxy.ProxyQueries
	AuthCrawl   queryauthcrawl.AuthCrawlQueries
	Job         *queryjob.JobQueries
	CrawlConfig *querycrawlconfig.CrawlConfigQueries
}

// CrawlerServices contains all business services for crawler
type CrawlerServices struct {
	CrawlService service.CrawlService
}

// StartEventHandlers starts the event handler manager
func (a *Application) StartEventHandlers(ctx context.Context) error {
	if a.EventManager != nil {
		return a.EventManager.RegisterHandlers(ctx)
	}
	return nil
}

func (a *Application) CloseApp(ctx context.Context) error {
	// Shutdown event handlers first
	if a.EventManager != nil {
		err := a.EventManager.Shutdown(ctx)
		if err != nil {
			return err
		}
	}

	err := a.Db.Close()
	if err != nil {
		return err
	}
	err = a.Broker.Disconnect(ctx)
	if err != nil {
		return err
	}
	return nil
}
