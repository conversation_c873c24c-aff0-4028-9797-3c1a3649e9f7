package buff163

import (
	"context"
	"fmt"
	"log"
	"time"
)

// GetCurrentHeaderSetInfo returns information about the currently used header set
func (c *Buff163Crawler) GetCurrentHeaderSetInfo() (int, string) {
	index := c.headerIndex % len(defaultHeadersSets)
	headerSetNames := []string{
		"Samsung Galaxy S21", "iPhone 13 Pro", "Xiaomi Mi 11", "OnePlus 9 Pro", "Google Pixel 6",
		"Huawei P40 Pro", "Oppo Find X3 Pro", "Vivo X70 Pro", "Realme GT", "Sony Xperia 1 III",
		"LG V60 ThinQ", "Motorola Edge+", "Nokia 8.3 5G", "Asus ROG Phone 5", "Nothing Phone (1)",
		"Fairphone 4", "BlackBerry KEY2", "Essential Phone", "Red Magic 6 Pro", "Razer Phone 2",
	}
	return index + 1, headerSetNames[index]
}

// SetHeaderSet manually sets a specific header set (0-19)
func (c *Buff163Crawler) SetHeaderSet(index int) {
	c.rotationMutex.Lock()
	defer c.rotationMutex.Unlock()
	if index >= 0 && index < len(defaultHeadersSets) {
		c.headerIndex = index
		c.setDefaultHeaders()
	}
}

func (c *Buff163Crawler) rotateCredentials() {
	c.rotationMutex.Lock()
	defer c.rotationMutex.Unlock()

	// Rotate headers
	c.headerIndex = (c.headerIndex + 1) % len(defaultHeadersSets)
	c.setDefaultHeaders()

	if len(c.config.Cookies) > 0 {
		c.client.SetCookie(c.config.Cookies[c.cookieIndex])
		c.cookieIndex = (c.cookieIndex + 1) % len(c.config.Cookies)
	}
	if len(c.config.APIKeys) > 0 {
		c.client.SetAPIKey(c.config.APIKeys[c.apiKeyIndex])
		c.apiKeyIndex = (c.apiKeyIndex + 1) % len(c.config.APIKeys)
	}
	if len(c.config.Proxies) > 0 {
		c.client.SetProxy(c.config.Proxies[c.proxyIndex])
		c.proxyIndex = (c.proxyIndex + 1) % len(c.config.Proxies)
	}
}

func (c *Buff163Crawler) makeRequest(ctx context.Context, path string, params map[string]string) ([]byte, error) {
	// Enforce rate limit trước khi thực hiện request
	c.requestMutex.Lock()
	if c.config.PerRequestDelay > 0 {
		delay := time.Duration(c.config.PerRequestDelay) * time.Second
		elapsed := time.Since(c.lastRequestTime)
		if elapsed < delay {
			time.Sleep(delay - elapsed)
		}
	}
	c.requestMutex.Unlock()

	var lastErr error
	for attempt := 0; attempt <= c.config.MaxRetries; attempt++ {
		c.rotateCredentials()

		data, err := c.client.Get(path, params)
		if err == nil {
			// Sau khi nhận được phản hồi thành công thì cập nhật lastRequestTime
			c.requestMutex.Lock()
			c.lastRequestTime = time.Now()
			c.requestMutex.Unlock()

			return data, nil
		}
		log.Println(err)
		lastErr = err
		if attempt < c.config.MaxRetries {
			delay := time.Duration(c.config.RetryDelay) * time.Second
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	}
	return nil, fmt.Errorf("request failed after %d attempts: %w", c.config.MaxRetries+1, lastErr)
}

func (c *Buff163Crawler) validateAPIResponse(code string, msg *string) error {
	if code != "OK" {
		log.Println(code)
		if msg != nil {
			return fmt.Errorf("API error: %s", *msg)
		}
		return fmt.Errorf("API error")
	}
	return nil
}
