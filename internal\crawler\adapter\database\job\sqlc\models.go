// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type Job struct {
	ID                 int                `db:"id" json:"id"`
	JobType            string             `db:"job_type" json:"job_type"`
	Name               string             `db:"name" json:"name"`
	Description        *string            `db:"description" json:"description"`
	Status             string             `db:"status" json:"status"`
	CrawlConfigID      *int32             `db:"crawl_config_id" json:"crawl_config_id"`
	ScheduledAt        pgtype.Timestamptz `db:"scheduled_at" json:"scheduled_at"`
	StartedAt          pgtype.Timestamptz `db:"started_at" json:"started_at"`
	CompletedAt        pgtype.Timestamptz `db:"completed_at" json:"completed_at"`
	CurrentStep        *int32             `db:"current_step" json:"current_step"`
	TotalSteps         *int32             `db:"total_steps" json:"total_steps"`
	ProgressMessage    *string            `db:"progress_message" json:"progress_message"`
	ProgressPercentage *int32             `db:"progress_percentage" json:"progress_percentage"`
	MaxRetries         int                `db:"max_retries" json:"max_retries"`
	RetryCount         int                `db:"retry_count" json:"retry_count"`
	LastError          *string            `db:"last_error" json:"last_error"`
	TimeoutSeconds     int                `db:"timeout_seconds" json:"timeout_seconds"`
	CreatedBy          string             `db:"created_by" json:"created_by"`
	CreatedAt          pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt          pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}
