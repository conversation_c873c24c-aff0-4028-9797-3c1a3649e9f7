package event

import (
	"go_core_market/internal/crawler/domain/value_object"
)

// ConvertCrawledPricesToDTOs converts domain CrawledPrice objects to DTOs
func ConvertCrawledPricesToDTOs(prices []*value_object.CrawledPrice) []CrawledPriceDTO {
	if prices == nil {
		return nil
	}
	
	dtos := make([]CrawledPriceDTO, len(prices))
	for i, price := range prices {
		dtos[i] = ConvertCrawledPriceToDTOFromDomain(price)
	}
	return dtos
}

// ConvertCrawledItemsToDTOs converts domain CrawledItem objects to DTOs
func ConvertCrawledItemsToDTOs(items []*value_object.CrawledItem) []CrawledItemDTO {
	if items == nil {
		return nil
	}
	
	dtos := make([]CrawledItemDTO, len(items))
	for i, item := range items {
		dtos[i] = ConvertCrawledItemToDTOFromDomain(item)
	}
	return dtos
}

// ConvertCrawledPriceToDTOFromDomain converts a single domain CrawledPrice to DTO
func ConvertCrawledPriceToDTOFromDomain(price *value_object.CrawledPrice) CrawledPriceDTO {
	if price == nil {
		return CrawledPriceDTO{}
	}
	
	var conditionDTO ConditionDTO
	if price.Condition != nil {
		conditionDTO = ConvertConditionToDTOFromDomain(price.Condition)
	}
	
	return CrawledPriceDTO{
		ItemName:   price.ItemName,
		MarketID:   price.MarketID,
		SellPrice:  price.SellPrice,
		BuyPrice:   price.BuyPrice,
		NumSell:    price.NumSell,
		NumBuy:     price.NumBuy,
		Condition:  conditionDTO,
		RecordedAt: price.RecordedAt,
	}
}

// ConvertCrawledItemToDTOFromDomain converts a single domain CrawledItem to DTO
func ConvertCrawledItemToDTOFromDomain(item *value_object.CrawledItem) CrawledItemDTO {
	if item == nil {
		return CrawledItemDTO{}
	}
	
	tags := make([]TagDTO, len(item.Tags))
	for i, tag := range item.Tags {
		tags[i] = ConvertTagToDTOFromDomain(tag)
	}
	
	return CrawledItemDTO{
		Name:            item.Name,
		ShortName:       item.ShortName,
		IconURL:         item.IconURL,
		OriginalIconURL: item.OriginalIconURL,
		Description:     item.Description,
		SteamMarketURL:  item.SteamMarketURL,
		Game:            item.Game,
		Tags:            tags,
		MarketID:        item.MarketID,
	}
}

// ConvertConditionToDTOFromDomain converts domain Condition interface to DTO
func ConvertConditionToDTOFromDomain(condition value_object.Condition) ConditionDTO {
	if condition == nil {
		return ConditionDTO{}
	}
	
	dto := ConditionDTO{
		Name: condition.GetCondition(),
		Type: condition.GetType(),
	}
	
	// Handle specific condition types
	switch cond := condition.(type) {
	case *value_object.ConditionFloat:
		dto.FloatMin = cond.FloatMin
		dto.FloatMax = cond.FloatMax
	case *value_object.ConditionFade:
		dto.FadeMin = cond.FadeMin
		dto.FadeMax = cond.FadeMax
	case *value_object.ConditionDoppler:
		// Doppler only has name, already set above
	}
	
	return dto
}

// ConvertConditionsToDTOs converts slice of domain Conditions to DTOs
func ConvertConditionsToDTOs(conditions []value_object.Condition) []ConditionDTO {
	if conditions == nil {
		return nil
	}
	
	dtos := make([]ConditionDTO, len(conditions))
	for i, condition := range conditions {
		dtos[i] = ConvertConditionToDTOFromDomain(condition)
	}
	return dtos
}

// ConvertTagToDTOFromDomain converts domain Tag to DTO
func ConvertTagToDTOFromDomain(tag value_object.Tag) TagDTO {
	return TagDTO{
		InternalName:  tag.InternalName,
		LocalizedName: tag.LocalizedName,
		TagType:       tag.TagType,
	}
}

// ConvertDopplerConditionsToDTOs converts ConditionDoppler slice to DTOs
func ConvertDopplerConditionsToDTOs(conditions []value_object.ConditionDoppler) []ConditionDTO {
	if conditions == nil {
		return nil
	}
	
	dtos := make([]ConditionDTO, len(conditions))
	for i, condition := range conditions {
		dtos[i] = ConditionDTO{
			Name: condition.Name,
			Type: "doppler",
		}
	}
	return dtos
}

// ConvertFloatConditionsToDTOs converts ConditionFloat slice to DTOs
func ConvertFloatConditionsToDTOs(conditions []value_object.ConditionFloat) []ConditionDTO {
	if conditions == nil {
		return nil
	}
	
	dtos := make([]ConditionDTO, len(conditions))
	for i, condition := range conditions {
		dtos[i] = ConditionDTO{
			Name:     condition.Name,
			Type:     "float",
			FloatMin: condition.FloatMin,
			FloatMax: condition.FloatMax,
		}
	}
	return dtos
}

// ConvertFadeConditionsToDTOs converts ConditionFade slice to DTOs
func ConvertFadeConditionsToDTOs(conditions []value_object.ConditionFade) []ConditionDTO {
	if conditions == nil {
		return nil
	}
	
	dtos := make([]ConditionDTO, len(conditions))
	for i, condition := range conditions {
		dtos[i] = ConditionDTO{
			Name:    condition.Name,
			Type:    "fade",
			FadeMin: condition.FadeMin,
			FadeMax: condition.FadeMax,
		}
	}
	return dtos
}
