package clienthttp

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"github.com/go-resty/resty/v2"
	"net/http"
	"net/http/cookiejar"
	"time"
)

type RestyClient struct {
	client *resty.Client
}

// NewClient tạo một instance mới của Client
func NewRestyClient() IClient {
	client := resty.New()

	// ⚠️ Tắt HTTP/2 để tránh lộ fingerprint "Golang" (dùng HTTP/1.1)
	client.SetTransport(&http.Transport{
		TLSNextProto: make(map[string]func(string, *tls.Conn) http.RoundTripper),
	})

	// (Optional) Gắn CookieJar để giữ session giống browser
	jar, err := cookiejar.New(nil)
	if err != nil {
		panic(err)
	}
	client.SetCookieJar(jar)

	client.SetTimeout(15 * time.Second) // Đặt timeout mặc định

	restyClient := &RestyClient{client: client}
	return restyClient
}

// Get thực hiện HTTP GET request
func (c *RestyClient) Get(path string, params map[string]string) ([]byte, error) {
	req := c.client.R()
	if transport, ok := c.client.GetClient().Transport.(*http.Transport); ok && transport.Proxy != nil {
		req := &http.Request{} // có thể để nil hoặc tạo dummy request
		proxyFunc := transport.Proxy

		// Gọi proxy func để lấy URL proxy hiện tại
		url, err := proxyFunc(req)
		if err != nil {
			fmt.Println("Lỗi lấy proxy:", err)
		} else if url != nil {
			fmt.Println("Proxy đang dùng:", url.String())
		} else {
			fmt.Println("Không có proxy nào được set")
		}
	}
	// Thêm query parameters nếu có
	if params != nil {
		req.SetQueryParams(params)
	}

	resp, err := req.Get(path)
	if err != nil {
		return nil, err
	}

	// Kiểm tra status code
	if resp.IsError() {
		return nil, fmt.Errorf("HTTP error: %d - %s", resp.StatusCode(), string(resp.Body()))
	}
	return resp.Body(), nil
}

// Post thực hiện HTTP POST request
func (c *RestyClient) Post(path string, params map[string]string, body interface{}) ([]byte, error) {
	req := c.client.R()

	// Thêm query parameters nếu có
	if params != nil {
		req.SetQueryParams(params)
	}

	// Set body nếu có
	if body != nil {
		// Kiểm tra nếu body là string/[]byte thì set trực tiếp, không thì marshal JSON
		switch v := body.(type) {
		case string:
			req.SetBody(v)
			req.SetHeader("Content-Type", "application/json")
		case []byte:
			req.SetBody(v)
			req.SetHeader("Content-Type", "application/json")
		default:
			// Marshal sang JSON
			jsonBody, err := json.Marshal(body)
			if err != nil {
				return nil, fmt.Errorf("failed to marshal body: %v", err)
			}
			req.SetBody(jsonBody)
			req.SetHeader("Content-Type", "application/json")
		}
	}

	resp, err := req.Post(path)
	if err != nil {
		return nil, err
	}

	// Kiểm tra status code
	if resp.IsError() {
		return nil, fmt.Errorf("HTTP error: %d - %s", resp.StatusCode(), string(resp.Body()))
	}

	return resp.Body(), nil
}

// SetBaseURL thiết lập base URL cho client
func (c *RestyClient) SetBaseURL(baseURL string) {
	c.client.SetBaseURL(baseURL)
}

// SetProxy thiết lập proxy cho client
func (c *RestyClient) SetProxy(proxy string) {
	c.client.SetProxy(proxy)
}

// SetCookie thiết lập cookie cho client
func (c *RestyClient) SetCookie(cookie string) {
	c.client.SetHeader("Cookie", cookie)
}

// SetHeader thiết lập header cho client
func (c *RestyClient) SetHeader(key, value string) {
	c.client.SetHeader(key, value)
}

// SetAuthWithJwt thiết lập JWT authentication
func (c *RestyClient) SetAPIKey(apkey string) {
	c.client.SetHeader("Authorization", "Bearer "+apkey)
}
func (c *RestyClient) SetTimeout(timeout time.Duration) {
	c.client.SetTimeout(timeout) // ← sửa thành gọi đúng
}
