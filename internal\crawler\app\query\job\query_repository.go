package queryjob

import (
	"context"
)

type JobReadModel interface {
	Get<PERSON><PERSON><PERSON><PERSON><PERSON>(ctx context.Context, query GetAllJobsQuery) ([]*Job, error)
	GetJobById(ctx context.Context, id int) (*Job, error)
	GetJobsByStatus(ctx context.Context, status string) ([]*Job, error)
	GetJobsByType(ctx context.Context, jobType string) ([]*Job, error)
	GetPendingJobs(ctx context.Context) ([]*Job, error)
	GetRunningJobs(ctx context.Context) ([]*Job, error)
	GetJobsByConfigId(ctx context.Context, configId int) ([]*Job, error)
	FilterJobs(ctx context.Context, query FilterJobsQuery) ([]*Job, error)
	GetJobsNeedingRetry(ctx context.Context) ([]*Job, error)
	GetExpiredJobs(ctx context.Context) ([]*Job, error)
	CountJobs(ctx context.Context) (int64, error)
	CountJobsByStatus(ctx context.Context, status string) (int64, error)
}
