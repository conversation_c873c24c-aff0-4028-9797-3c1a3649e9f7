package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
	"time"
)

type CreateAuth struct {
	MarketID  int
	AuthType  string
	Value     string
	IsActive  bool
	ExpiredAt *time.Time
	CreatedBy string
}

type CreateAuthHandler decorator.CommandHandler[CreateAuth]

type createAuthHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewCreateAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateAuth](
		createAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createAuthHandler) Handle(ctx context.Context, cmd CreateAuth) error {
	// Create domain auth
	authParams := entity.AuthParams{
		MarketID:  cmd.MarketID,
		AuthType:  cmd.AuthType,
		Value:     cmd.Value,
		IsActive:  cmd.IsActive,
		ExpiredAt: cmd.ExpiredAt,
	}

	auth, err := entity.NewAuth(authParams)
	if err != nil {
		return err
	}
	err = h.repo.Create(ctx, auth)
	if err != nil {
		return err
	}
	return nil
}
