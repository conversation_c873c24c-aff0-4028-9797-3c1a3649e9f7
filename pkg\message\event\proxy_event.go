package event

import "time"

const (
	ProxyCreatedEventType     = "proxy.created"
	ProxyUsedEventType        = "proxy.used"
	ProxyReleasedEventType    = "proxy.released"
	ProxyDeactivatedEventType = "proxy.deactivated"
	ProxyActivatedEventType   = "proxy.activated"
	ProxyUpdatedEventType     = "proxy.updated"
)

// ====== Proxy Created Event ======
type ProxyCreatedData struct {
	ProxyID   int       `json:"proxy_id"`
	Host      string    `json:"host"`
	Port      int       `json:"port"`
	UserName  string    `json:"user_name,omitempty"`
	Password  string    `json:"password,omitempty"`
	Speed     int       `json:"speed"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
}

type ProxyCreatedEvent struct {
	*BaseEvent[ProxyCreatedData]
}

func NewProxyCreatedEvent(aggregateID string, data ProxyCreatedData) *ProxyCreatedEvent {
	return &ProxyCreatedEvent{
		BaseEvent: NewBaseEvent(ProxyCreatedEventType, aggregateID, data),
	}
}

// ====== Proxy Used Event ======
type ProxyUsedData struct {
	ProxyID   int       `json:"proxy_id"`
	Host      string    `json:"host"`
	Port      int       `json:"port"`
	UsedAt    time.Time `json:"used_at"`
	UsedBy    string    `json:"used_by,omitempty"`    // Optional: service/process that used it
	Purpose   string    `json:"purpose,omitempty"`    // Optional: what it was used for
	RequestID string    `json:"request_id,omitempty"` // Optional: trace request
}

type ProxyUsedEvent struct {
	*BaseEvent[ProxyUsedData]
}

func NewProxyUsedEvent(aggregateID string, data ProxyUsedData) *ProxyUsedEvent {
	return &ProxyUsedEvent{
		BaseEvent: NewBaseEvent(ProxyUsedEventType, aggregateID, data),
	}
}

// ====== Proxy Released Event ======
type ProxyReleasedData struct {
	ProxyID    int       `json:"proxy_id"`
	Host       string    `json:"host"`
	Port       int       `json:"port"`
	ReleasedAt time.Time `json:"released_at"`
	ReleasedBy string    `json:"released_by,omitempty"` // Optional: service/process that released it
	Duration   int64     `json:"duration,omitempty"`    // Optional: how long it was used (in seconds)
	Success    bool      `json:"success"`               // Whether the usage was successful
	ErrorMsg   string    `json:"error_msg,omitempty"`   // Optional: error message if failed
}

type ProxyReleasedEvent struct {
	*BaseEvent[ProxyReleasedData]
}

func NewProxyReleasedEvent(aggregateID string, data ProxyReleasedData) *ProxyReleasedEvent {
	return &ProxyReleasedEvent{
		BaseEvent: NewBaseEvent(ProxyReleasedEventType, aggregateID, data),
	}
}

// ====== Proxy Deactivated Event ======
type ProxyDeactivatedData struct {
	ProxyID       int       `json:"proxy_id"`
	Host          string    `json:"host"`
	Port          int       `json:"port"`
	DeactivatedAt time.Time `json:"deactivated_at"`
	DeactivatedBy string    `json:"deactivated_by,omitempty"` // Optional: who/what deactivated it
	Reason        string    `json:"reason,omitempty"`         // Optional: reason for deactivation (error, blocked, etc.)
}

type ProxyDeactivatedEvent struct {
	*BaseEvent[ProxyDeactivatedData]
}

func NewProxyDeactivatedEvent(aggregateID string, data ProxyDeactivatedData) *ProxyDeactivatedEvent {
	return &ProxyDeactivatedEvent{
		BaseEvent: NewBaseEvent(ProxyDeactivatedEventType, aggregateID, data),
	}
}

// ====== Proxy Activated Event ======
type ProxyActivatedData struct {
	ProxyID     int       `json:"proxy_id"`
	Host        string    `json:"host"`
	Port        int       `json:"port"`
	ActivatedAt time.Time `json:"activated_at"`
	ActivatedBy string    `json:"activated_by,omitempty"` // Optional: who/what activated it
	Reason      string    `json:"reason,omitempty"`       // Optional: reason for activation
}

type ProxyActivatedEvent struct {
	*BaseEvent[ProxyActivatedData]
}

func NewProxyActivatedEvent(aggregateID string, data ProxyActivatedData) *ProxyActivatedEvent {
	return &ProxyActivatedEvent{
		BaseEvent: NewBaseEvent(ProxyActivatedEventType, aggregateID, data),
	}
}

// ====== Proxy Updated Event ======
type ProxyUpdatedData struct {
	ProxyID   int                    `json:"proxy_id"`
	Host      string                 `json:"host"`
	Port      int                    `json:"port"`
	UpdatedAt time.Time              `json:"updated_at"`
	UpdatedBy string                 `json:"updated_by,omitempty"` // Optional: who/what updated it
	Changes   map[string]interface{} `json:"changes,omitempty"`    // Optional: what fields were changed
}

type ProxyUpdatedEvent struct {
	*BaseEvent[ProxyUpdatedData]
}

func NewProxyUpdatedEvent(aggregateID string, data ProxyUpdatedData) *ProxyUpdatedEvent {
	return &ProxyUpdatedEvent{
		BaseEvent: NewBaseEvent(ProxyUpdatedEventType, aggregateID, data),
	}
}
