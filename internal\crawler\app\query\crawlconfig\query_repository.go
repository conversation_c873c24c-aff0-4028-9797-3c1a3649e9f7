package querycrawlconfig

import (
	"context"
)

type CrawlConfigReadModel interface {
	GetAllCrawlConfigs(ctx context.Context, query GetAllCrawlConfigsQuery) ([]*CrawlConfig, error)
	GetCrawlConfigById(ctx context.Context, id int) (*CrawlConfig, error)
	GetActiveCrawlConfigs(ctx context.Context) ([]*CrawlConfig, error)
	GetCrawlConfigsByMarketId(ctx context.Context, marketId int) ([]*CrawlConfig, error)
	GetCrawlConfigsByType(ctx context.Context, typeConfig string) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsByMarket(ctx context.Context, marketId int) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsByType(ctx context.Context, typeConfig string) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsByMarketAndType(ctx context.Context, marketId int, typeConfig string) ([]*CrawlConfig, error)
	FilterCrawlConfigs(ctx context.Context, query FilterCrawlConfigsQuery) ([]*CrawlConfig, error)
	GetLeastUsedActiveConfig(ctx context.Context) (*CrawlConfig, error)
	GetLeastUsedActiveConfigByMarket(ctx context.Context, marketId int) (*CrawlConfig, error)
	GetLeastUsedActiveConfigByType(ctx context.Context, typeConfig string) (*CrawlConfig, error)
	CountCrawlConfigs(ctx context.Context) (int64, error)
	CountActiveCrawlConfigs(ctx context.Context) (int64, error)
	CountCrawlConfigsByMarket(ctx context.Context, marketId int) (int64, error)
}
