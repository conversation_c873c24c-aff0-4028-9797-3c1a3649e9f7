package queryjob

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"time"
)

type FilterJobsQuery struct {
	JobType       string
	Status        string
	CrawlConfigId int
	CreatedBy     string
	CreatedFrom   *time.Time
	CreatedTo     *time.Time
	OrderBy       string
}

type FilterJobsQueryHandler decorator.QueryHandler[FilterJobsQuery, []*Job]

type filterJobsQueryHandler struct {
	repo JobReadModel
}

func NewFilterJobsQueryHandler(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) FilterJobsQueryHandler {

	return decorator.ApplyQueryDecorators[FilterJobsQuery, []*Job](
		filterJobsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h filterJobsQueryHandler) Handle(ctx context.Context, query FilterJobsQuery) ([]*Job, error) {
	return h.repo.FilterJobs(ctx, query)
}
