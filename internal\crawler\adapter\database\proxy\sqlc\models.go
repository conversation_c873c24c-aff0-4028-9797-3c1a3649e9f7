// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type Proxy struct {
	ID         int32              `db:"id" json:"id"`
	Host       string             `db:"host" json:"host"`
	Port       int32              `db:"port" json:"port"`
	UserName   string             `db:"user_name" json:"user_name"`
	Password   string             `db:"password" json:"password"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
	CreatedAt  pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt  pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}
