package youpin

import (
	"context"
	"encoding/json"
	"fmt"
	"go_core_market/internal/crawler/adapter/clienthttp"
	"go_core_market/internal/crawler/domain/value_object"
	"sync"
	"time"
)

type YoupinCrawler struct {
	client          clienthttp.IClient
	config          *value_object.Config
	lastRequestTime time.Time
	requestMutex    sync.Mutex
	cookieIndex     int
	apiKeyIndex     int
	proxyIndex      int
	headerIndex     int
	rotationMutex   sync.RWMutex
}

func NewYoupinCrawler(client clienthttp.IClient) *YoupinCrawler {
	crawler := &YoupinCrawler{client: client}
	crawler.setDefaultHeaders()
	return crawler
}

func (c *YoupinCrawler) setDefaultHeaders() {
	// Get current header set based on headerIndex
	currentHeaders := defaultHeadersSets[c.headerIndex%len(defaultHeadersSets)]
	for k, v := range currentHeaders {
		c.client.SetHeader(k, v)
	}
}

func (y *YoupinCrawler) SetConfig(config *value_object.Config) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}
	y.config = config
	return nil
}
func (c *YoupinCrawler) CrawlNormal(ctx context.Context, page int) ([]*value_object.CrawledPrice, int, error) {
	requestBody := saleTemplateRequest{
		ListSortType: 1,
		PageIndex:    page,
		PageSize:     100,
		SortType:     2,
	}
	data, err := c.makeRequest(ctx, "/api/homepage/pc/goods/market/querySaleTemplate", nil, requestBody)
	if err != nil {
		return nil, 0, err
	}
	var response SaleTemplateResponse
	err = json.Unmarshal(data, &response)
	if err != nil {
		return nil, 0, err
	}
	err = c.validateAPIResponse(response.Code, response.Msg)
	if err != nil {
		return nil, 0, err
	}
	if response.Data == nil || len(response.Data) == 0 {
		return nil, 0, nil
	}
	prices := make([]*value_object.CrawledPrice, 0, len(response.Data))
	for _, item := range response.Data {
		prices = append(prices, ItemToCrawledPrice(item, c.config.MarketID))
	}
	totalPage := int(response.TotalCount / len(response.Data))
	return prices, totalPage, nil
}
