package clienthttp

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strings"
	"time"
)

type Client struct {
	baseURL        string
	headers        map[string]string
	cookies        string
	proxy          string
	client         *http.Client
	requestTimeout time.Duration
}

func NewClient() *Client {
	return &Client{
		headers:        make(map[string]string),
		requestTimeout: 30 * time.Second, // Default timeout
		client: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (c *Client) SetBaseURL(baseURL string) {
	c.baseURL = strings.TrimSuffix(baseURL, "/")
}

func (c *Client) SetAPIKey(apikey string) {
	c.SetHeader("Authorization", "Bearer "+apikey)
}

func (c *Client) SetCookie(cookie string) {
	c.cookies = cookie
}

func (c *Client) SetHeader(key, value string) {
	c.headers[key] = value
}

func (c *Client) SetProxy(proxy string) {
	c.proxy = proxy

	// Log proxy information before updating
	if proxy == "" {
		log.Println("[HTTP Client] Proxy: None (Direct connection)")
		c.updateClientWithProxy()
		return
	}

	// Parse and validate proxy URL (supports IPv6, auth, etc.)
	proxyURL, err := c.parseProxyURL(proxy)
	if err != nil {
		log.Printf("[HTTP Client] Failed to parse proxy: %v", err)
		return
	}

	// Update the proxy string to the parsed/normalized version
	c.proxy = proxyURL.String()
	c.updateClientWithProxy()
}

func (c *Client) parseProxyURL(proxy string) (*url.URL, error) {
	// If no scheme provided, default to http
	if !strings.Contains(proxy, "://") {
		proxy = "http://" + proxy
	}

	// Parse the proxy URL
	proxyURL, err := url.Parse(proxy)
	if err != nil {
		return nil, fmt.Errorf("invalid proxy URL: %w", err)
	}

	// Validate scheme
	switch proxyURL.Scheme {
	case "http", "https", "socks5":
		// Valid schemes
	default:
		return nil, fmt.Errorf("unsupported proxy scheme: %s (supported: http, https, socks5)", proxyURL.Scheme)
	}

	return proxyURL, nil
}

func (c *Client) SetTimeout(timeout time.Duration) {
	c.requestTimeout = timeout
	c.client.Timeout = timeout
	log.Printf("[HTTP Client] Timeout set to: %v", timeout)
}

func (c *Client) updateClientWithProxy() {
	if c.proxy == "" {
		c.client.Transport = nil
		return
	}

	// Parse proxy URL
	proxyURL, err := url.Parse(c.proxy)
	if err != nil {
		// If parsing fails, try to add scheme
		if !strings.Contains(c.proxy, "://") {
			// Default to http if no scheme provided
			proxyURL, err = url.Parse("http://" + c.proxy)
			if err != nil {
				log.Printf("[HTTP Client] Failed to parse proxy URL: %v", err)
				return
			}
		} else {
			log.Printf("[HTTP Client] Failed to parse proxy URL: %v", err)
			return
		}
	}

	// Create transport with proxy
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}

	c.client.Transport = transport

	// Log proxy info (mask password for security)
	proxyInfo := c.proxy
	if proxyURL.User != nil {
		// Mask password in logs
		username := proxyURL.User.Username()
		if _, hasPassword := proxyURL.User.Password(); hasPassword {
			maskedURL := *proxyURL
			maskedURL.User = url.UserPassword(username, "***")
			proxyInfo = maskedURL.String()
		}
	}
	log.Printf("[HTTP Client] Proxy configured: %s", proxyInfo)
}

func (c *Client) buildURL(path string, params map[string]string) string {
	fullURL := c.baseURL + "/" + strings.TrimPrefix(path, "/")

	if len(params) > 0 {
		values := url.Values{}
		for key, value := range params {
			values.Add(key, value)
		}
		fullURL += "?" + values.Encode()
	}

	return fullURL
}

func (c *Client) prepareRequest(method, url string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	// Set headers
	for key, value := range c.headers {
		req.Header.Set(key, value)
	}

	// Set cookies
	if c.cookies != "" {
		req.Header.Set("Cookie", c.cookies)
	}

	// Set default Content-Type for POST requests
	if method == "POST" && req.Header.Get("Content-Type") == "" {
		req.Header.Set("Content-Type", "application/json")
	}

	return req, nil
}

func (c *Client) executeRequest(req *http.Request) ([]byte, error) {
	// Add timeout context to request
	ctx, cancel := context.WithTimeout(req.Context(), c.requestTimeout)
	defer cancel()
	req = req.WithContext(ctx)

	// Log request information including proxy
	proxyInfo := "Direct"
	if c.proxy != "" {
		proxyInfo = c.proxy
	}
	log.Printf("[HTTP Client] %s %s (Proxy: %s, Timeout: %v)",
		req.Method, req.URL.String(), proxyInfo, c.requestTimeout)

	startTime := time.Now()
	resp, err := c.client.Do(req)
	duration := time.Since(startTime)

	if err != nil {
		log.Printf("[HTTP Client] Request failed after %v: %v", duration, err)
		return nil, fmt.Errorf("request failed: %w", err)
	}
	defer resp.Body.Close()

	log.Printf("[HTTP Client] Response: %d %s (Duration: %v)",
		resp.StatusCode, resp.Status, duration)

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}
	log.Printf("[HTTP Client] Response body: %s", string(body))
	// Check for specific HTTP status codes
	if resp.StatusCode >= 400 {
		httpErr := &HTTPError{
			StatusCode: resp.StatusCode,
			Status:     resp.Status,
			Body:       string(body),
		}

		switch resp.StatusCode {
		case 401:
			httpErr.Err = ErrUnauthorized
		case 403:
			httpErr.Err = ErrForbidden
		case 404:
			httpErr.Err = ErrNotFound
		case 405:
			httpErr.Err = ErrMethodNotAllowed
		case 429:
			httpErr.Err = ErrTooManyRequests
		case 500:
			httpErr.Err = ErrInternalServer
		case 502:
			httpErr.Err = ErrBadGateway
		case 503:
			httpErr.Err = ErrServiceUnavail
		case 504:
			httpErr.Err = ErrGatewayTimeout
		default:
			if resp.StatusCode >= 400 && resp.StatusCode < 500 {
				httpErr.Err = fmt.Errorf("client error (%d)", resp.StatusCode)
			} else if resp.StatusCode >= 500 {
				httpErr.Err = fmt.Errorf("server error (%d)", resp.StatusCode)
			}
		}

		return nil, httpErr
	}

	return body, nil
}

func (c *Client) Get(path string, params map[string]string) ([]byte, error) {
	url := c.buildURL(path, params)

	req, err := c.prepareRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	return c.executeRequest(req)
}

func (c *Client) Post(path string, params map[string]string, body interface{}) ([]byte, error) {
	url := c.buildURL(path, params)

	var bodyReader io.Reader
	if body != nil {
		jsonBody, err := json.Marshal(body)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal request body: %w", err)
		}
		bodyReader = bytes.NewReader(jsonBody)
	}

	req, err := c.prepareRequest("POST", url, bodyReader)
	if err != nil {
		return nil, err
	}

	return c.executeRequest(req)
}

// Custom error types for different HTTP status codes
var (
	ErrUnauthorized     = errors.New("unauthorized (401)")
	ErrForbidden        = errors.New("forbidden (403)")
	ErrNotFound         = errors.New("not found (404)")
	ErrMethodNotAllowed = errors.New("method not allowed (405)")
	ErrTooManyRequests  = errors.New("too many requests (429)")
	ErrInternalServer   = errors.New("internal server error (500)")
	ErrBadGateway       = errors.New("bad gateway (502)")
	ErrServiceUnavail   = errors.New("service unavailable (503)")
	ErrGatewayTimeout   = errors.New("gateway timeout (504)")
)

type HTTPError struct {
	StatusCode int
	Status     string
	Body       string
	Err        error
}

func (e *HTTPError) Error() string {
	if e.Err != nil {
		return fmt.Sprintf("HTTP %d %s: %s", e.StatusCode, e.Status, e.Err.Error())
	}
	return fmt.Sprintf("HTTP %d %s: %s", e.StatusCode, e.Status, e.Body)
}

func (e *HTTPError) Unwrap() error {
	return e.Err
}

// Helper functions to check specific error types
func IsUnauthorized(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode == 401
}

func IsForbidden(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode == 403
}

func IsNotFound(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode == 404
}

func IsTooManyRequests(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode == 429
}

func IsServerError(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode >= 500
}

func IsClientError(err error) bool {
	var httpErr *HTTPError
	return errors.As(err, &httpErr) && httpErr.StatusCode >= 400 && httpErr.StatusCode < 500
}

func GetStatusCode(err error) int {
	var httpErr *HTTPError
	if errors.As(err, &httpErr) {
		return httpErr.StatusCode
	}
	return 0
}
