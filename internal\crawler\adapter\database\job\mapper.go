package repository

import (
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jinzhu/copier"
	"go_core_market/internal/crawler/adapter/database/job/sqlc"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/internal/crawler/domain/entity"
)

// FromSQLCModel converts sqlc.Job to domain.Job
func FromSQLCModel(sqlcJob *sqlc.Job) (*entity.Job, error) {
	target := entity.RebuildJobParams{}
	err := copier.Copy(target, &sqlcJob)
	if err != nil {
		return nil, err
	}

	// Handle progress fields
	if sqlcJob.CurrentStep != nil && sqlcJob.TotalSteps != nil &&
		sqlcJob.ProgressMessage != nil && sqlcJob.ProgressPercentage != nil {
		target.Progress = &entity.JobProgress{
			CurrentStep: 0,
			TotalSteps:  0,
			Message:     "",
			Percentage:  0,
		}
	}
	return entity.RebuildJob(target)

}

// FromSQLCModels converts slice of sqlc.Job to slice of domain.Job
func FromSQLCModels(sqlcJobs []*sqlc.Job) ([]*entity.Job, error) {
	jobs := make([]*entity.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		j, err := FromSQLCModel(sqlcJob)
		if err != nil {
			return nil, err
		}
		jobs[i] = j
	}
	return jobs, nil
}

func ToCreateParams(job *entity.Job) (*sqlc.CreateParams, error) {
	var crawlConfigID *int32
	if job.CrawlConfigId() != nil {
		id := int32(*job.CrawlConfigId())
		crawlConfigID = &id
	}
	var scheduledAt pgtype.Timestamptz
	if job.ScheduledAt() != nil {
		scheduledAt.Time = *job.ScheduledAt()
		scheduledAt.Valid = true
	} else {
		scheduledAt.Valid = false
	}
	return &sqlc.CreateParams{
		JobType:        string(job.JobType()),
		Name:           job.Name(),
		Description:    job.Description(),
		Status:         string(job.Status()),
		CrawlConfigID:  crawlConfigID,
		ScheduledAt:    scheduledAt,
		MaxRetries:     job.MaxRetries(),
		TimeoutSeconds: int(job.Timeout().Seconds()),
		CreatedBy:      job.CreatedBy(),
	}, nil
}

func ToUpdateJobParams(job *entity.Job) (*sqlc.UpdateJobParams, error) {
	// Chuyển đổi CrawlConfigID
	var crawlConfigID *int32
	if job.CrawlConfigId() != nil {
		id := int32(*job.CrawlConfigId())
		crawlConfigID = &id
	}

	// Chuyển đổi ScheduledAt
	var scheduledAt pgtype.Timestamptz
	if job.ScheduledAt() != nil {
		scheduledAt.Time = *job.ScheduledAt()
		scheduledAt.Valid = true
	} else {
		scheduledAt.Valid = false
	}

	// Chuyển đổi StartedAt
	var startedAt pgtype.Timestamptz
	if job.StartedAt() != nil {
		startedAt.Time = *job.StartedAt()
		startedAt.Valid = true
	} else {
		startedAt.Valid = false
	}

	// Chuyển đổi CompletedAt
	var completedAt pgtype.Timestamptz
	if job.CompletedAt() != nil {
		completedAt.Time = *job.CompletedAt()
		completedAt.Valid = true
	} else {
		completedAt.Valid = false
	}

	// Chuyển đổi Progress
	var currentStep, totalSteps, progressPercentage *int32
	var progressMessage *string
	if progress := job.Progress(); progress != nil {
		cs := int32(progress.CurrentStep)
		ts := int32(progress.TotalSteps)
		pp := int32(progress.Percentage)
		currentStep = &cs
		totalSteps = &ts
		progressPercentage = &pp
		progressMessage = &progress.Message
	}

	return &sqlc.UpdateJobParams{
		ID:                 job.ID(),
		JobType:            string(job.JobType()),
		Name:               job.Name(),
		Description:        job.Description(),
		Status:             string(job.Status()),
		CrawlConfigID:      crawlConfigID,
		ScheduledAt:        scheduledAt,
		StartedAt:          startedAt,
		CompletedAt:        completedAt,
		CurrentStep:        currentStep,
		TotalSteps:         totalSteps,
		ProgressMessage:    progressMessage,
		ProgressPercentage: progressPercentage,
		MaxRetries:         job.MaxRetries(),
		RetryCount:         job.RetryCount(),
		LastError:          job.LastError(),
		TimeoutSeconds:     int(job.Timeout().Seconds()),
	}, nil
}

func ToCreateManyParams(jobs []*entity.Job) ([]sqlc.CreateManyParams, error) {
	params := make([]sqlc.CreateManyParams, len(jobs))
	for i, job := range jobs {
		param, err := ToCreateParams(job)
		if err != nil {
			return nil, err
		}
		params[i] = sqlc.CreateManyParams{
			JobType:        param.JobType,
			Name:           param.Name,
			Description:    param.Description,
			Status:         param.Status,
			CrawlConfigID:  param.CrawlConfigID,
			ScheduledAt:    param.ScheduledAt,
			MaxRetries:     param.MaxRetries,
			TimeoutSeconds: param.TimeoutSeconds,
			CreatedBy:      param.CreatedBy,
		}
	}
	return params, nil
}

// FromSQLCModelToQueryModel converts sqlc.Job to query.Job
func FromSQLCModelToQueryModel(sqlcJob *sqlc.Job) (*queryjob.Job, error) {
	target := queryjob.Job{}
	err := copier.Copy(&target, &sqlcJob)
	if err != nil {
		return nil, err
	}
	return &target, nil
}

// FromSQLCModelsToQueryModels converts slice of sqlc.Job to slice of query.Job
func FromSQLCModelsToQueryModels(sqlcJobs []*sqlc.Job) ([]*queryjob.Job, error) {
	jobs := make([]*queryjob.Job, len(sqlcJobs))
	for i, sqlcJob := range sqlcJobs {
		job, err := FromSQLCModelToQueryModel(sqlcJob)
		if err != nil {
			return nil, err
		}
		jobs[i] = job
	}
	return jobs, nil
}
