package queryjob

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetJobByIdQuery struct {
	Id int
}

type GetJobByIdQueryHandler decorator.QueryHandler[GetJobByIdQuery, *Job]

type getJobByIdQueryHandler struct {
	repo JobReadModel
}

func NewGetJobByIdQueryHandler(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetJobByIdQueryHandler {

	return decorator.ApplyQueryDecorators[GetJobByIdQuery, *Job](
		getJobByIdQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getJobByIdQueryHandler) Handle(ctx context.Context, query GetJobByIdQuery) (*Job, error) {
	return h.repo.GetJobById(ctx, query.Id)
}
