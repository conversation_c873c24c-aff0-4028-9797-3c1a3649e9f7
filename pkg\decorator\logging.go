package decorator

import (
	"context"
	"fmt"
	"go_core_market/pkg/logger"
)

type commandLoggingDecorator[C any] struct {
	base   CommandHandler[C]
	logger logger.Logger
}

func (d commandLoggingDecorator[C]) Handle(ctx context.Context, cmd C) (err error) {
	handlerType := generateActionName(cmd)

	log := d.logger.WithFields(map[string]any{
		"command":      handlerType,
		"command_body": fmt.Sprintf("%#v", cmd),
	})

	log.Debug("Executing command")
	defer func() {
		if err == nil {
			log.Info("Command executed successfully")
		} else {
			log.WithError(err).Error("Failed to execute command")
		}
	}()

	return d.base.Handle(ctx, cmd)
}

type queryLoggingDecorator[C any, R any] struct {
	base   QueryHandler[C, R]
	logger logger.Logger
}

func (d queryLoggingDecorator[C, R]) Handle(ctx context.Context, cmd C) (result R, err error) {
	log := d.logger.WithFields(map[string]any{
		"query":      generateActionName(cmd),
		"query_body": fmt.Sprintf("%#v", cmd),
	})

	log.Debug("Executing query")
	defer func() {
		if err == nil {
			log.Info("Query executed successfully")
		} else {
			log.WithError(err).Error("Failed to execute query")
		}
	}()

	return d.base.Handle(ctx, cmd)
}
