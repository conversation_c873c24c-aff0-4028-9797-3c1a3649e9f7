package grpc

import (
	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	pb "go_core_market/pkg/pb/auth_crawl"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// AuthCrawlToProto converts a query.AuthCrawl to pb.AuthCrawl.
func AuthCrawlToProto(auth *queryauthcral.AuthCrawl) *pb.AuthCrawl {
	if auth == nil {
		return nil
	}

	pbAuth := &pb.AuthCrawl{
		Id:        int32(auth.Id),
		MarketId:  int32(auth.MarketID),
		AuthType:  auth.AuthType,
		Value:     auth.Value,
		IsActive:  auth.IsActive,
		IsUse:     auth.IsUse,
		CreatedAt: timestamppb.New(auth.CreatedAt),
		UpdatedAt: timestamppb.New(auth.UpdatedAt),
	}

	if auth.LastUsedAt != nil {
		pbAuth.LastUsedAt = timestamppb.New(*auth.LastUsedAt)
	}

	if auth.ExpiredAt != nil {
		pbAuth.ExpiredAt = timestamppb.New(*auth.ExpiredAt)
	}

	return pbAuth
}
