// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: api/protobuf/proxy.proto

package proxy

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ProxyService_CreateProxy_FullMethodName         = "/proxy.v1.ProxyService/CreateProxy"
	ProxyService_CreateProxyBatch_FullMethodName    = "/proxy.v1.ProxyService/CreateProxyBatch"
	ProxyService_UpdateProxy_FullMethodName         = "/proxy.v1.ProxyService/UpdateProxy"
	ProxyService_DeleteProxy_FullMethodName         = "/proxy.v1.ProxyService/DeleteProxy"
	ProxyService_ActivateProxy_FullMethodName       = "/proxy.v1.ProxyService/ActivateProxy"
	ProxyService_DeactivateProxy_FullMethodName     = "/proxy.v1.ProxyService/DeactivateProxy"
	ProxyService_MarkProxyUsed_FullMethodName       = "/proxy.v1.ProxyService/MarkProxyUsed"
	ProxyService_MarkProxiesUsed_FullMethodName     = "/proxy.v1.ProxyService/MarkProxiesUsed"
	ProxyService_ReleaseProxy_FullMethodName        = "/proxy.v1.ProxyService/ReleaseProxy"
	ProxyService_ReleaseManyProxies_FullMethodName  = "/proxy.v1.ProxyService/ReleaseManyProxies"
	ProxyService_GetAllProxies_FullMethodName       = "/proxy.v1.ProxyService/GetAllProxies"
	ProxyService_GetProxyById_FullMethodName        = "/proxy.v1.ProxyService/GetProxyById"
	ProxyService_GetAvailableProxies_FullMethodName = "/proxy.v1.ProxyService/GetAvailableProxies"
)

// ProxyServiceClient is the client API for ProxyService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC Service
type ProxyServiceClient interface {
	// Commands
	CreateProxy(ctx context.Context, in *CreateProxyRequest, opts ...grpc.CallOption) (*CreateProxyResponse, error)
	CreateProxyBatch(ctx context.Context, in *CreateProxyBatchRequest, opts ...grpc.CallOption) (*CreateProxyBatchResponse, error)
	UpdateProxy(ctx context.Context, in *UpdateProxyRequest, opts ...grpc.CallOption) (*UpdateProxyResponse, error)
	DeleteProxy(ctx context.Context, in *DeleteProxyRequest, opts ...grpc.CallOption) (*DeleteProxyResponse, error)
	ActivateProxy(ctx context.Context, in *ActivateProxyRequest, opts ...grpc.CallOption) (*ActivateProxyResponse, error)
	DeactivateProxy(ctx context.Context, in *DeactivateProxyRequest, opts ...grpc.CallOption) (*DeactivateProxyResponse, error)
	MarkProxyUsed(ctx context.Context, in *MarkProxyUsedRequest, opts ...grpc.CallOption) (*MarkProxyUsedResponse, error)
	MarkProxiesUsed(ctx context.Context, in *MarkProxiesUsedRequest, opts ...grpc.CallOption) (*MarkProxiesUsedResponse, error)
	ReleaseProxy(ctx context.Context, in *ReleaseProxyRequest, opts ...grpc.CallOption) (*ReleaseProxyResponse, error)
	ReleaseManyProxies(ctx context.Context, in *ReleaseManyProxiesRequest, opts ...grpc.CallOption) (*ReleaseManyProxiesResponse, error)
	// Queries
	GetAllProxies(ctx context.Context, in *GetAllProxiesRequest, opts ...grpc.CallOption) (*GetAllProxiesResponse, error)
	GetProxyById(ctx context.Context, in *GetProxyByIdRequest, opts ...grpc.CallOption) (*GetProxyByIdResponse, error)
	GetAvailableProxies(ctx context.Context, in *GetAvailableProxiesRequest, opts ...grpc.CallOption) (*GetAvailableProxiesResponse, error)
}

type proxyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewProxyServiceClient(cc grpc.ClientConnInterface) ProxyServiceClient {
	return &proxyServiceClient{cc}
}

func (c *proxyServiceClient) CreateProxy(ctx context.Context, in *CreateProxyRequest, opts ...grpc.CallOption) (*CreateProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_CreateProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) CreateProxyBatch(ctx context.Context, in *CreateProxyBatchRequest, opts ...grpc.CallOption) (*CreateProxyBatchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateProxyBatchResponse)
	err := c.cc.Invoke(ctx, ProxyService_CreateProxyBatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) UpdateProxy(ctx context.Context, in *UpdateProxyRequest, opts ...grpc.CallOption) (*UpdateProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_UpdateProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) DeleteProxy(ctx context.Context, in *DeleteProxyRequest, opts ...grpc.CallOption) (*DeleteProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_DeleteProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) ActivateProxy(ctx context.Context, in *ActivateProxyRequest, opts ...grpc.CallOption) (*ActivateProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_ActivateProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) DeactivateProxy(ctx context.Context, in *DeactivateProxyRequest, opts ...grpc.CallOption) (*DeactivateProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeactivateProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_DeactivateProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) MarkProxyUsed(ctx context.Context, in *MarkProxyUsedRequest, opts ...grpc.CallOption) (*MarkProxyUsedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkProxyUsedResponse)
	err := c.cc.Invoke(ctx, ProxyService_MarkProxyUsed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) MarkProxiesUsed(ctx context.Context, in *MarkProxiesUsedRequest, opts ...grpc.CallOption) (*MarkProxiesUsedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkProxiesUsedResponse)
	err := c.cc.Invoke(ctx, ProxyService_MarkProxiesUsed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) ReleaseProxy(ctx context.Context, in *ReleaseProxyRequest, opts ...grpc.CallOption) (*ReleaseProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseProxyResponse)
	err := c.cc.Invoke(ctx, ProxyService_ReleaseProxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) ReleaseManyProxies(ctx context.Context, in *ReleaseManyProxiesRequest, opts ...grpc.CallOption) (*ReleaseManyProxiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseManyProxiesResponse)
	err := c.cc.Invoke(ctx, ProxyService_ReleaseManyProxies_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) GetAllProxies(ctx context.Context, in *GetAllProxiesRequest, opts ...grpc.CallOption) (*GetAllProxiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllProxiesResponse)
	err := c.cc.Invoke(ctx, ProxyService_GetAllProxies_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) GetProxyById(ctx context.Context, in *GetProxyByIdRequest, opts ...grpc.CallOption) (*GetProxyByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetProxyByIdResponse)
	err := c.cc.Invoke(ctx, ProxyService_GetProxyById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *proxyServiceClient) GetAvailableProxies(ctx context.Context, in *GetAvailableProxiesRequest, opts ...grpc.CallOption) (*GetAvailableProxiesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAvailableProxiesResponse)
	err := c.cc.Invoke(ctx, ProxyService_GetAvailableProxies_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ProxyServiceServer is the server API for ProxyService service.
// All implementations must embed UnimplementedProxyServiceServer
// for forward compatibility.
//
// gRPC Service
type ProxyServiceServer interface {
	// Commands
	CreateProxy(context.Context, *CreateProxyRequest) (*CreateProxyResponse, error)
	CreateProxyBatch(context.Context, *CreateProxyBatchRequest) (*CreateProxyBatchResponse, error)
	UpdateProxy(context.Context, *UpdateProxyRequest) (*UpdateProxyResponse, error)
	DeleteProxy(context.Context, *DeleteProxyRequest) (*DeleteProxyResponse, error)
	ActivateProxy(context.Context, *ActivateProxyRequest) (*ActivateProxyResponse, error)
	DeactivateProxy(context.Context, *DeactivateProxyRequest) (*DeactivateProxyResponse, error)
	MarkProxyUsed(context.Context, *MarkProxyUsedRequest) (*MarkProxyUsedResponse, error)
	MarkProxiesUsed(context.Context, *MarkProxiesUsedRequest) (*MarkProxiesUsedResponse, error)
	ReleaseProxy(context.Context, *ReleaseProxyRequest) (*ReleaseProxyResponse, error)
	ReleaseManyProxies(context.Context, *ReleaseManyProxiesRequest) (*ReleaseManyProxiesResponse, error)
	// Queries
	GetAllProxies(context.Context, *GetAllProxiesRequest) (*GetAllProxiesResponse, error)
	GetProxyById(context.Context, *GetProxyByIdRequest) (*GetProxyByIdResponse, error)
	GetAvailableProxies(context.Context, *GetAvailableProxiesRequest) (*GetAvailableProxiesResponse, error)
	mustEmbedUnimplementedProxyServiceServer()
}

// UnimplementedProxyServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedProxyServiceServer struct{}

func (UnimplementedProxyServiceServer) CreateProxy(context.Context, *CreateProxyRequest) (*CreateProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProxy not implemented")
}
func (UnimplementedProxyServiceServer) CreateProxyBatch(context.Context, *CreateProxyBatchRequest) (*CreateProxyBatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateProxyBatch not implemented")
}
func (UnimplementedProxyServiceServer) UpdateProxy(context.Context, *UpdateProxyRequest) (*UpdateProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateProxy not implemented")
}
func (UnimplementedProxyServiceServer) DeleteProxy(context.Context, *DeleteProxyRequest) (*DeleteProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteProxy not implemented")
}
func (UnimplementedProxyServiceServer) ActivateProxy(context.Context, *ActivateProxyRequest) (*ActivateProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateProxy not implemented")
}
func (UnimplementedProxyServiceServer) DeactivateProxy(context.Context, *DeactivateProxyRequest) (*DeactivateProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateProxy not implemented")
}
func (UnimplementedProxyServiceServer) MarkProxyUsed(context.Context, *MarkProxyUsedRequest) (*MarkProxyUsedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkProxyUsed not implemented")
}
func (UnimplementedProxyServiceServer) MarkProxiesUsed(context.Context, *MarkProxiesUsedRequest) (*MarkProxiesUsedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkProxiesUsed not implemented")
}
func (UnimplementedProxyServiceServer) ReleaseProxy(context.Context, *ReleaseProxyRequest) (*ReleaseProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseProxy not implemented")
}
func (UnimplementedProxyServiceServer) ReleaseManyProxies(context.Context, *ReleaseManyProxiesRequest) (*ReleaseManyProxiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseManyProxies not implemented")
}
func (UnimplementedProxyServiceServer) GetAllProxies(context.Context, *GetAllProxiesRequest) (*GetAllProxiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllProxies not implemented")
}
func (UnimplementedProxyServiceServer) GetProxyById(context.Context, *GetProxyByIdRequest) (*GetProxyByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetProxyById not implemented")
}
func (UnimplementedProxyServiceServer) GetAvailableProxies(context.Context, *GetAvailableProxiesRequest) (*GetAvailableProxiesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableProxies not implemented")
}
func (UnimplementedProxyServiceServer) mustEmbedUnimplementedProxyServiceServer() {}
func (UnimplementedProxyServiceServer) testEmbeddedByValue()                      {}

// UnsafeProxyServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ProxyServiceServer will
// result in compilation errors.
type UnsafeProxyServiceServer interface {
	mustEmbedUnimplementedProxyServiceServer()
}

func RegisterProxyServiceServer(s grpc.ServiceRegistrar, srv ProxyServiceServer) {
	// If the following call pancis, it indicates UnimplementedProxyServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ProxyService_ServiceDesc, srv)
}

func _ProxyService_CreateProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).CreateProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_CreateProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).CreateProxy(ctx, req.(*CreateProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_CreateProxyBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateProxyBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).CreateProxyBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_CreateProxyBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).CreateProxyBatch(ctx, req.(*CreateProxyBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_UpdateProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).UpdateProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_UpdateProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).UpdateProxy(ctx, req.(*UpdateProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_DeleteProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).DeleteProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_DeleteProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).DeleteProxy(ctx, req.(*DeleteProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_ActivateProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).ActivateProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_ActivateProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).ActivateProxy(ctx, req.(*ActivateProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_DeactivateProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).DeactivateProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_DeactivateProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).DeactivateProxy(ctx, req.(*DeactivateProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_MarkProxyUsed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkProxyUsedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).MarkProxyUsed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_MarkProxyUsed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).MarkProxyUsed(ctx, req.(*MarkProxyUsedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_MarkProxiesUsed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkProxiesUsedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).MarkProxiesUsed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_MarkProxiesUsed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).MarkProxiesUsed(ctx, req.(*MarkProxiesUsedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_ReleaseProxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).ReleaseProxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_ReleaseProxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).ReleaseProxy(ctx, req.(*ReleaseProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_ReleaseManyProxies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseManyProxiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).ReleaseManyProxies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_ReleaseManyProxies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).ReleaseManyProxies(ctx, req.(*ReleaseManyProxiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_GetAllProxies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllProxiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).GetAllProxies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_GetAllProxies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).GetAllProxies(ctx, req.(*GetAllProxiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_GetProxyById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetProxyByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).GetProxyById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_GetProxyById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).GetProxyById(ctx, req.(*GetProxyByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ProxyService_GetAvailableProxies_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableProxiesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ProxyServiceServer).GetAvailableProxies(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ProxyService_GetAvailableProxies_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ProxyServiceServer).GetAvailableProxies(ctx, req.(*GetAvailableProxiesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ProxyService_ServiceDesc is the grpc.ServiceDesc for ProxyService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ProxyService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "proxy.v1.ProxyService",
	HandlerType: (*ProxyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateProxy",
			Handler:    _ProxyService_CreateProxy_Handler,
		},
		{
			MethodName: "CreateProxyBatch",
			Handler:    _ProxyService_CreateProxyBatch_Handler,
		},
		{
			MethodName: "UpdateProxy",
			Handler:    _ProxyService_UpdateProxy_Handler,
		},
		{
			MethodName: "DeleteProxy",
			Handler:    _ProxyService_DeleteProxy_Handler,
		},
		{
			MethodName: "ActivateProxy",
			Handler:    _ProxyService_ActivateProxy_Handler,
		},
		{
			MethodName: "DeactivateProxy",
			Handler:    _ProxyService_DeactivateProxy_Handler,
		},
		{
			MethodName: "MarkProxyUsed",
			Handler:    _ProxyService_MarkProxyUsed_Handler,
		},
		{
			MethodName: "MarkProxiesUsed",
			Handler:    _ProxyService_MarkProxiesUsed_Handler,
		},
		{
			MethodName: "ReleaseProxy",
			Handler:    _ProxyService_ReleaseProxy_Handler,
		},
		{
			MethodName: "ReleaseManyProxies",
			Handler:    _ProxyService_ReleaseManyProxies_Handler,
		},
		{
			MethodName: "GetAllProxies",
			Handler:    _ProxyService_GetAllProxies_Handler,
		},
		{
			MethodName: "GetProxyById",
			Handler:    _ProxyService_GetProxyById_Handler,
		},
		{
			MethodName: "GetAvailableProxies",
			Handler:    _ProxyService_GetAvailableProxies_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protobuf/proxy.proto",
}
