package repository

import (
	"context"
	"errors"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/configcrawl/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/database"
)

// Error variables
var (
	ErrConfigNotFound      = errors.New("crawl config not found")
	ErrEmptyConfigList     = errors.New("no crawl configs to create")
	ErrUpdateConfigFail    = errors.New("failed to update crawl config")
	ErrCreateConfigFail    = errors.New("failed to save crawl config")
	ErrCreateConfigsFail   = errors.New("failed to save crawl configs")
	ErrDeleteConfigFail    = errors.New("failed to delete crawl config")
)

type configCrawlRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewConfigCrawlRepository creates a new crawl config repository
func NewConfigCrawlRepository(db database.DBTX) repository.RepositoryConfigCrawl {
	return &configCrawlRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create inserts a single crawl config into the database
func (r *configCrawlRepository) Create(ctx context.Context, config *entity.CrawlConfig) error {
	params, err := ToCreateParams(config)
	if err != nil {
		return err
	}

	if _, err := r.queries.Create(ctx, *params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateConfigFail, err)
	}

	return nil
}

// CreateMany inserts multiple crawl configs into the database
func (r *configCrawlRepository) CreateMany(ctx context.Context, configs []*entity.CrawlConfig) error {
	if len(configs) == 0 {
		return ErrEmptyConfigList
	}

	params, err := ToCreateManyParams(configs)
	if err != nil {
		return err
	}

	if _, err := r.queries.CreateMany(ctx, params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateConfigsFail, err)
	}

	return nil
}

// Update updates a single crawl config by ID using a provided update function
func (r *configCrawlRepository) Update(ctx context.Context, id int, updateFn func(c *entity.CrawlConfig) (*entity.CrawlConfig, error)) error {
	rawConfig, err := r.queries.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrConfigNotFound, err)
	}

	config, err := FromSQLCModel(rawConfig)
	if err != nil {
		return err
	}

	updatedConfig, err := updateFn(config)
	if err != nil {
		return err
	}

	updateParams, err := ToUpdateCrawlConfigParams(updatedConfig)
	if err != nil {
		return err
	}

	if _, err := r.queries.UpdateCrawlConfig(ctx, *updateParams); err != nil {
		return fmt.Errorf("%w: %v", ErrUpdateConfigFail, err)
	}

	return nil
}

// Delete removes a crawl config by ID
func (r *configCrawlRepository) Delete(ctx context.Context, id int) error {
	if err := r.queries.DeleteCrawlConfig(ctx, id); err != nil {
		return fmt.Errorf("%w: %v", ErrDeleteConfigFail, err)
	}
	return nil
}

// DeleteMany removes multiple crawl configs by IDs
func (r *configCrawlRepository) DeleteMany(ctx context.Context, ids []int) error {
	for _, id := range ids {
		if err := r.Delete(ctx, id); err != nil {
			return err
		}
	}
	return nil
}
