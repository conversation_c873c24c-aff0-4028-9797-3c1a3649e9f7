package buff163

var defaultHeadersSets = []map[string]string{
	// Header Set 1 - Samsung Galaxy S21
	{
		"App-Version":         "1160",
		"Channel":             "Official",
		"Device-Id-Weak":      "ff5d9b5c74087064",
		"Manufacturer":        "samsung",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "z3qksx",
		"Device-Id":           "a20316de-c54a-4349-a6ad-34aa90421a40",
		"Rom-Id":              "NRD90M.G955NKSU1AQDC",
		"DeviceName":          "SM-G977N",
		"Network":             "WIFI/",
		"App-Version-Code":    "********",
		"Brand":               "samsung",
		"Timezone-Offset":     "25200000",
		"Model":               "SM-G988N",
		"Resolution":          "1080x1920",
		"Screen-Density":      "320.00",
		"Build-Fingerprint":   "samsung/z3qksx/z3q:7.1.2/NRD90M/700231017:user/release-keys",
		"User-Agent":          "Android/1160/********/ff5d9b5c74087064/25/samsung/SM-G988N/z3qksx/a20316de-c54a-4349-a6ad-34aa90421a40",
		"System-Version":      "25",
		"Timezone":            "Indochina Time",
		"Seed":                "fc9bb77b89b3c0ba4f3deffd5c71d6eb",
		"Timezone-Offset-DST": "25200000",
		"Sign":                "Hello, world!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "SM-G955N-user 7.1.2 NRD90M.G955NKSU1AQDC 701221104 release-keys",
	},
	// Header Set 2 - iPhone 13 Pro
	{
		"App-Version":         "1165",
		"Channel":             "AppStore",
		"Device-Id-Weak":      "ab3e8f2d91056789",
		"Manufacturer":        "Apple",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "iPhone14,3",
		"Device-Id":           "b30427ef-d65b-4450-b7be-45bb91532b51",
		"Rom-Id":              "iOS15.6.1",
		"DeviceName":          "iPhone13,3",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "Apple",
		"Timezone-Offset":     "28800000",
		"Model":               "iPhone13,3",
		"Resolution":          "1170x2532",
		"Screen-Density":      "460.00",
		"Build-Fingerprint":   "Apple/iPhone14,3/iOS15.6.1:user/release-keys",
		"User-Agent":          "iOS/1165/********/ab3e8f2d91056789/15/Apple/iPhone13,3/iPhone14,3/b30427ef-d65b-4450-b7be-45bb91532b51",
		"System-Version":      "15",
		"Timezone":            "China Standard Time",
		"Seed":                "a1b2c3d4e5f6789012345678901234ab",
		"Timezone-Offset-DST": "28800000",
		"Sign":                "Hello, iOS!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "iOS 15.6.1 (19G82)",
	},
	// Header Set 3 - Xiaomi Mi 11
	{
		"App-Version":         "1158",
		"Channel":             "Xiaomi",
		"Device-Id-Weak":      "cd4f9a6e82137890",
		"Manufacturer":        "Xiaomi",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "venus",
		"Device-Id":           "c41538fg-e76c-4561-c8cf-56cc02643c62",
		"Rom-Id":              "SKQ1.210216.001",
		"DeviceName":          "M2011K2C",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "Xiaomi",
		"Timezone-Offset":     "28800000",
		"Model":               "M2011K2C",
		"Resolution":          "1440x3200",
		"Screen-Density":      "560.00",
		"Build-Fingerprint":   "Xiaomi/venus/venus:11/RKQ1.200826.002/V12.5.5.0.RKBCNXM:user/release-keys",
		"User-Agent":          "Android/1158/********/cd4f9a6e82137890/30/Xiaomi/M2011K2C/venus/c41538fg-e76c-4561-c8cf-56cc02643c62",
		"System-Version":      "30",
		"Timezone":            "China Standard Time",
		"Seed":                "ef1234567890abcdef1234567890abcd",
		"Timezone-Offset-DST": "28800000",
		"Sign":                "Hello, MIUI!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "M2011K2C-user 11 RKQ1.200826.002 V12.5.5.0.RKBCNXM release-keys",
	},
	// Header Set 4 - OnePlus 9 Pro
	{
		"App-Version":         "1162",
		"Channel":             "OnePlus",
		"Device-Id-Weak":      "de5a0b7f93248901",
		"Manufacturer":        "OnePlus",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "lemonadep",
		"Device-Id":           "d52649gh-f87d-4672-d9d0-67dd13754d73",
		"Rom-Id":              "RKQ1.201105.002",
		"DeviceName":          "LE2123",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "OnePlus",
		"Timezone-Offset":     "0",
		"Model":               "LE2123",
		"Resolution":          "1440x3216",
		"Screen-Density":      "525.00",
		"Build-Fingerprint":   "OnePlus/lemonadep/OnePlus9Pro:11/RKQ1.201105.002/2103191602:user/release-keys",
		"User-Agent":          "Android/1162/********/de5a0b7f93248901/30/OnePlus/LE2123/lemonadep/d52649gh-f87d-4672-d9d0-67dd13754d73",
		"System-Version":      "30",
		"Timezone":            "Greenwich Mean Time",
		"Seed":                "123456789abcdef0123456789abcdef01",
		"Timezone-Offset-DST": "3600000",
		"Sign":                "Hello, OxygenOS!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "LE2123-user 11 RKQ1.201105.002 2103191602 release-keys",
	},
	// Header Set 5 - Google Pixel 6
	{
		"App-Version":         "1163",
		"Channel":             "GooglePlay",
		"Device-Id-Weak":      "ef6b1c8094359012",
		"Manufacturer":        "Google",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "oriole",
		"Device-Id":           "e6375ahi-098e-4783-eae1-78ee24865e84",
		"Rom-Id":              "SP2A.220405.004",
		"DeviceName":          "Pixel 6",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "google",
		"Timezone-Offset":     "-28800000",
		"Model":               "Pixel 6",
		"Resolution":          "1080x2400",
		"Screen-Density":      "411.00",
		"Build-Fingerprint":   "google/oriole/oriole:12/SP2A.220405.004/8233519:user/release-keys",
		"User-Agent":          "Android/1163/********/ef6b1c8094359012/31/Google/Pixel 6/oriole/e6375ahi-098e-4783-eae1-78ee24865e84",
		"System-Version":      "31",
		"Timezone":            "Pacific Standard Time",
		"Seed":                "23456789abcdef0123456789abcdef012",
		"Timezone-Offset-DST": "-25200000",
		"Sign":                "Hello, Android!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "Pixel 6-user 12 SP2A.220405.004 8233519 release-keys",
	},
	// Header Set 6 - Huawei P40 Pro
	{
		"App-Version":         "1159",
		"Channel":             "Huawei",
		"Device-Id-Weak":      "f07c2d9105460123",
		"Manufacturer":        "HUAWEI",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "els",
		"Device-Id":           "f7486bij-109f-4894-fbf2-89ff35976f95",
		"Rom-Id":              "HUAWEI ELS-AN00 **********",
		"DeviceName":          "ELS-AN00",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "HUAWEI",
		"Timezone-Offset":     "28800000",
		"Model":               "ELS-AN00",
		"Resolution":          "1200x2640",
		"Screen-Density":      "441.00",
		"Build-Fingerprint":   "HUAWEI/ELS-AN00/HWELS:10/HUAWEIALS-AN00/**********C00:user/release-keys",
		"User-Agent":          "Android/1159/********/f07c2d9105460123/29/HUAWEI/ELS-AN00/els/f7486bij-109f-4894-fbf2-89ff35976f95",
		"System-Version":      "29",
		"Timezone":            "China Standard Time",
		"Seed":                "3456789abcdef0123456789abcdef0123",
		"Timezone-Offset-DST": "28800000",
		"Sign":                "Hello, EMUI!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "ELS-AN00-user 10 HUAWEIALS-AN00 **********C00 release-keys",
	},
	// Header Set 7 - Oppo Find X3 Pro
	{
		"App-Version":         "1161",
		"Channel":             "Oppo",
		"Device-Id-Weak":      "018d3ea216571234",
		"Manufacturer":        "OPPO",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "find_x3_pro",
		"Device-Id":           "08597cjk-21a0-4905-0c03-9a00469870a6",
		"Rom-Id":              "RKQ1.201217.002",
		"DeviceName":          "CPH2173",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "OPPO",
		"Timezone-Offset":     "19800000",
		"Model":               "CPH2173",
		"Resolution":          "1440x3216",
		"Screen-Density":      "525.00",
		"Build-Fingerprint":   "OPPO/CPH2173/OP4F2F:11/RKQ1.201217.002/1615443819:user/release-keys",
		"User-Agent":          "Android/1161/********/018d3ea216571234/30/OPPO/CPH2173/find_x3_pro/08597cjk-21a0-4905-0c03-9a00469870a6",
		"System-Version":      "30",
		"Timezone":            "India Standard Time",
		"Seed":                "456789abcdef0123456789abcdef01234",
		"Timezone-Offset-DST": "19800000",
		"Sign":                "Hello, ColorOS!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "CPH2173-user 11 RKQ1.201217.002 1615443819 release-keys",
	},
	// Header Set 8 - Vivo X70 Pro
	{
		"App-Version":         "1164",
		"Channel":             "Vivo",
		"Device-Id-Weak":      "129e4fb327682345",
		"Manufacturer":        "vivo",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "PD2158F",
		"Device-Id":           "19608dkl-32b1-4a16-1d14-ab11570981b7",
		"Rom-Id":              "RP1A.200720.012",
		"DeviceName":          "V2158A",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "vivo",
		"Timezone-Offset":     "25200000",
		"Model":               "V2158A",
		"Resolution":          "1080x2376",
		"Screen-Density":      "440.00",
		"Build-Fingerprint":   "vivo/PD2158F/PD2158F:11/RP1A.200720.012/compiler09211739:user/release-keys",
		"User-Agent":          "Android/1164/********/129e4fb327682345/30/vivo/V2158A/PD2158F/19608dkl-32b1-4a16-1d14-ab11570981b7",
		"System-Version":      "30",
		"Timezone":            "Indochina Time",
		"Seed":                "56789abcdef0123456789abcdef012345",
		"Timezone-Offset-DST": "25200000",
		"Sign":                "Hello, FuntouchOS!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "V2158A-user 11 RP1A.200720.012 compiler09211739 release-keys",
	},
	// Header Set 9 - Realme GT
	{
		"App-Version":         "1157",
		"Channel":             "Realme",
		"Device-Id-Weak":      "23af50c438793456",
		"Manufacturer":        "realme",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "RMX3031",
		"Device-Id":           "2a719elm-43c2-4b27-2e25-bc22681a92c8",
		"Rom-Id":              "RKQ1.201112.002",
		"DeviceName":          "RMX3031",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "realme",
		"Timezone-Offset":     "25200000",
		"Model":               "RMX3031",
		"Resolution":          "1080x2400",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "realme/RMX3031/RMX3031:11/RKQ1.201112.002/1624437406:user/release-keys",
		"User-Agent":          "Android/1157/********/23af50c438793456/30/realme/RMX3031/RMX3031/2a719elm-43c2-4b27-2e25-bc22681a92c8",
		"System-Version":      "30",
		"Timezone":            "Indochina Time",
		"Seed":                "6789abcdef0123456789abcdef0123456",
		"Timezone-Offset-DST": "25200000",
		"Sign":                "Hello, RealmeUI!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "RMX3031-user 11 RKQ1.201112.002 1624437406 release-keys",
	},
	// Header Set 10 - Sony Xperia 1 III
	{
		"App-Version":         "1166",
		"Channel":             "Sony",
		"Device-Id-Weak":      "34b061d549804567",
		"Manufacturer":        "Sony",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "pdx215",
		"Device-Id":           "3b82afmn-54d3-4c38-3f36-cd33792ba3d9",
		"Rom-Id":              "61.1.A.1.149",
		"DeviceName":          "XQ-BC72",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "Sony",
		"Timezone-Offset":     "32400000",
		"Model":               "XQ-BC72",
		"Resolution":          "1644x3840",
		"Screen-Density":      "643.00",
		"Build-Fingerprint":   "Sony/pdx215/pdx215:11/61.1.A.1.149/061001149:user/release-keys",
		"User-Agent":          "Android/1166/********/34b061d549804567/30/Sony/XQ-BC72/pdx215/3b82afmn-54d3-4c38-3f36-cd33792ba3d9",
		"System-Version":      "30",
		"Timezone":            "Japan Standard Time",
		"Seed":                "789abcdef0123456789abcdef01234567",
		"Timezone-Offset-DST": "32400000",
		"Sign":                "Hello, Xperia!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "XQ-BC72-user 11 61.1.A.1.149 061001149 release-keys",
	},
	// Header Set 11 - LG V60 ThinQ
	{
		"App-Version":         "1154",
		"Channel":             "LG",
		"Device-Id-Weak":      "45c172e65a915678",
		"Manufacturer":        "LGE",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "timelm",
		"Device-Id":           "4c93b0no-65e4-4d49-4047-de44803cb4ea",
		"Rom-Id":              "QKQ1.191222.002",
		"DeviceName":          "LM-V600N",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "lge",
		"Timezone-Offset":     "32400000",
		"Model":               "LM-V600N",
		"Resolution":          "1080x2460",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "lge/timelm_lao_com/timelm:10/QKQ1.191222.002/201161234567:user/release-keys",
		"User-Agent":          "Android/1154/********/45c172e65a915678/29/LGE/LM-V600N/timelm/4c93b0no-65e4-4d49-4047-de44803cb4ea",
		"System-Version":      "29",
		"Timezone":            "Korea Standard Time",
		"Seed":                "89abcdef0123456789abcdef012345678",
		"Timezone-Offset-DST": "32400000",
		"Sign":                "Hello, LG UX!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "LM-V600N-user 10 QKQ1.191222.002 201161234567 release-keys",
	},
	// Header Set 12 - Motorola Edge+
	{
		"App-Version":         "1155",
		"Channel":             "Motorola",
		"Device-Id-Weak":      "56d283f76ba26789",
		"Manufacturer":        "motorola",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "burton",
		"Device-Id":           "5da4c1op-76f5-4e5a-5158-ef55914dc5fb",
		"Rom-Id":              "RPAS31.Q2-59-17-2",
		"DeviceName":          "XT2061-3",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "motorola",
		"Timezone-Offset":     "-10800000",
		"Model":               "XT2061-3",
		"Resolution":          "1080x2340",
		"Screen-Density":      "444.00",
		"Build-Fingerprint":   "motorola/burton_retail/burton:10/QPAS30.61-83-11/c6c4c:user/release-keys",
		"User-Agent":          "Android/1155/********/56d283f76ba26789/29/motorola/XT2061-3/burton/5da4c1op-76f5-4e5a-5158-ef55914dc5fb",
		"System-Version":      "29",
		"Timezone":            "Brasilia Time",
		"Seed":                "9abcdef0123456789abcdef0123456789",
		"Timezone-Offset-DST": "-7200000",
		"Sign":                "Hello, Moto!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "XT2061-3-user 10 QPAS30.61-83-11 c6c4c release-keys",
	},
	// Header Set 13 - Nokia 8.3 5G
	{
		"App-Version":         "1156",
		"Channel":             "Nokia",
		"Device-Id-Weak":      "67e394087cb3789a",
		"Manufacturer":        "HMD Global",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "BGT_sprout",
		"Device-Id":           "6eb5d2pq-87g6-4f6b-6269-f066a25ed60c",
		"Rom-Id":              "00WW_2_340",
		"DeviceName":          "TA-1243",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "Nokia",
		"Timezone-Offset":     "7200000",
		"Model":               "TA-1243",
		"Resolution":          "1080x2400",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "Nokia/Quicksilver_00WW/BGT_sprout:10/QKQ1.200329.002/00WW_2_340:user/release-keys",
		"User-Agent":          "Android/1156/********/67e394087cb3789a/29/HMD Global/TA-1243/BGT_sprout/6eb5d2pq-87g6-4f6b-6269-f066a25ed60c",
		"System-Version":      "29",
		"Timezone":            "Eastern European Time",
		"Seed":                "abcdef0123456789abcdef0123456789a",
		"Timezone-Offset-DST": "10800000",
		"Sign":                "Hello, Nokia!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "TA-1243-user 10 QKQ1.200329.002 00WW_2_340 release-keys",
	},
	// Header Set 14 - Asus ROG Phone 5
	{
		"App-Version":         "1167",
		"Channel":             "Asus",
		"Device-Id-Weak":      "78f4a5198dc4890b",
		"Manufacturer":        "asus",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "ASUS_I005_1",
		"Device-Id":           "7fc6e3qr-98h7-4g7c-737a-0177b36fe71d",
		"Rom-Id":              "RKQ1.201022.002",
		"DeviceName":          "ASUS_I005D",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "asus",
		"Timezone-Offset":     "28800000",
		"Model":               "ASUS_I005D",
		"Resolution":          "1080x2448",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "asus/WW_I005D/ASUS_I005_1:11/RKQ1.201022.002/18.0840.2104.49-0:user/release-keys",
		"User-Agent":          "Android/1167/********/78f4a5198dc4890b/30/asus/ASUS_I005D/ASUS_I005_1/7fc6e3qr-98h7-4g7c-737a-0177b36fe71d",
		"System-Version":      "30",
		"Timezone":            "Taipei Standard Time",
		"Seed":                "bcdef0123456789abcdef0123456789ab",
		"Timezone-Offset-DST": "28800000",
		"Sign":                "Hello, ROG!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "ASUS_I005D-user 11 RKQ1.201022.002 18.0840.2104.49-0 release-keys",
	},
	// Header Set 15 - Nothing Phone (1)
	{
		"App-Version":         "1168",
		"Channel":             "Nothing",
		"Device-Id-Weak":      "89056b2a9ed5901c",
		"Manufacturer":        "Nothing",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "Spacewar",
		"Device-Id":           "80d7f4rs-a9i8-4h8d-848b-1288c47g082e",
		"Rom-Id":              "TP1A.220624.014",
		"DeviceName":          "A063",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "Nothing",
		"Timezone-Offset":     "0",
		"Model":               "A063",
		"Resolution":          "1080x2400",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "Nothing/Spacewar/Spacewar:12/TP1A.220624.014/1659416176:user/release-keys",
		"User-Agent":          "Android/1168/********/89056b2a9ed5901c/31/Nothing/A063/Spacewar/80d7f4rs-a9i8-4h8d-848b-1288c47g082e",
		"System-Version":      "31",
		"Timezone":            "Greenwich Mean Time",
		"Seed":                "cdef0123456789abcdef0123456789abc",
		"Timezone-Offset-DST": "3600000",
		"Sign":                "Hello, Nothing!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "A063-user 12 TP1A.220624.014 1659416176 release-keys",
	},
	// Header Set 16 - Fairphone 4
	{
		"App-Version":         "1169",
		"Channel":             "Fairphone",
		"Device-Id-Weak":      "9a167c3baf06a12d",
		"Manufacturer":        "Fairphone",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "FP4",
		"Device-Id":           "91e805st-baj9-4i9e-959c-2399d58h193f",
		"Rom-Id":              "SP1A.210812.016",
		"DeviceName":          "FP4",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "Fairphone",
		"Timezone-Offset":     "3600000",
		"Model":               "FP4",
		"Resolution":          "1080x2340",
		"Screen-Density":      "409.00",
		"Build-Fingerprint":   "Fairphone/FP4/FP4:11/RKQ1.210503.001/FP3R:user/release-keys",
		"User-Agent":          "Android/1169/********/9a167c3baf06a12d/30/Fairphone/FP4/FP4/91e805st-baj9-4i9e-959c-2399d58h193f",
		"System-Version":      "30",
		"Timezone":            "Central European Time",
		"Seed":                "def0123456789abcdef0123456789abcd",
		"Timezone-Offset-DST": "7200000",
		"Sign":                "Hello, Fairphone!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "FP4-user 11 RKQ1.210503.001 FP3R release-keys",
	},
	// Header Set 17 - BlackBerry KEY2
	{
		"App-Version":         "1150",
		"Channel":             "BlackBerry",
		"Device-Id-Weak":      "ab278d4cc017b23e",
		"Manufacturer":        "BlackBerry",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "athena",
		"Device-Id":           "a2f916tu-cbka-4ja0-a6ad-34aa90421a40",
		"Rom-Id":              "PKQ1.181203.001",
		"DeviceName":          "BBF100-6",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "BlackBerry",
		"Timezone-Offset":     "-18000000",
		"Model":               "BBF100-6",
		"Resolution":          "1080x1620",
		"Screen-Density":      "434.00",
		"Build-Fingerprint":   "BlackBerry/athena/athena:8.1.0/PKQ1.181203.001/1234567890:user/release-keys",
		"User-Agent":          "Android/1150/********/ab278d4cc017b23e/27/BlackBerry/BBF100-6/athena/a2f916tu-cbka-4ja0-a6ad-34aa90421a40",
		"System-Version":      "27",
		"Timezone":            "Eastern Standard Time",
		"Seed":                "ef0123456789abcdef0123456789abcde",
		"Timezone-Offset-DST": "-14400000",
		"Sign":                "Hello, BlackBerry!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "BBF100-6-user 8.1.0 PKQ1.181203.001 1234567890 release-keys",
	},
	// Header Set 18 - Essential Phone
	{
		"App-Version":         "1151",
		"Channel":             "Essential",
		"Device-Id-Weak":      "bc389e5dd128c34f",
		"Manufacturer":        "Essential Products",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "mata",
		"Device-Id":           "b3fa27uv-dclb-4kb1-b7be-45bb91532b51",
		"Rom-Id":              "QQ1A.200105.032",
		"DeviceName":          "PH-1",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "Essential",
		"Timezone-Offset":     "-25200000",
		"Model":               "PH-1",
		"Resolution":          "1312x2560",
		"Screen-Density":      "503.00",
		"Build-Fingerprint":   "essential/mata/mata:10/QQ1A.200105.032/448:user/release-keys",
		"User-Agent":          "Android/1151/********/bc389e5dd128c34f/29/Essential Products/PH-1/mata/b3fa27uv-dclb-4kb1-b7be-45bb91532b51",
		"System-Version":      "29",
		"Timezone":            "Mountain Standard Time",
		"Seed":                "f0123456789abcdef0123456789abcdef",
		"Timezone-Offset-DST": "-21600000",
		"Sign":                "Hello, Essential!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "PH-1-user 10 QQ1A.200105.032 448 release-keys",
	},
	// Header Set 19 - Red Magic 6 Pro
	{
		"App-Version":         "1170",
		"Channel":             "Nubia",
		"Device-Id-Weak":      "cd49af6ee239d450",
		"Manufacturer":        "nubia",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "NX669J",
		"Device-Id":           "c40b38vw-edmc-4lc2-c8cf-56cc02643c62",
		"Rom-Id":              "RKQ1.200826.002",
		"DeviceName":          "NX669J",
		"Network":             "5G/",
		"App-Version-Code":    "********",
		"Brand":               "nubia",
		"Timezone-Offset":     "28800000",
		"Model":               "NX669J",
		"Resolution":          "1080x2400",
		"Screen-Density":      "395.00",
		"Build-Fingerprint":   "nubia/NX669J/NX669J:11/RKQ1.200826.002/nubia.20210420.001:user/release-keys",
		"User-Agent":          "Android/1170/********/cd49af6ee239d450/30/nubia/NX669J/NX669J/c40b38vw-edmc-4lc2-c8cf-56cc02643c62",
		"System-Version":      "30",
		"Timezone":            "China Standard Time",
		"Seed":                "0123456789abcdef0123456789abcdef0",
		"Timezone-Offset-DST": "28800000",
		"Sign":                "Hello, RedMagic!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "NX669J-user 11 RKQ1.200826.002 nubia.20210420.001 release-keys",
	},
	// Header Set 20 - Razer Phone 2
	{
		"App-Version":         "1152",
		"Channel":             "Razer",
		"Device-Id-Weak":      "de5ab07f9324e561",
		"Manufacturer":        "Razer",
		"Locale":              "en-US",
		"Locale-Supported":    "en-US",
		"Product":             "aura",
		"Device-Id":           "d51c49wx-fend-4md3-d9d0-67dd13754d73",
		"Rom-Id":              "P-SMR3-RC7-RZR-190220",
		"DeviceName":          "RZ35-0259",
		"Network":             "4G/",
		"App-Version-Code":    "********",
		"Brand":               "Razer",
		"Timezone-Offset":     "-28800000",
		"Model":               "RZ35-0259",
		"Resolution":          "1440x2560",
		"Screen-Density":      "515.00",
		"Build-Fingerprint":   "Razer/aura/aura:9/P-SMR3-RC7-RZR-190220/3831:user/release-keys",
		"User-Agent":          "Android/1152/********/de5ab07f9324e561/28/Razer/RZ35-0259/aura/d51c49wx-fend-4md3-d9d0-67dd13754d73",
		"System-Version":      "28",
		"Timezone":            "Pacific Standard Time",
		"Seed":                "123456789abcdef0123456789abcdef01",
		"Timezone-Offset-DST": "-25200000",
		"Sign":                "Hello, Razer!",
		"Host":                "buff.163.com",
		"Connection":          "Keep-Alive",
		"Accept-Encoding":     "gzip",
		"Rom":                 "RZ35-0259-user 9 P-SMR3-RC7-RZR-190220 3831 release-keys",
	},
}

// Backward compatibility - keep the original defaultHeaders variable
var defaultHeaders = defaultHeadersSets[0]
