// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateMany implements pgx.CopyFromSource.
type iteratorForCreateMany struct {
	rows                 []CreateManyParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateMany) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateMany) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].JobType,
		r.rows[0].Name,
		r.rows[0].Description,
		r.rows[0].Status,
		r.rows[0].CrawlConfigID,
		r.rows[0].ScheduledAt,
		r.rows[0].MaxRetries,
		r.rows[0].TimeoutSeconds,
		r.rows[0].CreatedBy,
	}, nil
}

func (r iteratorForCreateMany) Err() error {
	return nil
}

func (q *Queries) CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"jobs"}, []string{"job_type", "name", "description", "status", "crawl_config_id", "scheduled_at", "max_retries", "timeout_seconds", "created_by"}, &iteratorForCreateMany{rows: arg})
}
