package grpc

import (
	"context"
	app2 "go_core_market/internal/crawler/app"
	commandproxy "go_core_market/internal/crawler/app/command/proxy"
	queryproxy "go_core_market/internal/crawler/app/query/proxy"

	pb "go_core_market/pkg/pb/proxy"
)

type ProxyGRPCServer struct {
	app app2.Application
	pb.UnimplementedProxyServiceServer
}

func NewProxyGRPCServer(app app2.Application) *ProxyGRPCServer {
	return &ProxyGRPCServer{
		app: app,
	}
}

// Command handlers
func (s *ProxyGRPCServer) CreateProxy(ctx context.Context, req *pb.CreateProxyRequest) (*pb.CreateProxyResponse, error) {
	cmd := commandproxy.CreateProxy{
		Host:      req.Host,
		Port:      int(req.Port),
		UserName:  req.UserName,
		Password:  req.Password,
		IsActive:  req.IsActive,
		CreatedBy: req.CreatedBy,
	}

	err := s.app.Commands.Proxy.CreateProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateProxyResponse{
		Success: true,
		Message: "Proxy created successfully",
	}, nil
}

func (s *ProxyGRPCServer) CreateProxyBatch(ctx context.Context, req *pb.CreateProxyBatchRequest) (*pb.CreateProxyBatchResponse, error) {
	var proxies []commandproxy.CreateProxy
	for _, proxyReq := range req.Proxies {
		proxies = append(proxies, commandproxy.CreateProxy{
			Host:      proxyReq.Host,
			Port:      int(proxyReq.Port),
			UserName:  proxyReq.UserName,
			Password:  proxyReq.Password,
			IsActive:  proxyReq.IsActive,
			CreatedBy: proxyReq.CreatedBy,
		})
	}

	cmd := commandproxy.CreateProxyBatch{
		Proxies:   proxies,
		CreatedBy: req.CreatedBy,
	}

	err := s.app.Commands.Proxy.CreateProxyBatch.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateProxyBatchResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateProxyBatchResponse{
		Success: true,
		Message: "Proxy batch created successfully",
	}, nil
}

func (s *ProxyGRPCServer) UpdateProxy(ctx context.Context, req *pb.UpdateProxyRequest) (*pb.UpdateProxyResponse, error) {
	cmd := commandproxy.UpdateProxy{
		ID:        int(req.Id),
		Host:      req.Host,
		Port:      int(req.Port),
		UserName:  req.UserName,
		Password:  req.Password,
		UpdatedBy: req.UpdatedBy,
	}

	err := s.app.Commands.Proxy.UpdateProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.UpdateProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.UpdateProxyResponse{
		Success: true,
		Message: "Proxy updated successfully",
	}, nil
}

func (s *ProxyGRPCServer) DeleteProxy(ctx context.Context, req *pb.DeleteProxyRequest) (*pb.DeleteProxyResponse, error) {
	cmd := commandproxy.DeleteProxy{
		ID:        int(req.Id),
		DeletedBy: req.DeletedBy,
	}

	err := s.app.Commands.Proxy.DeleteProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeleteProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteProxyResponse{
		Success: true,
		Message: "Proxy deleted successfully",
	}, nil
}

func (s *ProxyGRPCServer) ActivateProxy(ctx context.Context, req *pb.ActivateProxyRequest) (*pb.ActivateProxyResponse, error) {
	cmd := commandproxy.ActivateProxy{
		ID:          int(req.Id),
		ActivatedBy: req.ActivatedBy,
	}

	err := s.app.Commands.Proxy.ActivateProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.ActivateProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ActivateProxyResponse{
		Success: true,
		Message: "Proxy activated successfully",
	}, nil
}

func (s *ProxyGRPCServer) DeactivateProxy(ctx context.Context, req *pb.DeactivateProxyRequest) (*pb.DeactivateProxyResponse, error) {
	cmd := commandproxy.DeactivateProxy{
		ID:            int(req.Id),
		DeactivatedBy: req.DeactivatedBy,
	}

	err := s.app.Commands.Proxy.DeactivateProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeactivateProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeactivateProxyResponse{
		Success: true,
		Message: "Proxy deactivated successfully",
	}, nil
}

func (s *ProxyGRPCServer) MarkProxyUsed(ctx context.Context, req *pb.MarkProxyUsedRequest) (*pb.MarkProxyUsedResponse, error) {
	cmd := commandproxy.MarkProxyUsed{
		ID:     int(req.Id),
		UsedBy: req.UsedBy,
	}

	err := s.app.Commands.Proxy.MarkProxyUsed.Handle(ctx, cmd)
	if err != nil {
		return &pb.MarkProxyUsedResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.MarkProxyUsedResponse{
		Success: true,
		Message: "Proxy marked as used successfully",
	}, nil
}

func (s *ProxyGRPCServer) MarkProxiesUsed(ctx context.Context, req *pb.MarkProxiesUsedRequest) (*pb.MarkProxiesUsedResponse, error) {
	var ids []int
	for _, id := range req.Ids {
		ids = append(ids, int(id))
	}

	cmd := commandproxy.MarkProxiesUsed{
		IDs:    ids,
		UsedBy: req.UsedBy,
	}

	err := s.app.Commands.Proxy.MarkProxiesUsed.Handle(ctx, cmd)
	if err != nil {
		return &pb.MarkProxiesUsedResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.MarkProxiesUsedResponse{
		Success: true,
		Message: "Proxies marked as used successfully",
	}, nil
}

func (s *ProxyGRPCServer) ReleaseProxy(ctx context.Context, req *pb.ReleaseProxyRequest) (*pb.ReleaseProxyResponse, error) {
	cmd := commandproxy.ReleaseProxy{
		ID:         int(req.Id),
		ReleasedBy: req.ReleasedBy,
	}

	err := s.app.Commands.Proxy.ReleaseProxy.Handle(ctx, cmd)
	if err != nil {
		return &pb.ReleaseProxyResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ReleaseProxyResponse{
		Success: true,
		Message: "Proxy released successfully",
	}, nil
}

func (s *ProxyGRPCServer) ReleaseManyProxies(ctx context.Context, req *pb.ReleaseManyProxiesRequest) (*pb.ReleaseManyProxiesResponse, error) {
	var ids []int
	for _, id := range req.Ids {
		ids = append(ids, int(id))
	}

	cmd := commandproxy.ReleaseManyProxies{
		IDs:        ids,
		ReleasedBy: req.ReleasedBy,
	}

	err := s.app.Commands.Proxy.ReleaseManyProxies.Handle(ctx, cmd)
	if err != nil {
		return &pb.ReleaseManyProxiesResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ReleaseManyProxiesResponse{
		Success: true,
		Message: "Proxies released successfully",
	}, nil
}

// Query handlers
func (s *ProxyGRPCServer) GetAllProxies(ctx context.Context, req *pb.GetAllProxiesRequest) (*pb.GetAllProxiesResponse, error) {
	q := queryproxy.GetAllProxiesQuery{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		OrderBy:  req.OrderBy,
	}

	result, err := s.app.Queries.Proxy.GetAllProxies.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbProxies []*pb.Proxy
	for _, proxy := range result.Data {
		pbProxies = append(pbProxies, ProxyToProto(proxy))
	}

	return &pb.GetAllProxiesResponse{
		Proxies:    pbProxies,
		Total:      int32(result.Pagination.Total),
		Page:       int32(result.Pagination.Page),
		PageSize:   int32(result.Pagination.PageSize),
		TotalPages: int32(result.Pagination.Pages),
	}, nil
}

func (s *ProxyGRPCServer) GetProxyById(ctx context.Context, req *pb.GetProxyByIdRequest) (*pb.GetProxyByIdResponse, error) {
	q := queryproxy.GetProxyByID{
		ID: int(req.Id),
	}

	proxy, err := s.app.Queries.Proxy.GetProxyByID.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetProxyByIdResponse{
		Proxy: ProxyToProto(proxy),
	}, nil
}

func (s *ProxyGRPCServer) GetAvailableProxies(ctx context.Context, req *pb.GetAvailableProxiesRequest) (*pb.GetAvailableProxiesResponse, error) {
	q := queryproxy.GetAvailableProxiesQuery{
		Limit: int(req.Limit),
	}

	proxies, err := s.app.Queries.Proxy.GetAvailableProxies.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbProxies []*pb.Proxy
	for _, proxy := range proxies {
		pbProxies = append(pbProxies, ProxyToProto(proxy))
	}

	return &pb.GetAvailableProxiesResponse{
		Proxies: pbProxies,
	}, nil
}
