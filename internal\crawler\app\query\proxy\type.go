package queryproxy

import (
	"fmt"
	"time"
)

type Proxy struct {
	Id         int
	Host       string
	Port       int
	UserName   string
	Password   string
	IsActive   bool
	IsUse      bool
	LastUsedAt *time.Time
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

func (proxy *Proxy) ToString() string {
	if proxy.UserName != "" && proxy.Password != "" {
		return fmt.Sprintf("%s:%s@%s:%d",
			proxy.UserName, proxy.Password, proxy.Host, proxy.Port)
	} else {
		return fmt.Sprintf("%s:%d", proxy.Host, proxy.Port)
	}
}
