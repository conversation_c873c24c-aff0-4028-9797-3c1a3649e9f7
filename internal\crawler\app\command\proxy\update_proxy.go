package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateProxy struct {
	ID        int
	Host      string
	Port      int
	UserName  string
	Password  string
	UpdatedBy string
}

type UpdateProxyHandler decorator.CommandHandler[UpdateProxy]

type updateProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewUpdateProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateProxy](
		updateProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateProxyHandler) Handle(ctx context.Context, cmd UpdateProxy) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Proxy) (*entity.Proxy, error) {
		err := p.UpdateInfo(cmd.Host, cmd.Port, cmd.UserName, cmd.Password)
		if err != nil {
			return nil, err
		}
		return p, nil
	})
}
