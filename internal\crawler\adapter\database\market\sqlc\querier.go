// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	CountMarketsQuery(ctx context.Context) (int64, error)
	Create(ctx context.Context, arg CreateParams) (int, error)
	CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error)
	FilterMarketsQuery(ctx context.Context, arg FilterMarketsQueryParams) ([]*Market, error)
	FindByID(ctx context.Context, id int) (*Market, error)
	FindByName(ctx context.Context, name string) (*Market, error)
	GetActiveMarketsQuery(ctx context.Context) ([]*Market, error)
	// Query methods for MarketQueryRepository
	GetAllMarketsWithPagination(ctx context.Context, arg GetAllMarketsWithPaginationParams) ([]*Market, error)
	GetMarketByIdQuery(ctx context.Context, id int) (*Market, error)
	UpdateMarket(ctx context.Context, arg UpdateMarketParams) (*Market, error)
}

var _ Querier = (*Queries)(nil)
