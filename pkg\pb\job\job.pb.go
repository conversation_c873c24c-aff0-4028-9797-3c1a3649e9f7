// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: job.proto

package job

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Job message
type Job struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	JobType       string                 `protobuf:"bytes,2,opt,name=job_type,json=jobType,proto3" json:"job_type,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Status        string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	CrawlConfigId *int32                 `protobuf:"varint,6,opt,name=crawl_config_id,json=crawlConfigId,proto3,oneof" json:"crawl_config_id,omitempty"`
	ScheduledAt   *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=scheduled_at,json=scheduledAt,proto3,oneof" json:"scheduled_at,omitempty"`
	StartedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=started_at,json=startedAt,proto3,oneof" json:"started_at,omitempty"`
	CompletedAt   *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=completed_at,json=completedAt,proto3,oneof" json:"completed_at,omitempty"`
	// Progress tracking
	CurrentStep        *int32  `protobuf:"varint,10,opt,name=current_step,json=currentStep,proto3,oneof" json:"current_step,omitempty"`
	TotalSteps         *int32  `protobuf:"varint,11,opt,name=total_steps,json=totalSteps,proto3,oneof" json:"total_steps,omitempty"`
	ProgressMessage    *string `protobuf:"bytes,12,opt,name=progress_message,json=progressMessage,proto3,oneof" json:"progress_message,omitempty"`
	ProgressPercentage *int32  `protobuf:"varint,13,opt,name=progress_percentage,json=progressPercentage,proto3,oneof" json:"progress_percentage,omitempty"`
	// Error handling
	MaxRetries     int32   `protobuf:"varint,14,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RetryCount     int32   `protobuf:"varint,15,opt,name=retry_count,json=retryCount,proto3" json:"retry_count,omitempty"`
	LastError      *string `protobuf:"bytes,16,opt,name=last_error,json=lastError,proto3,oneof" json:"last_error,omitempty"`
	TimeoutSeconds int32   `protobuf:"varint,17,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	// Metadata
	CreatedBy     string                 `protobuf:"bytes,18,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,19,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Job) Reset() {
	*x = Job{}
	mi := &file_job_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Job) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Job) ProtoMessage() {}

func (x *Job) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Job.ProtoReflect.Descriptor instead.
func (*Job) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{0}
}

func (x *Job) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Job) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *Job) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Job) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Job) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Job) GetCrawlConfigId() int32 {
	if x != nil && x.CrawlConfigId != nil {
		return *x.CrawlConfigId
	}
	return 0
}

func (x *Job) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *Job) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *Job) GetCompletedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CompletedAt
	}
	return nil
}

func (x *Job) GetCurrentStep() int32 {
	if x != nil && x.CurrentStep != nil {
		return *x.CurrentStep
	}
	return 0
}

func (x *Job) GetTotalSteps() int32 {
	if x != nil && x.TotalSteps != nil {
		return *x.TotalSteps
	}
	return 0
}

func (x *Job) GetProgressMessage() string {
	if x != nil && x.ProgressMessage != nil {
		return *x.ProgressMessage
	}
	return ""
}

func (x *Job) GetProgressPercentage() int32 {
	if x != nil && x.ProgressPercentage != nil {
		return *x.ProgressPercentage
	}
	return 0
}

func (x *Job) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *Job) GetRetryCount() int32 {
	if x != nil {
		return x.RetryCount
	}
	return 0
}

func (x *Job) GetLastError() string {
	if x != nil && x.LastError != nil {
		return *x.LastError
	}
	return ""
}

func (x *Job) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *Job) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

func (x *Job) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Job) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// JobProgress message
type JobProgress struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CurrentStep   int32                  `protobuf:"varint,1,opt,name=current_step,json=currentStep,proto3" json:"current_step,omitempty"`
	TotalSteps    int32                  `protobuf:"varint,2,opt,name=total_steps,json=totalSteps,proto3" json:"total_steps,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	Percentage    int32                  `protobuf:"varint,4,opt,name=percentage,proto3" json:"percentage,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JobProgress) Reset() {
	*x = JobProgress{}
	mi := &file_job_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JobProgress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JobProgress) ProtoMessage() {}

func (x *JobProgress) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JobProgress.ProtoReflect.Descriptor instead.
func (*JobProgress) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{1}
}

func (x *JobProgress) GetCurrentStep() int32 {
	if x != nil {
		return x.CurrentStep
	}
	return 0
}

func (x *JobProgress) GetTotalSteps() int32 {
	if x != nil {
		return x.TotalSteps
	}
	return 0
}

func (x *JobProgress) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *JobProgress) GetPercentage() int32 {
	if x != nil {
		return x.Percentage
	}
	return 0
}

// Request/Response messages for Commands
type CreateJobRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	JobType        string                 `protobuf:"bytes,1,opt,name=job_type,json=jobType,proto3" json:"job_type,omitempty"`
	Name           string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Description    string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	CrawlConfigId  *int32                 `protobuf:"varint,4,opt,name=crawl_config_id,json=crawlConfigId,proto3,oneof" json:"crawl_config_id,omitempty"`
	ScheduledAt    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=scheduled_at,json=scheduledAt,proto3,oneof" json:"scheduled_at,omitempty"`
	MaxRetries     int32                  `protobuf:"varint,6,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	TimeoutSeconds int32                  `protobuf:"varint,7,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	CreatedBy      string                 `protobuf:"bytes,8,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateJobRequest) Reset() {
	*x = CreateJobRequest{}
	mi := &file_job_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobRequest) ProtoMessage() {}

func (x *CreateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobRequest.ProtoReflect.Descriptor instead.
func (*CreateJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{2}
}

func (x *CreateJobRequest) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *CreateJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateJobRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateJobRequest) GetCrawlConfigId() int32 {
	if x != nil && x.CrawlConfigId != nil {
		return *x.CrawlConfigId
	}
	return 0
}

func (x *CreateJobRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *CreateJobRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *CreateJobRequest) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *CreateJobRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	JobId         *int32                 `protobuf:"varint,3,opt,name=job_id,json=jobId,proto3,oneof" json:"job_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateJobResponse) Reset() {
	*x = CreateJobResponse{}
	mi := &file_job_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateJobResponse) ProtoMessage() {}

func (x *CreateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateJobResponse.ProtoReflect.Descriptor instead.
func (*CreateJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{3}
}

func (x *CreateJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateJobResponse) GetJobId() int32 {
	if x != nil && x.JobId != nil {
		return *x.JobId
	}
	return 0
}

type UpdateJobRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Id             int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	JobType        string                 `protobuf:"bytes,2,opt,name=job_type,json=jobType,proto3" json:"job_type,omitempty"`
	Name           string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Description    string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	CrawlConfigId  *int32                 `protobuf:"varint,5,opt,name=crawl_config_id,json=crawlConfigId,proto3,oneof" json:"crawl_config_id,omitempty"`
	ScheduledAt    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=scheduled_at,json=scheduledAt,proto3,oneof" json:"scheduled_at,omitempty"`
	MaxRetries     int32                  `protobuf:"varint,7,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	TimeoutSeconds int32                  `protobuf:"varint,8,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	UpdatedBy      string                 `protobuf:"bytes,9,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateJobRequest) Reset() {
	*x = UpdateJobRequest{}
	mi := &file_job_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobRequest) ProtoMessage() {}

func (x *UpdateJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateJobRequest) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

func (x *UpdateJobRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateJobRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateJobRequest) GetCrawlConfigId() int32 {
	if x != nil && x.CrawlConfigId != nil {
		return *x.CrawlConfigId
	}
	return 0
}

func (x *UpdateJobRequest) GetScheduledAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ScheduledAt
	}
	return nil
}

func (x *UpdateJobRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *UpdateJobRequest) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *UpdateJobRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobResponse) Reset() {
	*x = UpdateJobResponse{}
	mi := &file_job_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobResponse) ProtoMessage() {}

func (x *UpdateJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateJobStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Status        string                 `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,3,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobStatusRequest) Reset() {
	*x = UpdateJobStatusRequest{}
	mi := &file_job_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobStatusRequest) ProtoMessage() {}

func (x *UpdateJobStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobStatusRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobStatusRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateJobStatusRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateJobStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *UpdateJobStatusRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateJobStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobStatusResponse) Reset() {
	*x = UpdateJobStatusResponse{}
	mi := &file_job_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobStatusResponse) ProtoMessage() {}

func (x *UpdateJobStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobStatusResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobStatusResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateJobStatusResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateJobStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateJobProgressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Progress      *JobProgress           `protobuf:"bytes,2,opt,name=progress,proto3" json:"progress,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,3,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobProgressRequest) Reset() {
	*x = UpdateJobProgressRequest{}
	mi := &file_job_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobProgressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobProgressRequest) ProtoMessage() {}

func (x *UpdateJobProgressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobProgressRequest.ProtoReflect.Descriptor instead.
func (*UpdateJobProgressRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateJobProgressRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateJobProgressRequest) GetProgress() *JobProgress {
	if x != nil {
		return x.Progress
	}
	return nil
}

func (x *UpdateJobProgressRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateJobProgressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateJobProgressResponse) Reset() {
	*x = UpdateJobProgressResponse{}
	mi := &file_job_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateJobProgressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateJobProgressResponse) ProtoMessage() {}

func (x *UpdateJobProgressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateJobProgressResponse.ProtoReflect.Descriptor instead.
func (*UpdateJobProgressResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateJobProgressResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateJobProgressResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type StartJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	StartedBy     string                 `protobuf:"bytes,2,opt,name=started_by,json=startedBy,proto3" json:"started_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartJobRequest) Reset() {
	*x = StartJobRequest{}
	mi := &file_job_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartJobRequest) ProtoMessage() {}

func (x *StartJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartJobRequest.ProtoReflect.Descriptor instead.
func (*StartJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{10}
}

func (x *StartJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *StartJobRequest) GetStartedBy() string {
	if x != nil {
		return x.StartedBy
	}
	return ""
}

type StartJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartJobResponse) Reset() {
	*x = StartJobResponse{}
	mi := &file_job_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartJobResponse) ProtoMessage() {}

func (x *StartJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartJobResponse.ProtoReflect.Descriptor instead.
func (*StartJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{11}
}

func (x *StartJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *StartJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CompleteJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CompletedBy   string                 `protobuf:"bytes,2,opt,name=completed_by,json=completedBy,proto3" json:"completed_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompleteJobRequest) Reset() {
	*x = CompleteJobRequest{}
	mi := &file_job_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteJobRequest) ProtoMessage() {}

func (x *CompleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteJobRequest.ProtoReflect.Descriptor instead.
func (*CompleteJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{12}
}

func (x *CompleteJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CompleteJobRequest) GetCompletedBy() string {
	if x != nil {
		return x.CompletedBy
	}
	return ""
}

type CompleteJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CompleteJobResponse) Reset() {
	*x = CompleteJobResponse{}
	mi := &file_job_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompleteJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompleteJobResponse) ProtoMessage() {}

func (x *CompleteJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompleteJobResponse.ProtoReflect.Descriptor instead.
func (*CompleteJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{13}
}

func (x *CompleteJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CompleteJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type FailJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ErrorMessage  string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	FailedBy      string                 `protobuf:"bytes,3,opt,name=failed_by,json=failedBy,proto3" json:"failed_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FailJobRequest) Reset() {
	*x = FailJobRequest{}
	mi := &file_job_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FailJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailJobRequest) ProtoMessage() {}

func (x *FailJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailJobRequest.ProtoReflect.Descriptor instead.
func (*FailJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{14}
}

func (x *FailJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FailJobRequest) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *FailJobRequest) GetFailedBy() string {
	if x != nil {
		return x.FailedBy
	}
	return ""
}

type FailJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FailJobResponse) Reset() {
	*x = FailJobResponse{}
	mi := &file_job_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FailJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FailJobResponse) ProtoMessage() {}

func (x *FailJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FailJobResponse.ProtoReflect.Descriptor instead.
func (*FailJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{15}
}

func (x *FailJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *FailJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CancelJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CancelledBy   string                 `protobuf:"bytes,2,opt,name=cancelled_by,json=cancelledBy,proto3" json:"cancelled_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelJobRequest) Reset() {
	*x = CancelJobRequest{}
	mi := &file_job_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobRequest) ProtoMessage() {}

func (x *CancelJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobRequest.ProtoReflect.Descriptor instead.
func (*CancelJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{16}
}

func (x *CancelJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CancelJobRequest) GetCancelledBy() string {
	if x != nil {
		return x.CancelledBy
	}
	return ""
}

type CancelJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CancelJobResponse) Reset() {
	*x = CancelJobResponse{}
	mi := &file_job_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CancelJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelJobResponse) ProtoMessage() {}

func (x *CancelJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelJobResponse.ProtoReflect.Descriptor instead.
func (*CancelJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{17}
}

func (x *CancelJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CancelJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type RetryJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	RetriedBy     string                 `protobuf:"bytes,2,opt,name=retried_by,json=retriedBy,proto3" json:"retried_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RetryJobRequest) Reset() {
	*x = RetryJobRequest{}
	mi := &file_job_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryJobRequest) ProtoMessage() {}

func (x *RetryJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryJobRequest.ProtoReflect.Descriptor instead.
func (*RetryJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{18}
}

func (x *RetryJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RetryJobRequest) GetRetriedBy() string {
	if x != nil {
		return x.RetriedBy
	}
	return ""
}

type RetryJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RetryJobResponse) Reset() {
	*x = RetryJobResponse{}
	mi := &file_job_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetryJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetryJobResponse) ProtoMessage() {}

func (x *RetryJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetryJobResponse.ProtoReflect.Descriptor instead.
func (*RetryJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{19}
}

func (x *RetryJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *RetryJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteJobRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     string                 `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteJobRequest) Reset() {
	*x = DeleteJobRequest{}
	mi := &file_job_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteJobRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobRequest) ProtoMessage() {}

func (x *DeleteJobRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobRequest.ProtoReflect.Descriptor instead.
func (*DeleteJobRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{20}
}

func (x *DeleteJobRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteJobRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

type DeleteJobResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteJobResponse) Reset() {
	*x = DeleteJobResponse{}
	mi := &file_job_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteJobResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteJobResponse) ProtoMessage() {}

func (x *DeleteJobResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteJobResponse.ProtoReflect.Descriptor instead.
func (*DeleteJobResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{21}
}

func (x *DeleteJobResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteJobResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Request/Response messages for Queries
type GetAllJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OrderBy       string                 `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllJobsRequest) Reset() {
	*x = GetAllJobsRequest{}
	mi := &file_job_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllJobsRequest) ProtoMessage() {}

func (x *GetAllJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllJobsRequest.ProtoReflect.Descriptor instead.
func (*GetAllJobsRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{22}
}

func (x *GetAllJobsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllJobsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllJobsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type GetAllJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalPages    int32                  `protobuf:"varint,5,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllJobsResponse) Reset() {
	*x = GetAllJobsResponse{}
	mi := &file_job_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllJobsResponse) ProtoMessage() {}

func (x *GetAllJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllJobsResponse.ProtoReflect.Descriptor instead.
func (*GetAllJobsResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{23}
}

func (x *GetAllJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

func (x *GetAllJobsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAllJobsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllJobsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllJobsResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

type GetJobByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobByIdRequest) Reset() {
	*x = GetJobByIdRequest{}
	mi := &file_job_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobByIdRequest) ProtoMessage() {}

func (x *GetJobByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobByIdRequest.ProtoReflect.Descriptor instead.
func (*GetJobByIdRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{24}
}

func (x *GetJobByIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetJobByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Job           *Job                   `protobuf:"bytes,1,opt,name=job,proto3" json:"job,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobByIdResponse) Reset() {
	*x = GetJobByIdResponse{}
	mi := &file_job_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobByIdResponse) ProtoMessage() {}

func (x *GetJobByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobByIdResponse.ProtoReflect.Descriptor instead.
func (*GetJobByIdResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{25}
}

func (x *GetJobByIdResponse) GetJob() *Job {
	if x != nil {
		return x.Job
	}
	return nil
}

type GetJobsByStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        string                 `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByStatusRequest) Reset() {
	*x = GetJobsByStatusRequest{}
	mi := &file_job_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByStatusRequest) ProtoMessage() {}

func (x *GetJobsByStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByStatusRequest.ProtoReflect.Descriptor instead.
func (*GetJobsByStatusRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{26}
}

func (x *GetJobsByStatusRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetJobsByStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByStatusResponse) Reset() {
	*x = GetJobsByStatusResponse{}
	mi := &file_job_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByStatusResponse) ProtoMessage() {}

func (x *GetJobsByStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByStatusResponse.ProtoReflect.Descriptor instead.
func (*GetJobsByStatusResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{27}
}

func (x *GetJobsByStatusResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetJobsByTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobType       string                 `protobuf:"bytes,1,opt,name=job_type,json=jobType,proto3" json:"job_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByTypeRequest) Reset() {
	*x = GetJobsByTypeRequest{}
	mi := &file_job_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByTypeRequest) ProtoMessage() {}

func (x *GetJobsByTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByTypeRequest.ProtoReflect.Descriptor instead.
func (*GetJobsByTypeRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{28}
}

func (x *GetJobsByTypeRequest) GetJobType() string {
	if x != nil {
		return x.JobType
	}
	return ""
}

type GetJobsByTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByTypeResponse) Reset() {
	*x = GetJobsByTypeResponse{}
	mi := &file_job_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByTypeResponse) ProtoMessage() {}

func (x *GetJobsByTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByTypeResponse.ProtoReflect.Descriptor instead.
func (*GetJobsByTypeResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{29}
}

func (x *GetJobsByTypeResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetPendingJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingJobsRequest) Reset() {
	*x = GetPendingJobsRequest{}
	mi := &file_job_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingJobsRequest) ProtoMessage() {}

func (x *GetPendingJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingJobsRequest.ProtoReflect.Descriptor instead.
func (*GetPendingJobsRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{30}
}

type GetPendingJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPendingJobsResponse) Reset() {
	*x = GetPendingJobsResponse{}
	mi := &file_job_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPendingJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPendingJobsResponse) ProtoMessage() {}

func (x *GetPendingJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPendingJobsResponse.ProtoReflect.Descriptor instead.
func (*GetPendingJobsResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{31}
}

func (x *GetPendingJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetRunningJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRunningJobsRequest) Reset() {
	*x = GetRunningJobsRequest{}
	mi := &file_job_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRunningJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunningJobsRequest) ProtoMessage() {}

func (x *GetRunningJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunningJobsRequest.ProtoReflect.Descriptor instead.
func (*GetRunningJobsRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{32}
}

type GetRunningJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRunningJobsResponse) Reset() {
	*x = GetRunningJobsResponse{}
	mi := &file_job_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRunningJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRunningJobsResponse) ProtoMessage() {}

func (x *GetRunningJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRunningJobsResponse.ProtoReflect.Descriptor instead.
func (*GetRunningJobsResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{33}
}

func (x *GetRunningJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetJobsByConfigIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CrawlConfigId int32                  `protobuf:"varint,1,opt,name=crawl_config_id,json=crawlConfigId,proto3" json:"crawl_config_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByConfigIdRequest) Reset() {
	*x = GetJobsByConfigIdRequest{}
	mi := &file_job_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByConfigIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByConfigIdRequest) ProtoMessage() {}

func (x *GetJobsByConfigIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByConfigIdRequest.ProtoReflect.Descriptor instead.
func (*GetJobsByConfigIdRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{34}
}

func (x *GetJobsByConfigIdRequest) GetCrawlConfigId() int32 {
	if x != nil {
		return x.CrawlConfigId
	}
	return 0
}

type GetJobsByConfigIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsByConfigIdResponse) Reset() {
	*x = GetJobsByConfigIdResponse{}
	mi := &file_job_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsByConfigIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsByConfigIdResponse) ProtoMessage() {}

func (x *GetJobsByConfigIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsByConfigIdResponse.ProtoReflect.Descriptor instead.
func (*GetJobsByConfigIdResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{35}
}

func (x *GetJobsByConfigIdResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type FilterJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobType       *string                `protobuf:"bytes,1,opt,name=job_type,json=jobType,proto3,oneof" json:"job_type,omitempty"`
	Status        *string                `protobuf:"bytes,2,opt,name=status,proto3,oneof" json:"status,omitempty"`
	CrawlConfigId *int32                 `protobuf:"varint,3,opt,name=crawl_config_id,json=crawlConfigId,proto3,oneof" json:"crawl_config_id,omitempty"`
	CreatedBy     *string                `protobuf:"bytes,4,opt,name=created_by,json=createdBy,proto3,oneof" json:"created_by,omitempty"`
	CreatedFrom   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_from,json=createdFrom,proto3,oneof" json:"created_from,omitempty"`
	CreatedTo     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_to,json=createdTo,proto3,oneof" json:"created_to,omitempty"`
	OrderBy       string                 `protobuf:"bytes,7,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterJobsRequest) Reset() {
	*x = FilterJobsRequest{}
	mi := &file_job_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterJobsRequest) ProtoMessage() {}

func (x *FilterJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterJobsRequest.ProtoReflect.Descriptor instead.
func (*FilterJobsRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{36}
}

func (x *FilterJobsRequest) GetJobType() string {
	if x != nil && x.JobType != nil {
		return *x.JobType
	}
	return ""
}

func (x *FilterJobsRequest) GetStatus() string {
	if x != nil && x.Status != nil {
		return *x.Status
	}
	return ""
}

func (x *FilterJobsRequest) GetCrawlConfigId() int32 {
	if x != nil && x.CrawlConfigId != nil {
		return *x.CrawlConfigId
	}
	return 0
}

func (x *FilterJobsRequest) GetCreatedBy() string {
	if x != nil && x.CreatedBy != nil {
		return *x.CreatedBy
	}
	return ""
}

func (x *FilterJobsRequest) GetCreatedFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedFrom
	}
	return nil
}

func (x *FilterJobsRequest) GetCreatedTo() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTo
	}
	return nil
}

func (x *FilterJobsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type FilterJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterJobsResponse) Reset() {
	*x = FilterJobsResponse{}
	mi := &file_job_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterJobsResponse) ProtoMessage() {}

func (x *FilterJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterJobsResponse.ProtoReflect.Descriptor instead.
func (*FilterJobsResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{37}
}

func (x *FilterJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetJobsNeedingRetryRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsNeedingRetryRequest) Reset() {
	*x = GetJobsNeedingRetryRequest{}
	mi := &file_job_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsNeedingRetryRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsNeedingRetryRequest) ProtoMessage() {}

func (x *GetJobsNeedingRetryRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsNeedingRetryRequest.ProtoReflect.Descriptor instead.
func (*GetJobsNeedingRetryRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{38}
}

type GetJobsNeedingRetryResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetJobsNeedingRetryResponse) Reset() {
	*x = GetJobsNeedingRetryResponse{}
	mi := &file_job_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetJobsNeedingRetryResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetJobsNeedingRetryResponse) ProtoMessage() {}

func (x *GetJobsNeedingRetryResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetJobsNeedingRetryResponse.ProtoReflect.Descriptor instead.
func (*GetJobsNeedingRetryResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{39}
}

func (x *GetJobsNeedingRetryResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

type GetExpiredJobsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetExpiredJobsRequest) Reset() {
	*x = GetExpiredJobsRequest{}
	mi := &file_job_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExpiredJobsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpiredJobsRequest) ProtoMessage() {}

func (x *GetExpiredJobsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpiredJobsRequest.ProtoReflect.Descriptor instead.
func (*GetExpiredJobsRequest) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{40}
}

type GetExpiredJobsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Jobs          []*Job                 `protobuf:"bytes,1,rep,name=jobs,proto3" json:"jobs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetExpiredJobsResponse) Reset() {
	*x = GetExpiredJobsResponse{}
	mi := &file_job_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetExpiredJobsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExpiredJobsResponse) ProtoMessage() {}

func (x *GetExpiredJobsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_job_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExpiredJobsResponse.ProtoReflect.Descriptor instead.
func (*GetExpiredJobsResponse) Descriptor() ([]byte, []int) {
	return file_job_proto_rawDescGZIP(), []int{41}
}

func (x *GetExpiredJobsResponse) GetJobs() []*Job {
	if x != nil {
		return x.Jobs
	}
	return nil
}

var File_job_proto protoreflect.FileDescriptor

const file_job_proto_rawDesc = "" +
	"\n" +
	"\tjob.proto\x12\x06job.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xed\a\n" +
	"\x03Job\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x19\n" +
	"\bjob_type\x18\x02 \x01(\tR\ajobType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12+\n" +
	"\x0fcrawl_config_id\x18\x06 \x01(\x05H\x00R\rcrawlConfigId\x88\x01\x01\x12B\n" +
	"\fscheduled_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampH\x01R\vscheduledAt\x88\x01\x01\x12>\n" +
	"\n" +
	"started_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x02R\tstartedAt\x88\x01\x01\x12B\n" +
	"\fcompleted_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x03R\vcompletedAt\x88\x01\x01\x12&\n" +
	"\fcurrent_step\x18\n" +
	" \x01(\x05H\x04R\vcurrentStep\x88\x01\x01\x12$\n" +
	"\vtotal_steps\x18\v \x01(\x05H\x05R\n" +
	"totalSteps\x88\x01\x01\x12.\n" +
	"\x10progress_message\x18\f \x01(\tH\x06R\x0fprogressMessage\x88\x01\x01\x124\n" +
	"\x13progress_percentage\x18\r \x01(\x05H\aR\x12progressPercentage\x88\x01\x01\x12\x1f\n" +
	"\vmax_retries\x18\x0e \x01(\x05R\n" +
	"maxRetries\x12\x1f\n" +
	"\vretry_count\x18\x0f \x01(\x05R\n" +
	"retryCount\x12\"\n" +
	"\n" +
	"last_error\x18\x10 \x01(\tH\bR\tlastError\x88\x01\x01\x12'\n" +
	"\x0ftimeout_seconds\x18\x11 \x01(\x05R\x0etimeoutSeconds\x12\x1d\n" +
	"\n" +
	"created_by\x18\x12 \x01(\tR\tcreatedBy\x129\n" +
	"\n" +
	"created_at\x18\x13 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\x12\n" +
	"\x10_crawl_config_idB\x0f\n" +
	"\r_scheduled_atB\r\n" +
	"\v_started_atB\x0f\n" +
	"\r_completed_atB\x0f\n" +
	"\r_current_stepB\x0e\n" +
	"\f_total_stepsB\x13\n" +
	"\x11_progress_messageB\x16\n" +
	"\x14_progress_percentageB\r\n" +
	"\v_last_error\"\x8b\x01\n" +
	"\vJobProgress\x12!\n" +
	"\fcurrent_step\x18\x01 \x01(\x05R\vcurrentStep\x12\x1f\n" +
	"\vtotal_steps\x18\x02 \x01(\x05R\n" +
	"totalSteps\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"percentage\x18\x04 \x01(\x05R\n" +
	"percentage\"\xe2\x02\n" +
	"\x10CreateJobRequest\x12\x19\n" +
	"\bjob_type\x18\x01 \x01(\tR\ajobType\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12+\n" +
	"\x0fcrawl_config_id\x18\x04 \x01(\x05H\x00R\rcrawlConfigId\x88\x01\x01\x12B\n" +
	"\fscheduled_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\vscheduledAt\x88\x01\x01\x12\x1f\n" +
	"\vmax_retries\x18\x06 \x01(\x05R\n" +
	"maxRetries\x12'\n" +
	"\x0ftimeout_seconds\x18\a \x01(\x05R\x0etimeoutSeconds\x12\x1d\n" +
	"\n" +
	"created_by\x18\b \x01(\tR\tcreatedByB\x12\n" +
	"\x10_crawl_config_idB\x0f\n" +
	"\r_scheduled_at\"n\n" +
	"\x11CreateJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\x06job_id\x18\x03 \x01(\x05H\x00R\x05jobId\x88\x01\x01B\t\n" +
	"\a_job_id\"\xf2\x02\n" +
	"\x10UpdateJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x19\n" +
	"\bjob_type\x18\x02 \x01(\tR\ajobType\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12+\n" +
	"\x0fcrawl_config_id\x18\x05 \x01(\x05H\x00R\rcrawlConfigId\x88\x01\x01\x12B\n" +
	"\fscheduled_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\vscheduledAt\x88\x01\x01\x12\x1f\n" +
	"\vmax_retries\x18\a \x01(\x05R\n" +
	"maxRetries\x12'\n" +
	"\x0ftimeout_seconds\x18\b \x01(\x05R\x0etimeoutSeconds\x12\x1d\n" +
	"\n" +
	"updated_by\x18\t \x01(\tR\tupdatedByB\x12\n" +
	"\x10_crawl_config_idB\x0f\n" +
	"\r_scheduled_at\"G\n" +
	"\x11UpdateJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"_\n" +
	"\x16UpdateJobStatusRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x03 \x01(\tR\tupdatedBy\"M\n" +
	"\x17UpdateJobStatusResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"z\n" +
	"\x18UpdateJobProgressRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12/\n" +
	"\bprogress\x18\x02 \x01(\v2\x13.job.v1.JobProgressR\bprogress\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x03 \x01(\tR\tupdatedBy\"O\n" +
	"\x19UpdateJobProgressResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"@\n" +
	"\x0fStartJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"started_by\x18\x02 \x01(\tR\tstartedBy\"F\n" +
	"\x10StartJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"G\n" +
	"\x12CompleteJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\fcompleted_by\x18\x02 \x01(\tR\vcompletedBy\"I\n" +
	"\x13CompleteJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"b\n" +
	"\x0eFailJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12#\n" +
	"\rerror_message\x18\x02 \x01(\tR\ferrorMessage\x12\x1b\n" +
	"\tfailed_by\x18\x03 \x01(\tR\bfailedBy\"E\n" +
	"\x0fFailJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"E\n" +
	"\x10CancelJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\fcancelled_by\x18\x02 \x01(\tR\vcancelledBy\"G\n" +
	"\x11CancelJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"@\n" +
	"\x0fRetryJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"retried_by\x18\x02 \x01(\tR\tretriedBy\"F\n" +
	"\x10RetryJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"A\n" +
	"\x10DeleteJobRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\tR\tdeletedBy\"G\n" +
	"\x11DeleteJobResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"_\n" +
	"\x11GetAllJobsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x19\n" +
	"\border_by\x18\x03 \x01(\tR\aorderBy\"\x9d\x01\n" +
	"\x12GetAllJobsResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_pages\x18\x05 \x01(\x05R\n" +
	"totalPages\"#\n" +
	"\x11GetJobByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"3\n" +
	"\x12GetJobByIdResponse\x12\x1d\n" +
	"\x03job\x18\x01 \x01(\v2\v.job.v1.JobR\x03job\"0\n" +
	"\x16GetJobsByStatusRequest\x12\x16\n" +
	"\x06status\x18\x01 \x01(\tR\x06status\":\n" +
	"\x17GetJobsByStatusResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"1\n" +
	"\x14GetJobsByTypeRequest\x12\x19\n" +
	"\bjob_type\x18\x01 \x01(\tR\ajobType\"8\n" +
	"\x15GetJobsByTypeResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"\x17\n" +
	"\x15GetPendingJobsRequest\"9\n" +
	"\x16GetPendingJobsResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"\x17\n" +
	"\x15GetRunningJobsRequest\"9\n" +
	"\x16GetRunningJobsResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"B\n" +
	"\x18GetJobsByConfigIdRequest\x12&\n" +
	"\x0fcrawl_config_id\x18\x01 \x01(\x05R\rcrawlConfigId\"<\n" +
	"\x19GetJobsByConfigIdResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"\x9b\x03\n" +
	"\x11FilterJobsRequest\x12\x1e\n" +
	"\bjob_type\x18\x01 \x01(\tH\x00R\ajobType\x88\x01\x01\x12\x1b\n" +
	"\x06status\x18\x02 \x01(\tH\x01R\x06status\x88\x01\x01\x12+\n" +
	"\x0fcrawl_config_id\x18\x03 \x01(\x05H\x02R\rcrawlConfigId\x88\x01\x01\x12\"\n" +
	"\n" +
	"created_by\x18\x04 \x01(\tH\x03R\tcreatedBy\x88\x01\x01\x12B\n" +
	"\fcreated_from\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x04R\vcreatedFrom\x88\x01\x01\x12>\n" +
	"\n" +
	"created_to\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampH\x05R\tcreatedTo\x88\x01\x01\x12\x19\n" +
	"\border_by\x18\a \x01(\tR\aorderByB\v\n" +
	"\t_job_typeB\t\n" +
	"\a_statusB\x12\n" +
	"\x10_crawl_config_idB\r\n" +
	"\v_created_byB\x0f\n" +
	"\r_created_fromB\r\n" +
	"\v_created_to\"5\n" +
	"\x12FilterJobsResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"\x1c\n" +
	"\x1aGetJobsNeedingRetryRequest\">\n" +
	"\x1bGetJobsNeedingRetryResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs\"\x17\n" +
	"\x15GetExpiredJobsRequest\"9\n" +
	"\x16GetExpiredJobsResponse\x12\x1f\n" +
	"\x04jobs\x18\x01 \x03(\v2\v.job.v1.JobR\x04jobs2\xe2\v\n" +
	"\n" +
	"JobService\x12@\n" +
	"\tCreateJob\x12\x18.job.v1.CreateJobRequest\x1a\x19.job.v1.CreateJobResponse\x12@\n" +
	"\tUpdateJob\x12\x18.job.v1.UpdateJobRequest\x1a\x19.job.v1.UpdateJobResponse\x12R\n" +
	"\x0fUpdateJobStatus\x12\x1e.job.v1.UpdateJobStatusRequest\x1a\x1f.job.v1.UpdateJobStatusResponse\x12X\n" +
	"\x11UpdateJobProgress\x12 .job.v1.UpdateJobProgressRequest\x1a!.job.v1.UpdateJobProgressResponse\x12=\n" +
	"\bStartJob\x12\x17.job.v1.StartJobRequest\x1a\x18.job.v1.StartJobResponse\x12F\n" +
	"\vCompleteJob\x12\x1a.job.v1.CompleteJobRequest\x1a\x1b.job.v1.CompleteJobResponse\x12:\n" +
	"\aFailJob\x12\x16.job.v1.FailJobRequest\x1a\x17.job.v1.FailJobResponse\x12@\n" +
	"\tCancelJob\x12\x18.job.v1.CancelJobRequest\x1a\x19.job.v1.CancelJobResponse\x12=\n" +
	"\bRetryJob\x12\x17.job.v1.RetryJobRequest\x1a\x18.job.v1.RetryJobResponse\x12@\n" +
	"\tDeleteJob\x12\x18.job.v1.DeleteJobRequest\x1a\x19.job.v1.DeleteJobResponse\x12C\n" +
	"\n" +
	"GetAllJobs\x12\x19.job.v1.GetAllJobsRequest\x1a\x1a.job.v1.GetAllJobsResponse\x12C\n" +
	"\n" +
	"GetJobById\x12\x19.job.v1.GetJobByIdRequest\x1a\x1a.job.v1.GetJobByIdResponse\x12R\n" +
	"\x0fGetJobsByStatus\x12\x1e.job.v1.GetJobsByStatusRequest\x1a\x1f.job.v1.GetJobsByStatusResponse\x12L\n" +
	"\rGetJobsByType\x12\x1c.job.v1.GetJobsByTypeRequest\x1a\x1d.job.v1.GetJobsByTypeResponse\x12O\n" +
	"\x0eGetPendingJobs\x12\x1d.job.v1.GetPendingJobsRequest\x1a\x1e.job.v1.GetPendingJobsResponse\x12O\n" +
	"\x0eGetRunningJobs\x12\x1d.job.v1.GetRunningJobsRequest\x1a\x1e.job.v1.GetRunningJobsResponse\x12X\n" +
	"\x11GetJobsByConfigId\x12 .job.v1.GetJobsByConfigIdRequest\x1a!.job.v1.GetJobsByConfigIdResponse\x12C\n" +
	"\n" +
	"FilterJobs\x12\x19.job.v1.FilterJobsRequest\x1a\x1a.job.v1.FilterJobsResponse\x12^\n" +
	"\x13GetJobsNeedingRetry\x12\".job.v1.GetJobsNeedingRetryRequest\x1a#.job.v1.GetJobsNeedingRetryResponse\x12O\n" +
	"\x0eGetExpiredJobs\x12\x1d.job.v1.GetExpiredJobsRequest\x1a\x1e.job.v1.GetExpiredJobsResponseB\x1bZ\x19go_core_market/pkg/pb/jobb\x06proto3"

var (
	file_job_proto_rawDescOnce sync.Once
	file_job_proto_rawDescData []byte
)

func file_job_proto_rawDescGZIP() []byte {
	file_job_proto_rawDescOnce.Do(func() {
		file_job_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_job_proto_rawDesc), len(file_job_proto_rawDesc)))
	})
	return file_job_proto_rawDescData
}

var file_job_proto_msgTypes = make([]protoimpl.MessageInfo, 42)
var file_job_proto_goTypes = []any{
	(*Job)(nil),                         // 0: job.v1.Job
	(*JobProgress)(nil),                 // 1: job.v1.JobProgress
	(*CreateJobRequest)(nil),            // 2: job.v1.CreateJobRequest
	(*CreateJobResponse)(nil),           // 3: job.v1.CreateJobResponse
	(*UpdateJobRequest)(nil),            // 4: job.v1.UpdateJobRequest
	(*UpdateJobResponse)(nil),           // 5: job.v1.UpdateJobResponse
	(*UpdateJobStatusRequest)(nil),      // 6: job.v1.UpdateJobStatusRequest
	(*UpdateJobStatusResponse)(nil),     // 7: job.v1.UpdateJobStatusResponse
	(*UpdateJobProgressRequest)(nil),    // 8: job.v1.UpdateJobProgressRequest
	(*UpdateJobProgressResponse)(nil),   // 9: job.v1.UpdateJobProgressResponse
	(*StartJobRequest)(nil),             // 10: job.v1.StartJobRequest
	(*StartJobResponse)(nil),            // 11: job.v1.StartJobResponse
	(*CompleteJobRequest)(nil),          // 12: job.v1.CompleteJobRequest
	(*CompleteJobResponse)(nil),         // 13: job.v1.CompleteJobResponse
	(*FailJobRequest)(nil),              // 14: job.v1.FailJobRequest
	(*FailJobResponse)(nil),             // 15: job.v1.FailJobResponse
	(*CancelJobRequest)(nil),            // 16: job.v1.CancelJobRequest
	(*CancelJobResponse)(nil),           // 17: job.v1.CancelJobResponse
	(*RetryJobRequest)(nil),             // 18: job.v1.RetryJobRequest
	(*RetryJobResponse)(nil),            // 19: job.v1.RetryJobResponse
	(*DeleteJobRequest)(nil),            // 20: job.v1.DeleteJobRequest
	(*DeleteJobResponse)(nil),           // 21: job.v1.DeleteJobResponse
	(*GetAllJobsRequest)(nil),           // 22: job.v1.GetAllJobsRequest
	(*GetAllJobsResponse)(nil),          // 23: job.v1.GetAllJobsResponse
	(*GetJobByIdRequest)(nil),           // 24: job.v1.GetJobByIdRequest
	(*GetJobByIdResponse)(nil),          // 25: job.v1.GetJobByIdResponse
	(*GetJobsByStatusRequest)(nil),      // 26: job.v1.GetJobsByStatusRequest
	(*GetJobsByStatusResponse)(nil),     // 27: job.v1.GetJobsByStatusResponse
	(*GetJobsByTypeRequest)(nil),        // 28: job.v1.GetJobsByTypeRequest
	(*GetJobsByTypeResponse)(nil),       // 29: job.v1.GetJobsByTypeResponse
	(*GetPendingJobsRequest)(nil),       // 30: job.v1.GetPendingJobsRequest
	(*GetPendingJobsResponse)(nil),      // 31: job.v1.GetPendingJobsResponse
	(*GetRunningJobsRequest)(nil),       // 32: job.v1.GetRunningJobsRequest
	(*GetRunningJobsResponse)(nil),      // 33: job.v1.GetRunningJobsResponse
	(*GetJobsByConfigIdRequest)(nil),    // 34: job.v1.GetJobsByConfigIdRequest
	(*GetJobsByConfigIdResponse)(nil),   // 35: job.v1.GetJobsByConfigIdResponse
	(*FilterJobsRequest)(nil),           // 36: job.v1.FilterJobsRequest
	(*FilterJobsResponse)(nil),          // 37: job.v1.FilterJobsResponse
	(*GetJobsNeedingRetryRequest)(nil),  // 38: job.v1.GetJobsNeedingRetryRequest
	(*GetJobsNeedingRetryResponse)(nil), // 39: job.v1.GetJobsNeedingRetryResponse
	(*GetExpiredJobsRequest)(nil),       // 40: job.v1.GetExpiredJobsRequest
	(*GetExpiredJobsResponse)(nil),      // 41: job.v1.GetExpiredJobsResponse
	(*timestamppb.Timestamp)(nil),       // 42: google.protobuf.Timestamp
}
var file_job_proto_depIdxs = []int32{
	42, // 0: job.v1.Job.scheduled_at:type_name -> google.protobuf.Timestamp
	42, // 1: job.v1.Job.started_at:type_name -> google.protobuf.Timestamp
	42, // 2: job.v1.Job.completed_at:type_name -> google.protobuf.Timestamp
	42, // 3: job.v1.Job.created_at:type_name -> google.protobuf.Timestamp
	42, // 4: job.v1.Job.updated_at:type_name -> google.protobuf.Timestamp
	42, // 5: job.v1.CreateJobRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	42, // 6: job.v1.UpdateJobRequest.scheduled_at:type_name -> google.protobuf.Timestamp
	1,  // 7: job.v1.UpdateJobProgressRequest.progress:type_name -> job.v1.JobProgress
	0,  // 8: job.v1.GetAllJobsResponse.jobs:type_name -> job.v1.Job
	0,  // 9: job.v1.GetJobByIdResponse.job:type_name -> job.v1.Job
	0,  // 10: job.v1.GetJobsByStatusResponse.jobs:type_name -> job.v1.Job
	0,  // 11: job.v1.GetJobsByTypeResponse.jobs:type_name -> job.v1.Job
	0,  // 12: job.v1.GetPendingJobsResponse.jobs:type_name -> job.v1.Job
	0,  // 13: job.v1.GetRunningJobsResponse.jobs:type_name -> job.v1.Job
	0,  // 14: job.v1.GetJobsByConfigIdResponse.jobs:type_name -> job.v1.Job
	42, // 15: job.v1.FilterJobsRequest.created_from:type_name -> google.protobuf.Timestamp
	42, // 16: job.v1.FilterJobsRequest.created_to:type_name -> google.protobuf.Timestamp
	0,  // 17: job.v1.FilterJobsResponse.jobs:type_name -> job.v1.Job
	0,  // 18: job.v1.GetJobsNeedingRetryResponse.jobs:type_name -> job.v1.Job
	0,  // 19: job.v1.GetExpiredJobsResponse.jobs:type_name -> job.v1.Job
	2,  // 20: job.v1.JobService.CreateJob:input_type -> job.v1.CreateJobRequest
	4,  // 21: job.v1.JobService.UpdateJob:input_type -> job.v1.UpdateJobRequest
	6,  // 22: job.v1.JobService.UpdateJobStatus:input_type -> job.v1.UpdateJobStatusRequest
	8,  // 23: job.v1.JobService.UpdateJobProgress:input_type -> job.v1.UpdateJobProgressRequest
	10, // 24: job.v1.JobService.StartJob:input_type -> job.v1.StartJobRequest
	12, // 25: job.v1.JobService.CompleteJob:input_type -> job.v1.CompleteJobRequest
	14, // 26: job.v1.JobService.FailJob:input_type -> job.v1.FailJobRequest
	16, // 27: job.v1.JobService.CancelJob:input_type -> job.v1.CancelJobRequest
	18, // 28: job.v1.JobService.RetryJob:input_type -> job.v1.RetryJobRequest
	20, // 29: job.v1.JobService.DeleteJob:input_type -> job.v1.DeleteJobRequest
	22, // 30: job.v1.JobService.GetAllJobs:input_type -> job.v1.GetAllJobsRequest
	24, // 31: job.v1.JobService.GetJobById:input_type -> job.v1.GetJobByIdRequest
	26, // 32: job.v1.JobService.GetJobsByStatus:input_type -> job.v1.GetJobsByStatusRequest
	28, // 33: job.v1.JobService.GetJobsByType:input_type -> job.v1.GetJobsByTypeRequest
	30, // 34: job.v1.JobService.GetPendingJobs:input_type -> job.v1.GetPendingJobsRequest
	32, // 35: job.v1.JobService.GetRunningJobs:input_type -> job.v1.GetRunningJobsRequest
	34, // 36: job.v1.JobService.GetJobsByConfigId:input_type -> job.v1.GetJobsByConfigIdRequest
	36, // 37: job.v1.JobService.FilterJobs:input_type -> job.v1.FilterJobsRequest
	38, // 38: job.v1.JobService.GetJobsNeedingRetry:input_type -> job.v1.GetJobsNeedingRetryRequest
	40, // 39: job.v1.JobService.GetExpiredJobs:input_type -> job.v1.GetExpiredJobsRequest
	3,  // 40: job.v1.JobService.CreateJob:output_type -> job.v1.CreateJobResponse
	5,  // 41: job.v1.JobService.UpdateJob:output_type -> job.v1.UpdateJobResponse
	7,  // 42: job.v1.JobService.UpdateJobStatus:output_type -> job.v1.UpdateJobStatusResponse
	9,  // 43: job.v1.JobService.UpdateJobProgress:output_type -> job.v1.UpdateJobProgressResponse
	11, // 44: job.v1.JobService.StartJob:output_type -> job.v1.StartJobResponse
	13, // 45: job.v1.JobService.CompleteJob:output_type -> job.v1.CompleteJobResponse
	15, // 46: job.v1.JobService.FailJob:output_type -> job.v1.FailJobResponse
	17, // 47: job.v1.JobService.CancelJob:output_type -> job.v1.CancelJobResponse
	19, // 48: job.v1.JobService.RetryJob:output_type -> job.v1.RetryJobResponse
	21, // 49: job.v1.JobService.DeleteJob:output_type -> job.v1.DeleteJobResponse
	23, // 50: job.v1.JobService.GetAllJobs:output_type -> job.v1.GetAllJobsResponse
	25, // 51: job.v1.JobService.GetJobById:output_type -> job.v1.GetJobByIdResponse
	27, // 52: job.v1.JobService.GetJobsByStatus:output_type -> job.v1.GetJobsByStatusResponse
	29, // 53: job.v1.JobService.GetJobsByType:output_type -> job.v1.GetJobsByTypeResponse
	31, // 54: job.v1.JobService.GetPendingJobs:output_type -> job.v1.GetPendingJobsResponse
	33, // 55: job.v1.JobService.GetRunningJobs:output_type -> job.v1.GetRunningJobsResponse
	35, // 56: job.v1.JobService.GetJobsByConfigId:output_type -> job.v1.GetJobsByConfigIdResponse
	37, // 57: job.v1.JobService.FilterJobs:output_type -> job.v1.FilterJobsResponse
	39, // 58: job.v1.JobService.GetJobsNeedingRetry:output_type -> job.v1.GetJobsNeedingRetryResponse
	41, // 59: job.v1.JobService.GetExpiredJobs:output_type -> job.v1.GetExpiredJobsResponse
	40, // [40:60] is the sub-list for method output_type
	20, // [20:40] is the sub-list for method input_type
	20, // [20:20] is the sub-list for extension type_name
	20, // [20:20] is the sub-list for extension extendee
	0,  // [0:20] is the sub-list for field type_name
}

func init() { file_job_proto_init() }
func file_job_proto_init() {
	if File_job_proto != nil {
		return
	}
	file_job_proto_msgTypes[0].OneofWrappers = []any{}
	file_job_proto_msgTypes[2].OneofWrappers = []any{}
	file_job_proto_msgTypes[3].OneofWrappers = []any{}
	file_job_proto_msgTypes[4].OneofWrappers = []any{}
	file_job_proto_msgTypes[36].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_job_proto_rawDesc), len(file_job_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   42,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_job_proto_goTypes,
		DependencyIndexes: file_job_proto_depIdxs,
		MessageInfos:      file_job_proto_msgTypes,
	}.Build()
	File_job_proto = out.File
	file_job_proto_goTypes = nil
	file_job_proto_depIdxs = nil
}
