package repository

import (
	"context"
	"errors"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/market/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/database"
)

// Error variables
var (
	ErrMarketNotFound    = errors.New("market not found")
	ErrEmptyMarketList   = errors.New("no markets to create")
	ErrUpdateMarketFail  = errors.New("failed to update market")
	ErrCreateMarketFail  = errors.New("failed to save market")
	ErrCreateMarketsFail = errors.New("failed to save markets")
)

type marketRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewMarketRepository creates a new market repository
func NewMarketRepository(db database.DBTX) repository.RepositoryMarket {
	return &marketRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create inserts a single market into the database
func (r *marketRepository) Create(ctx context.Context, market *entity.Market) error {
	params, err := ToCreateParams(market)
	if err != nil {
		return err
	}

	if _, err := r.queries.Create(ctx, *params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateMarketFail, err)
	}

	return nil
}

// CreateMany inserts multiple markets into the database
func (r *marketRepository) CreateMany(ctx context.Context, markets []*entity.Market) error {
	if len(markets) == 0 {
		return ErrEmptyMarketList
	}

	params, err := ToSaveBatchParams(markets)
	if err != nil {
		return err
	}

	if _, err := r.queries.CreateMany(ctx, params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateMarketsFail, err)
	}

	return nil
}

// Update updates a single market by ID using a provided update function
func (r *marketRepository) Update(ctx context.Context, id int, updateFn func(m *entity.Market) (*entity.Market, error)) error {
	rawMarket, err := r.queries.FindByID(ctx, id)
	if err != nil {
		return err
	}

	market, err := FromSQLCModel(rawMarket)
	if err != nil {
		return err
	}

	updatedMarket, err := updateFn(market)
	if err != nil {
		return err
	}

	updateParams, err := ToUpdateMarketParams(updatedMarket)
	if err != nil {
		return err
	}

	if _, err := r.queries.UpdateMarket(ctx, *updateParams); err != nil {
		return fmt.Errorf("%w: %v", ErrUpdateMarketFail, err)
	}

	return nil
}

// UpdateMany updates multiple markets by IDs using a provided update function
func (r *marketRepository) UpdateMany(ctx context.Context, ids []int, updateFn func(m []*entity.Market) ([]*entity.Market, error)) error {
	panic("UpdateMany not implemented")
}

// Delete deletes a single market by ID
func (r *marketRepository) Delete(ctx context.Context, id int) error {
	panic("Delete not implemented")
}

// DeleteMany deletes multiple markets by IDs
func (r *marketRepository) DeleteMany(ctx context.Context, ids []int) error {
	panic("DeleteMany not implemented")
}
