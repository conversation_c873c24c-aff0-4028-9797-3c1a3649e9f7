package eventhandler

import (
	"context"
	"time"

	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/pkg/message/event"
)

// ExampleJobStartEventHandling demonstrates how the job start event handler works
func ExampleJobStartEventHandling() {
	// This example shows how a job start event triggers crawl processing

	// 1. Job is started (this would typically happen via gRPC API or scheduler)
	// The job start command would publish a JobStartedEvent

	// 2. Create a job started event (this would be published by the job start command handler)
	jobStartedEvent := event.NewJobStartedEvent("job-123", event.JobStartedData{
		JobID:     123,
		JobType:   string(entity.JobTypeCrawl),
		Name:      "Daily Price Crawl - Buff163",
		StartedAt: time.Now(),
		StartedBy: "scheduler",
	})

	// 3. The event would be published to Kafka topic "job.events"
	// broker.Publish(ctx, jobStartedEvent)

	// 4. The JobStartEventHandler.handleJobEvents would receive this event
	// and route it to JobStartEventHandler.Handle()

	// 5. The handler would:
	//    a. Check if job type is "crawl" ✓
	//    b. Query job details to get crawl_config_id
	//    c. Query crawl config to check if type is "normal"
	//    d. If normal, call CrawlService.CrawlPriceNormal()

	// Example flow:
	// Job(id=123, type="crawl", crawl_config_id=456)
	// -> CrawlConfig(id=456, type="normal", market_id=1)
	// -> CrawlService.CrawlPriceNormal(config) 
	// -> Crawls prices from market and publishes PricesCrawledEvent

	_ = jobStartedEvent // Use the event to avoid unused variable warning
}

// ExampleJobStartEventFlow shows the complete flow from job creation to crawl execution
func ExampleJobStartEventFlow() {
	// Step 1: Create a crawl job with normal config
	// POST /api/jobs
	// {
	//   "job_type": "crawl",
	//   "name": "Daily Price Crawl",
	//   "crawl_config_id": 456,
	//   "scheduled_at": "2024-01-15T10:00:00Z"
	// }

	// Step 2: Job is scheduled and then started
	// POST /api/jobs/123/start
	// This publishes JobStartedEvent

	// Step 3: JobStartEventHandler processes the event
	// - Checks job type = "crawl" ✓
	// - Gets job details: crawl_config_id = 456
	// - Gets config details: type = "normal", market_id = 1
	// - Calls CrawlService.CrawlPriceNormal()

	// Step 4: CrawlService executes the crawl
	// - Gets market details (Buff163)
	// - Gets available proxies and auth credentials
	// - Configures crawler with rate limits and timeouts
	// - Crawls prices page by page
	// - Publishes PricesCrawledEvent for each page

	// Step 5: Other handlers can process PricesCrawledEvent
	// - Store prices in database
	// - Update job progress
	// - Send notifications
	// - Trigger analysis jobs
}

// ExampleEventHandlerRegistration shows how to register the event handler
func ExampleEventHandlerRegistration(ctx context.Context) {
	// This would typically be done in the application startup

	// 1. Create dependencies
	// crawlService := service.NewCrawlService(...)
	// crawlConfigQueries := querycrawlconfig.NewCrawlConfigQueries(...)
	// jobQueries := queryjob.NewJobQueries(...)
	// logger := logger.NewZapLogger(...)
	// broker := kafka.NewKafkaBroker(...)

	// 2. Create event handler manager
	// eventManager := eventhandler.NewEventHandlerManager(
	//     broker,
	//     crawlService,
	//     crawlConfigQueries,
	//     jobQueries,
	//     logger,
	// )

	// 3. Register handlers with broker
	// err := eventManager.RegisterHandlers(ctx)
	// if err != nil {
	//     logger.Fatal("Failed to register event handlers", "error", err)
	// }

	// 4. The handler will now listen for job events on "job.events" topic
	// and process JobStartedEvent automatically
}

// ExampleCrawlConfigTypes shows different config types and their handling
func ExampleCrawlConfigTypes() {
	// Normal Config (handled by this event handler)
	// {
	//   "id": 456,
	//   "type": "normal",
	//   "market_id": 1,
	//   "requests_per_minute": 60,
	//   "timeout_seconds": 30
	// }
	// -> Triggers CrawlService.CrawlPriceNormal()

	// Doppler Config (not handled by this event handler)
	// {
	//   "id": 457,
	//   "type": "doppler",
	//   "market_id": 1,
	//   "item_id": 12345
	// }
	// -> Would need a separate event handler for doppler crawls

	// Float Config (not handled by this event handler)
	// {
	//   "id": 458,
	//   "type": "float",
	//   "market_id": 1,
	//   "item_id": 12345
	// }
	// -> Would need a separate event handler for float crawls
}

// ExampleErrorHandling shows how errors are handled in the event handler
func ExampleErrorHandling() {
	// Scenario 1: Job not found
	// -> Returns error, event processing fails
	// -> Message may be retried depending on broker configuration

	// Scenario 2: Config not found
	// -> Returns error, event processing fails
	// -> Should update job status to failed

	// Scenario 3: Config type is not "normal"
	// -> Logs debug message and returns nil (success)
	// -> Event is considered processed successfully

	// Scenario 4: CrawlService.CrawlPriceNormal() fails
	// -> Returns error with context
	// -> Should update job status to failed
	// -> May trigger retry logic

	// Best practices:
	// - Log all important steps for debugging
	// - Return errors for genuine failures that should be retried
	// - Return nil for expected scenarios (wrong job type, config type)
	// - Include context in error messages (job ID, config ID, etc.)
}
