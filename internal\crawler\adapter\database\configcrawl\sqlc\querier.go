// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	ActivateConfig(ctx context.Context, id int) (*CrawlConfig, error)
	CountActiveCrawlConfigsQuery(ctx context.Context) (int64, error)
	CountCrawlConfigsByMarketQuery(ctx context.Context, marketID int) (int64, error)
	CountCrawlConfigsQuery(ctx context.Context) (int64, error)
	Create(ctx context.Context, arg CreateParams) (int, error)
	CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error)
	DeactivateConfig(ctx context.Context, id int) (*CrawlConfig, error)
	DeleteCrawlConfig(ctx context.Context, id int) error
	FilterCrawlConfigsQuery(ctx context.Context, arg FilterCrawlConfigsQueryParams) ([]*CrawlConfig, error)
	FindByID(ctx context.Context, id int) (*CrawlConfig, error)
	GetActiveCrawlConfigsByMarketAndTypeQuery(ctx context.Context, arg GetActiveCrawlConfigsByMarketAndTypeQueryParams) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsByMarketQuery(ctx context.Context, marketID int) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsByTypeQuery(ctx context.Context, typeConfig string) ([]*CrawlConfig, error)
	GetActiveCrawlConfigsQuery(ctx context.Context) ([]*CrawlConfig, error)
	// Query methods for CrawlConfigQueryRepository
	GetAllCrawlConfigsWithPagination(ctx context.Context, arg GetAllCrawlConfigsWithPaginationParams) ([]*CrawlConfig, error)
	GetCrawlConfigByIdQuery(ctx context.Context, id int) (*CrawlConfig, error)
	GetCrawlConfigsByMarketIdQuery(ctx context.Context, marketID int) ([]*CrawlConfig, error)
	GetCrawlConfigsByTypeQuery(ctx context.Context, typeConfig string) ([]*CrawlConfig, error)
	GetLeastUsedActiveConfigByMarketQuery(ctx context.Context, marketID int) (*CrawlConfig, error)
	GetLeastUsedActiveConfigByTypeQuery(ctx context.Context, typeConfig string) (*CrawlConfig, error)
	GetLeastUsedActiveConfigQuery(ctx context.Context) (*CrawlConfig, error)
	UpdateCrawlConfig(ctx context.Context, arg UpdateCrawlConfigParams) (*CrawlConfig, error)
	UpdateLastUsedAt(ctx context.Context, id int) (*CrawlConfig, error)
}

var _ Querier = (*Queries)(nil)
