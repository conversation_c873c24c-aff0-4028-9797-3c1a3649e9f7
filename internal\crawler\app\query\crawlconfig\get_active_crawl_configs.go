package querycrawlconfig

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetActiveCrawlConfigsQuery struct {
	// No parameters needed
}

type GetActiveCrawlConfigsQueryHandler decorator.QueryHandler[GetActiveCrawlConfigsQuery, []*CrawlConfig]

type getActiveCrawlConfigsQueryHandler struct {
	repo CrawlConfigReadModel
}

func NewGetActiveCrawlConfigsQueryHandler(
	repo CrawlConfigReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetActiveCrawlConfigsQueryHandler {

	return decorator.ApplyQueryDecorators[GetActiveCrawlConfigsQuery, []*CrawlConfig](
		getActiveCrawlConfigsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getActiveCrawlConfigsQueryHandler) Handle(ctx context.Context, query GetActiveCrawlConfigsQuery) ([]*CrawlConfig, error) {
	return h.repo.GetActiveCrawlConfigs(ctx)
}
