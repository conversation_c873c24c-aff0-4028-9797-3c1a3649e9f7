package entity

import (
	"errors"
	"fmt"
	"strings"
	"time"
)

type CurrencyCode string

const (
	CurrencyUSD CurrencyCode = "USD"
	CurrencyEUR CurrencyCode = "EUR"
	CurrencyRUB CurrencyCode = "RUB"
	CurrencyGBP CurrencyCode = "GBP"
	CurrencyJPY CurrencyCode = "JPY"
	CurrencyCNY CurrencyCode = "CNY"
)

func (c CurrencyCode) IsValid() bool {
	switch c {
	case CurrencyUSD, CurrencyEUR, CurrencyRUB, CurrencyGBP, CurrencyJPY, CurrencyCNY:
		return true
	default:
		return false
	}
}

// MarketType để phân loại các loại market
type MarketType string

const (
	MarketTypeSteam MarketType = "steam"
	MarketTypeP2P   MarketType = "p2p"
	MarketTypeBot   MarketType = "bot"
	MarketTypeBet   MarketType = "bet"
)

func (mt MarketType) IsValid() bool {
	switch mt {
	case MarketTypeSteam, MarketTypeP2P, MarketTypeBot, MarketTypeBet:
		return true
	default:
		return false
	}
}

// MarketStatus represents operational status of a market.
type MarketStatus string

const (
	MarketStatusActive      MarketStatus = "active"
	MarketStatusInactive    MarketStatus = "inactive"
	MarketStatusMaintenance MarketStatus = "maintenance"
)

// MarketParams is the input used to create a Market.
type MarketParams struct {
	Name             string
	DisplayName      string
	Type             string
	BaseURL          string
	Currency         string
	BuyerFeePercent  float64
	SellerFeePercent float64
	CountryCode      string
	Language         string
	Description      string
}

// Market represents the Market aggregate root.
type Market struct {
	id               int
	name             string
	displayName      string
	marketType       MarketType
	status           MarketStatus
	baseURL          string
	currency         CurrencyCode
	buyerFeePercent  float64
	sellerFeePercent float64
	countryCode      string
	language         string
	description      string
	isActive         bool
	lastCrawlAt      *time.Time
}

// Error declarations for Market validation
var (
	ErrCurrencyCodeInvalid    = errors.New("invalid currency code")
	ErrMarketNameEmpty        = errors.New("market name cannot be empty")
	ErrMarketBaseURLEmpty     = errors.New("base URL cannot be empty")
	ErrMarketCurrencyEmpty    = errors.New("currency cannot be empty")
	ErrMarketNameCleanedEmpty = errors.New("market name cannot be empty after cleaning")
	ErrMarketTypeInvalid      = errors.New("invalid market type")
	ErrMarketIsActive         = errors.New("market is active")
	ErrMarketIsInactive       = errors.New("market is inactive")
)

// NewMarket creates a new Market instance with validation.
func NewMarket(params MarketParams) (*Market, error) {
	if params.Name == "" {
		return nil, ErrMarketNameEmpty
	}
	if params.BaseURL == "" {
		return nil, ErrMarketBaseURLEmpty
	}
	if params.Currency == "" {
		return nil, ErrMarketCurrencyEmpty
	}

	// Clean and validate name
	cleanName := strings.TrimSpace(strings.ToLower(params.Name))
	if cleanName == "" {
		return nil, ErrMarketNameCleanedEmpty
	}

	displayName := params.DisplayName
	if displayName == "" {
		displayName = params.Name
	}
	mt := MarketType(params.Type)
	if !mt.IsValid() {
		return nil, ErrMarketTypeInvalid
	}
	currency := CurrencyCode(params.Currency)
	if !currency.IsValid() {
		return nil, ErrCurrencyCodeInvalid
	}
	return &Market{
		name:             cleanName,
		displayName:      displayName,
		marketType:       mt,
		status:           MarketStatusActive,
		baseURL:          params.BaseURL,
		currency:         currency,
		buyerFeePercent:  params.BuyerFeePercent,
		sellerFeePercent: params.SellerFeePercent,
		countryCode:      params.CountryCode,
		language:         params.Language,
		description:      params.Description,
		isActive:         true,
	}, nil
}

type MarketRebuildParams struct {
	ID               int
	Name             string
	DisplayName      string
	MarketType       string
	Status           string
	BaseURL          string
	Currency         string
	BuyerFeePercent  float64
	SellerFeePercent float64
	CountryCode      string
	Language         string
	Description      string
	IsActive         bool
	LastCrawlAt      *time.Time
}

func RebuildMarket(params MarketRebuildParams) *Market {
	return &Market{
		id:               params.ID,
		name:             params.Name,
		displayName:      params.DisplayName,
		marketType:       MarketType(params.MarketType),
		status:           MarketStatus(params.Status),
		baseURL:          params.BaseURL,
		currency:         CurrencyCode(params.Currency),
		buyerFeePercent:  params.BuyerFeePercent,
		sellerFeePercent: params.SellerFeePercent,
		countryCode:      params.CountryCode,
		language:         params.Language,
		description:      params.Description,
		isActive:         params.IsActive,
		lastCrawlAt:      params.LastCrawlAt,
	}
}

// Getters
func (m *Market) ID() int                   { return m.id }
func (m *Market) Name() string              { return m.name }
func (m *Market) DisplayName() string       { return m.displayName }
func (m *Market) Type() MarketType          { return m.marketType }
func (m *Market) Status() MarketStatus      { return m.status }
func (m *Market) BaseURL() string           { return m.baseURL }
func (m *Market) Currency() CurrencyCode    { return m.currency }
func (m *Market) BuyerFeePercent() float64  { return m.buyerFeePercent }
func (m *Market) SellerFeePercent() float64 { return m.sellerFeePercent }
func (m *Market) CountryCode() string       { return m.countryCode }
func (m *Market) Language() string          { return m.language }
func (m *Market) Description() string       { return m.description }
func (m *Market) IsActive() bool            { return m.isActive }
func (m *Market) LastCrawlAt() *time.Time   { return m.lastCrawlAt }

// Setters (for repository layer)

// Domain Methods - Business Logic

// Status management
func (m *Market) Activate() error {
	if m.status == MarketStatusActive {
		return ErrMarketIsActive
	}
	m.status = MarketStatusActive
	m.isActive = true
	return nil
}

func (m *Market) Deactivate() error {
	if m.status == MarketStatusInactive {
		return ErrMarketIsInactive
	}
	m.status = MarketStatusInactive
	m.isActive = false
	return nil

}

func (m *Market) SetMaintenance() error {
	if m.status == MarketStatusMaintenance {
		return ErrMarketIsInactive
	}
	m.status = MarketStatusMaintenance
	m.isActive = false
	return nil

}

func (m *Market) UpdateLastCrawl() {
	now := time.Now()
	m.lastCrawlAt = &now

}

func (m *Market) GetCrawlInfo() string {
	if m.lastCrawlAt == nil {
		return "Never crawled"
	}
	return fmt.Sprintf("Last crawled: %s", m.lastCrawlAt.Format("2006-01-02 15:04:05"))
}

// UpdateInfo updates market information with validation
func (m *Market) UpdateInfo(name, displayName, marketType, baseURL, currency string, buyerFeePercent, sellerFeePercent float64, countryCode, language, description string) error {
	if name == "" {
		return ErrMarketNameEmpty
	}
	if baseURL == "" {
		return ErrMarketBaseURLEmpty
	}
	if currency == "" {
		return ErrMarketCurrencyEmpty
	}

	// Clean and validate name
	cleanName := strings.TrimSpace(strings.ToLower(name))
	if cleanName == "" {
		return ErrMarketNameCleanedEmpty
	}

	// Validate market type
	mt := MarketType(marketType)
	if !mt.IsValid() {
		return ErrMarketTypeInvalid
	}

	// Validate currency
	curr := CurrencyCode(currency)
	if !curr.IsValid() {
		return ErrCurrencyCodeInvalid
	}

	// Update fields
	m.name = cleanName
	if displayName != "" {
		m.displayName = displayName
	} else {
		m.displayName = name
	}
	m.marketType = mt
	m.baseURL = baseURL
	m.currency = curr
	m.buyerFeePercent = buyerFeePercent
	m.sellerFeePercent = sellerFeePercent
	m.countryCode = countryCode
	m.language = language
	m.description = description

	return nil
}
