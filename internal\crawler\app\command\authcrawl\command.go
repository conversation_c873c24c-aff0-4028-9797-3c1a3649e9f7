package commandauthcrawl

import (
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type AuthCrawlCommands struct {
	CreateAuth      CreateAuthHandler
	CreateAuthBatch CreateAuthBatchHandler
	UpdateAuth      UpdateAuthHandler
	DeleteAuth      DeleteAuthHandler
	ActivateAuth    ActivateAuthHandler
	DeactivateAuth  DeactivateAuthHandler
	MarkAuthUsed    MarkAuthUsedHandler
	MarkAuthsUsed   MarkAuthsUsedHandler
	ReleaseAuth     ReleaseAuthHandler
	ReleaseManyAuth ReleaseManyAuthHandler
}

func NewAuthCrawlCommands(repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker) *AuthCrawlCommands {
	return &AuthCrawlCommands{
		CreateAuth:      NewCreateAuthHandler(repo, logger, metricsClient, broker),
		CreateAuthBatch: NewCreateAuthBatchHandler(repo, logger, metricsClient, broker),
		UpdateAuth:      NewUpdateAuth<PERSON><PERSON><PERSON>(repo, logger, metricsClient, broker),
		DeleteAuth:      NewDeleteAuthHandler(repo, logger, metricsClient, broker),
		ActivateAuth:    NewActivateAuthHandler(repo, logger, metricsClient, broker),
		DeactivateAuth:  NewDeactivateAuthHandler(repo, logger, metricsClient, broker),
		MarkAuthUsed:    NewMarkAuthUsedHandler(repo, logger, metricsClient, broker),
		MarkAuthsUsed:   NewMarkAuthsUsedHandler(repo, logger, metricsClient, broker),
		ReleaseAuth:     NewReleaseAuthHandler(repo, logger, metricsClient, broker),
		ReleaseManyAuth: NewReleaseManyAuthHandler(repo, logger, metricsClient, broker),
	}
}
