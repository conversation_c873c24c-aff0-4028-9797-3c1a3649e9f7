package repository

import (
	sqlc2 "go_core_market/internal/crawler/adapter/database/proxy/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
)

func ToPgTimestamp(t time.Time) pgtype.Timestamp {
	return pgtype.Timestamp{
		Time:  t,
		Valid: true,
	}
}

func ToPgTimestamptz(t time.Time) pgtype.Timestamptz {
	return pgtype.Timestamptz{
		Time:  t,
		Valid: true,
	}
}

// FromSQLCModel converts sqlc.Proxy to domain.Proxy
func FromSQLCModel(sqlcProxy sqlc2.Proxy) *entity.Proxy {
	var lastUsedAt *time.Time
	if sqlcProxy.LastUsedAt.Valid {
		lastUsedAt = &sqlcProxy.LastUsedAt.Time
	}

	return entity.RebuildProxy(entity.ProxyRebuildParams{
		ID:         int(sqlcProxy.ID),
		Host:       sqlcProxy.Host,
		Port:       int(sqlcProxy.Port),
		UserName:   sqlcProxy.UserName,
		Password:   sqlcProxy.Password,
		IsActive:   sqlcProxy.IsActive,
		IsUse:      sqlcProxy.IsUse,
		LastUsedAt: lastUsedAt,
	})
}

// FromSQLCModels converts slice of sqlc.Proxy to slice of domain.Proxy
func FromSQLCModels(sqlcProxies []*sqlc2.Proxy) []*entity.Proxy {
	proxies := make([]*entity.Proxy, len(sqlcProxies))
	for i, sqlcProxy := range sqlcProxies {
		proxies[i] = FromSQLCModel(*sqlcProxy)
	}
	return proxies
}

// ToCreateProxyParams converts domain.Proxy to sqlc.CreateProxyParams
func ToCreateProxyParams(proxy *entity.Proxy) sqlc2.CreateProxyParams {

	return sqlc2.CreateProxyParams{
		Host:       proxy.Host(),
		Port:       int32(proxy.Port()),
		UserName:   proxy.UserName(),
		Password:   proxy.Password(),
		IsActive:   proxy.IsActive(),
		IsUse:      proxy.IsUse(),
		LastUsedAt: ToPgTimestamptz(*proxy.LastUsedAt()),
	}
}

func ToCreateProxiesParams(proxies []*entity.Proxy) []sqlc2.CreateProxiesParams {
	proxiesParams := make([]sqlc2.CreateProxiesParams, len(proxies))
	for i, p := range proxies {
		proxiesParams[i] = sqlc2.CreateProxiesParams{
			Host:       p.Host(),
			Port:       int32(p.Port()),
			UserName:   p.UserName(),
			Password:   p.Password(),
			IsActive:   p.IsActive(),
			IsUse:      p.IsUse(),
			LastUsedAt: ToPgTimestamptz(*p.LastUsedAt()),
		}
	}
	return proxiesParams
}

// ToUpdateProxyParams converts domain.Proxy to sqlc.UpdateProxyParams
func ToUpdateProxyParams(proxy *entity.Proxy) sqlc2.UpdateProxyParams {
	var lastUsedAt pgtype.Timestamptz
	if proxy.LastUsedAt() != nil {
		lastUsedAt = pgtype.Timestamptz{
			Time:  *proxy.LastUsedAt(),
			Valid: true,
		}
	}

	return sqlc2.UpdateProxyParams{
		ID:         int32(proxy.ID()),
		Host:       proxy.Host(),
		Port:       int32(proxy.Port()),
		UserName:   proxy.UserName(),
		Password:   proxy.Password(),
		IsActive:   proxy.IsActive(),
		IsUse:      proxy.IsUse(),
		LastUsedAt: lastUsedAt,
	}
}
