// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: api/protobuf/market.proto

package market

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Market message
type Market struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName      string                 `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type             string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	Status           string                 `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`
	BaseUrl          string                 `protobuf:"bytes,6,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	Currency         string                 `protobuf:"bytes,7,opt,name=currency,proto3" json:"currency,omitempty"`
	BuyerFeePercent  float64                `protobuf:"fixed64,8,opt,name=buyer_fee_percent,json=buyerFeePercent,proto3" json:"buyer_fee_percent,omitempty"`
	SellerFeePercent float64                `protobuf:"fixed64,9,opt,name=seller_fee_percent,json=sellerFeePercent,proto3" json:"seller_fee_percent,omitempty"`
	CountryCode      string                 `protobuf:"bytes,10,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Language         string                 `protobuf:"bytes,11,opt,name=language,proto3" json:"language,omitempty"`
	Description      string                 `protobuf:"bytes,12,opt,name=description,proto3" json:"description,omitempty"`
	IsActive         bool                   `protobuf:"varint,13,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	LastCrawlAt      *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=last_crawl_at,json=lastCrawlAt,proto3" json:"last_crawl_at,omitempty"`
	CreatedAt        *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt        *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Market) Reset() {
	*x = Market{}
	mi := &file_api_protobuf_market_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Market) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Market) ProtoMessage() {}

func (x *Market) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Market.ProtoReflect.Descriptor instead.
func (*Market) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{0}
}

func (x *Market) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Market) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Market) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Market) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Market) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Market) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *Market) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *Market) GetBuyerFeePercent() float64 {
	if x != nil {
		return x.BuyerFeePercent
	}
	return 0
}

func (x *Market) GetSellerFeePercent() float64 {
	if x != nil {
		return x.SellerFeePercent
	}
	return 0
}

func (x *Market) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *Market) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *Market) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Market) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Market) GetLastCrawlAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastCrawlAt
	}
	return nil
}

func (x *Market) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Market) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response messages for Commands
type CreateMarketRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Name             string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName      string                 `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type             string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	BaseUrl          string                 `protobuf:"bytes,4,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	Currency         string                 `protobuf:"bytes,5,opt,name=currency,proto3" json:"currency,omitempty"`
	BuyerFeePercent  float64                `protobuf:"fixed64,6,opt,name=buyer_fee_percent,json=buyerFeePercent,proto3" json:"buyer_fee_percent,omitempty"`
	SellerFeePercent float64                `protobuf:"fixed64,7,opt,name=seller_fee_percent,json=sellerFeePercent,proto3" json:"seller_fee_percent,omitempty"`
	CountryCode      string                 `protobuf:"bytes,8,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Language         string                 `protobuf:"bytes,9,opt,name=language,proto3" json:"language,omitempty"`
	Description      string                 `protobuf:"bytes,10,opt,name=description,proto3" json:"description,omitempty"`
	CreatedBy        string                 `protobuf:"bytes,11,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateMarketRequest) Reset() {
	*x = CreateMarketRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMarketRequest) ProtoMessage() {}

func (x *CreateMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMarketRequest.ProtoReflect.Descriptor instead.
func (*CreateMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{1}
}

func (x *CreateMarketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateMarketRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CreateMarketRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateMarketRequest) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *CreateMarketRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *CreateMarketRequest) GetBuyerFeePercent() float64 {
	if x != nil {
		return x.BuyerFeePercent
	}
	return 0
}

func (x *CreateMarketRequest) GetSellerFeePercent() float64 {
	if x != nil {
		return x.SellerFeePercent
	}
	return 0
}

func (x *CreateMarketRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *CreateMarketRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *CreateMarketRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateMarketRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMarketResponse) Reset() {
	*x = CreateMarketResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMarketResponse) ProtoMessage() {}

func (x *CreateMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMarketResponse.ProtoReflect.Descriptor instead.
func (*CreateMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{2}
}

func (x *CreateMarketResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateMarketResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateMarketRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	DisplayName      string                 `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	Type             string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	BaseUrl          string                 `protobuf:"bytes,5,opt,name=base_url,json=baseUrl,proto3" json:"base_url,omitempty"`
	Currency         string                 `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency,omitempty"`
	BuyerFeePercent  float64                `protobuf:"fixed64,7,opt,name=buyer_fee_percent,json=buyerFeePercent,proto3" json:"buyer_fee_percent,omitempty"`
	SellerFeePercent float64                `protobuf:"fixed64,8,opt,name=seller_fee_percent,json=sellerFeePercent,proto3" json:"seller_fee_percent,omitempty"`
	CountryCode      string                 `protobuf:"bytes,9,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	Language         string                 `protobuf:"bytes,10,opt,name=language,proto3" json:"language,omitempty"`
	Description      string                 `protobuf:"bytes,11,opt,name=description,proto3" json:"description,omitempty"`
	UpdatedBy        string                 `protobuf:"bytes,12,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpdateMarketRequest) Reset() {
	*x = UpdateMarketRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMarketRequest) ProtoMessage() {}

func (x *UpdateMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMarketRequest.ProtoReflect.Descriptor instead.
func (*UpdateMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateMarketRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateMarketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateMarketRequest) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *UpdateMarketRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UpdateMarketRequest) GetBaseUrl() string {
	if x != nil {
		return x.BaseUrl
	}
	return ""
}

func (x *UpdateMarketRequest) GetCurrency() string {
	if x != nil {
		return x.Currency
	}
	return ""
}

func (x *UpdateMarketRequest) GetBuyerFeePercent() float64 {
	if x != nil {
		return x.BuyerFeePercent
	}
	return 0
}

func (x *UpdateMarketRequest) GetSellerFeePercent() float64 {
	if x != nil {
		return x.SellerFeePercent
	}
	return 0
}

func (x *UpdateMarketRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *UpdateMarketRequest) GetLanguage() string {
	if x != nil {
		return x.Language
	}
	return ""
}

func (x *UpdateMarketRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateMarketRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateMarketResponse) Reset() {
	*x = UpdateMarketResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateMarketResponse) ProtoMessage() {}

func (x *UpdateMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateMarketResponse.ProtoReflect.Descriptor instead.
func (*UpdateMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateMarketResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateMarketResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     string                 `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMarketRequest) Reset() {
	*x = DeleteMarketRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMarketRequest) ProtoMessage() {}

func (x *DeleteMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMarketRequest.ProtoReflect.Descriptor instead.
func (*DeleteMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteMarketRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteMarketRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

type DeleteMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMarketResponse) Reset() {
	*x = DeleteMarketResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMarketResponse) ProtoMessage() {}

func (x *DeleteMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMarketResponse.ProtoReflect.Descriptor instead.
func (*DeleteMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteMarketResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteMarketResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ActiveMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	ActivatedBy   string                 `protobuf:"bytes,2,opt,name=activated_by,json=activatedBy,proto3" json:"activated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActiveMarketRequest) Reset() {
	*x = ActiveMarketRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveMarketRequest) ProtoMessage() {}

func (x *ActiveMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveMarketRequest.ProtoReflect.Descriptor instead.
func (*ActiveMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{7}
}

func (x *ActiveMarketRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *ActiveMarketRequest) GetActivatedBy() string {
	if x != nil {
		return x.ActivatedBy
	}
	return ""
}

type ActiveMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActiveMarketResponse) Reset() {
	*x = ActiveMarketResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActiveMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActiveMarketResponse) ProtoMessage() {}

func (x *ActiveMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActiveMarketResponse.ProtoReflect.Descriptor instead.
func (*ActiveMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{8}
}

func (x *ActiveMarketResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ActiveMarketResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeactiveMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	DeactivatedBy string                 `protobuf:"bytes,2,opt,name=deactivated_by,json=deactivatedBy,proto3" json:"deactivated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactiveMarketRequest) Reset() {
	*x = DeactiveMarketRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactiveMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactiveMarketRequest) ProtoMessage() {}

func (x *DeactiveMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactiveMarketRequest.ProtoReflect.Descriptor instead.
func (*DeactiveMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{9}
}

func (x *DeactiveMarketRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *DeactiveMarketRequest) GetDeactivatedBy() string {
	if x != nil {
		return x.DeactivatedBy
	}
	return ""
}

type DeactiveMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactiveMarketResponse) Reset() {
	*x = DeactiveMarketResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactiveMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactiveMarketResponse) ProtoMessage() {}

func (x *DeactiveMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactiveMarketResponse.ProtoReflect.Descriptor instead.
func (*DeactiveMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{10}
}

func (x *DeactiveMarketResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeactiveMarketResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type SetMarketMaintenanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	MaintainedBy  string                 `protobuf:"bytes,2,opt,name=maintained_by,json=maintainedBy,proto3" json:"maintained_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMarketMaintenanceRequest) Reset() {
	*x = SetMarketMaintenanceRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMarketMaintenanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMarketMaintenanceRequest) ProtoMessage() {}

func (x *SetMarketMaintenanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMarketMaintenanceRequest.ProtoReflect.Descriptor instead.
func (*SetMarketMaintenanceRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{11}
}

func (x *SetMarketMaintenanceRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *SetMarketMaintenanceRequest) GetMaintainedBy() string {
	if x != nil {
		return x.MaintainedBy
	}
	return ""
}

type SetMarketMaintenanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMarketMaintenanceResponse) Reset() {
	*x = SetMarketMaintenanceResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMarketMaintenanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMarketMaintenanceResponse) ProtoMessage() {}

func (x *SetMarketMaintenanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMarketMaintenanceResponse.ProtoReflect.Descriptor instead.
func (*SetMarketMaintenanceResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{12}
}

func (x *SetMarketMaintenanceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *SetMarketMaintenanceResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Request/Response messages for Queries
type GetAllMarketsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OrderBy       string                 `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllMarketsRequest) Reset() {
	*x = GetAllMarketsRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllMarketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllMarketsRequest) ProtoMessage() {}

func (x *GetAllMarketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllMarketsRequest.ProtoReflect.Descriptor instead.
func (*GetAllMarketsRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{13}
}

func (x *GetAllMarketsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllMarketsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllMarketsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type GetAllMarketsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Markets       []*Market              `protobuf:"bytes,1,rep,name=markets,proto3" json:"markets,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalPages    int32                  `protobuf:"varint,5,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllMarketsResponse) Reset() {
	*x = GetAllMarketsResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllMarketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllMarketsResponse) ProtoMessage() {}

func (x *GetAllMarketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllMarketsResponse.ProtoReflect.Descriptor instead.
func (*GetAllMarketsResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{14}
}

func (x *GetAllMarketsResponse) GetMarkets() []*Market {
	if x != nil {
		return x.Markets
	}
	return nil
}

func (x *GetAllMarketsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAllMarketsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllMarketsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllMarketsResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

type GetMarketByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMarketByIdRequest) Reset() {
	*x = GetMarketByIdRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMarketByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMarketByIdRequest) ProtoMessage() {}

func (x *GetMarketByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMarketByIdRequest.ProtoReflect.Descriptor instead.
func (*GetMarketByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{15}
}

func (x *GetMarketByIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetMarketByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Market        *Market                `protobuf:"bytes,1,opt,name=market,proto3" json:"market,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMarketByIdResponse) Reset() {
	*x = GetMarketByIdResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMarketByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMarketByIdResponse) ProtoMessage() {}

func (x *GetMarketByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMarketByIdResponse.ProtoReflect.Descriptor instead.
func (*GetMarketByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{16}
}

func (x *GetMarketByIdResponse) GetMarket() *Market {
	if x != nil {
		return x.Market
	}
	return nil
}

type GetActiveMarketsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveMarketsRequest) Reset() {
	*x = GetActiveMarketsRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveMarketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveMarketsRequest) ProtoMessage() {}

func (x *GetActiveMarketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveMarketsRequest.ProtoReflect.Descriptor instead.
func (*GetActiveMarketsRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{17}
}

type GetActiveMarketsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Markets       []*Market              `protobuf:"bytes,1,rep,name=markets,proto3" json:"markets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveMarketsResponse) Reset() {
	*x = GetActiveMarketsResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveMarketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveMarketsResponse) ProtoMessage() {}

func (x *GetActiveMarketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveMarketsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveMarketsResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{18}
}

func (x *GetActiveMarketsResponse) GetMarkets() []*Market {
	if x != nil {
		return x.Markets
	}
	return nil
}

type FilterMarketsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Types         []string               `protobuf:"bytes,1,rep,name=types,proto3" json:"types,omitempty"`
	Statuses      []string               `protobuf:"bytes,2,rep,name=statuses,proto3" json:"statuses,omitempty"`
	Currencies    []string               `protobuf:"bytes,3,rep,name=currencies,proto3" json:"currencies,omitempty"`
	IsActive      *bool                  `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	CountryCode   string                 `protobuf:"bytes,5,opt,name=country_code,json=countryCode,proto3" json:"country_code,omitempty"`
	OrderBy       string                 `protobuf:"bytes,6,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterMarketsRequest) Reset() {
	*x = FilterMarketsRequest{}
	mi := &file_api_protobuf_market_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterMarketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterMarketsRequest) ProtoMessage() {}

func (x *FilterMarketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterMarketsRequest.ProtoReflect.Descriptor instead.
func (*FilterMarketsRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{19}
}

func (x *FilterMarketsRequest) GetTypes() []string {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *FilterMarketsRequest) GetStatuses() []string {
	if x != nil {
		return x.Statuses
	}
	return nil
}

func (x *FilterMarketsRequest) GetCurrencies() []string {
	if x != nil {
		return x.Currencies
	}
	return nil
}

func (x *FilterMarketsRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *FilterMarketsRequest) GetCountryCode() string {
	if x != nil {
		return x.CountryCode
	}
	return ""
}

func (x *FilterMarketsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type FilterMarketsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Markets       []*Market              `protobuf:"bytes,1,rep,name=markets,proto3" json:"markets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterMarketsResponse) Reset() {
	*x = FilterMarketsResponse{}
	mi := &file_api_protobuf_market_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterMarketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterMarketsResponse) ProtoMessage() {}

func (x *FilterMarketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_market_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterMarketsResponse.ProtoReflect.Descriptor instead.
func (*FilterMarketsResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_market_proto_rawDescGZIP(), []int{20}
}

func (x *FilterMarketsResponse) GetMarkets() []*Market {
	if x != nil {
		return x.Markets
	}
	return nil
}

var File_api_protobuf_market_proto protoreflect.FileDescriptor

const file_api_protobuf_market_proto_rawDesc = "" +
	"\n" +
	"\x19api/protobuf/market.proto\x12\tmarket.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc0\x04\n" +
	"\x06Market\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12!\n" +
	"\fdisplay_name\x18\x03 \x01(\tR\vdisplayName\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x16\n" +
	"\x06status\x18\x05 \x01(\tR\x06status\x12\x19\n" +
	"\bbase_url\x18\x06 \x01(\tR\abaseUrl\x12\x1a\n" +
	"\bcurrency\x18\a \x01(\tR\bcurrency\x12*\n" +
	"\x11buyer_fee_percent\x18\b \x01(\x01R\x0fbuyerFeePercent\x12,\n" +
	"\x12seller_fee_percent\x18\t \x01(\x01R\x10sellerFeePercent\x12!\n" +
	"\fcountry_code\x18\n" +
	" \x01(\tR\vcountryCode\x12\x1a\n" +
	"\blanguage\x18\v \x01(\tR\blanguage\x12 \n" +
	"\vdescription\x18\f \x01(\tR\vdescription\x12\x1b\n" +
	"\tis_active\x18\r \x01(\bR\bisActive\x12>\n" +
	"\rlast_crawl_at\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampR\vlastCrawlAt\x129\n" +
	"\n" +
	"created_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xf1\x02\n" +
	"\x13CreateMarketRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12!\n" +
	"\fdisplay_name\x18\x02 \x01(\tR\vdisplayName\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12\x19\n" +
	"\bbase_url\x18\x04 \x01(\tR\abaseUrl\x12\x1a\n" +
	"\bcurrency\x18\x05 \x01(\tR\bcurrency\x12*\n" +
	"\x11buyer_fee_percent\x18\x06 \x01(\x01R\x0fbuyerFeePercent\x12,\n" +
	"\x12seller_fee_percent\x18\a \x01(\x01R\x10sellerFeePercent\x12!\n" +
	"\fcountry_code\x18\b \x01(\tR\vcountryCode\x12\x1a\n" +
	"\blanguage\x18\t \x01(\tR\blanguage\x12 \n" +
	"\vdescription\x18\n" +
	" \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"created_by\x18\v \x01(\tR\tcreatedBy\"J\n" +
	"\x14CreateMarketResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x81\x03\n" +
	"\x13UpdateMarketRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12!\n" +
	"\fdisplay_name\x18\x03 \x01(\tR\vdisplayName\x12\x12\n" +
	"\x04type\x18\x04 \x01(\tR\x04type\x12\x19\n" +
	"\bbase_url\x18\x05 \x01(\tR\abaseUrl\x12\x1a\n" +
	"\bcurrency\x18\x06 \x01(\tR\bcurrency\x12*\n" +
	"\x11buyer_fee_percent\x18\a \x01(\x01R\x0fbuyerFeePercent\x12,\n" +
	"\x12seller_fee_percent\x18\b \x01(\x01R\x10sellerFeePercent\x12!\n" +
	"\fcountry_code\x18\t \x01(\tR\vcountryCode\x12\x1a\n" +
	"\blanguage\x18\n" +
	" \x01(\tR\blanguage\x12 \n" +
	"\vdescription\x18\v \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"updated_by\x18\f \x01(\tR\tupdatedBy\"J\n" +
	"\x14UpdateMarketResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"D\n" +
	"\x13DeleteMarketRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\tR\tdeletedBy\"J\n" +
	"\x14DeleteMarketResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"U\n" +
	"\x13ActiveMarketRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12!\n" +
	"\factivated_by\x18\x02 \x01(\tR\vactivatedBy\"J\n" +
	"\x14ActiveMarketResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"[\n" +
	"\x15DeactiveMarketRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12%\n" +
	"\x0edeactivated_by\x18\x02 \x01(\tR\rdeactivatedBy\"L\n" +
	"\x16DeactiveMarketResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"_\n" +
	"\x1bSetMarketMaintenanceRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12#\n" +
	"\rmaintained_by\x18\x02 \x01(\tR\fmaintainedBy\"R\n" +
	"\x1cSetMarketMaintenanceResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"b\n" +
	"\x14GetAllMarketsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x19\n" +
	"\border_by\x18\x03 \x01(\tR\aorderBy\"\xac\x01\n" +
	"\x15GetAllMarketsResponse\x12+\n" +
	"\amarkets\x18\x01 \x03(\v2\x11.market.v1.MarketR\amarkets\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_pages\x18\x05 \x01(\x05R\n" +
	"totalPages\"&\n" +
	"\x14GetMarketByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"B\n" +
	"\x15GetMarketByIdResponse\x12)\n" +
	"\x06market\x18\x01 \x01(\v2\x11.market.v1.MarketR\x06market\"\x19\n" +
	"\x17GetActiveMarketsRequest\"G\n" +
	"\x18GetActiveMarketsResponse\x12+\n" +
	"\amarkets\x18\x01 \x03(\v2\x11.market.v1.MarketR\amarkets\"\xd6\x01\n" +
	"\x14FilterMarketsRequest\x12\x14\n" +
	"\x05types\x18\x01 \x03(\tR\x05types\x12\x1a\n" +
	"\bstatuses\x18\x02 \x03(\tR\bstatuses\x12\x1e\n" +
	"\n" +
	"currencies\x18\x03 \x03(\tR\n" +
	"currencies\x12 \n" +
	"\tis_active\x18\x04 \x01(\bH\x00R\bisActive\x88\x01\x01\x12!\n" +
	"\fcountry_code\x18\x05 \x01(\tR\vcountryCode\x12\x19\n" +
	"\border_by\x18\x06 \x01(\tR\aorderByB\f\n" +
	"\n" +
	"_is_active\"D\n" +
	"\x15FilterMarketsResponse\x12+\n" +
	"\amarkets\x18\x01 \x03(\v2\x11.market.v1.MarketR\amarkets2\xec\x06\n" +
	"\rMarketService\x12O\n" +
	"\fCreateMarket\x12\x1e.market.v1.CreateMarketRequest\x1a\x1f.market.v1.CreateMarketResponse\x12O\n" +
	"\fUpdateMarket\x12\x1e.market.v1.UpdateMarketRequest\x1a\x1f.market.v1.UpdateMarketResponse\x12O\n" +
	"\fDeleteMarket\x12\x1e.market.v1.DeleteMarketRequest\x1a\x1f.market.v1.DeleteMarketResponse\x12O\n" +
	"\fActiveMarket\x12\x1e.market.v1.ActiveMarketRequest\x1a\x1f.market.v1.ActiveMarketResponse\x12U\n" +
	"\x0eDeactiveMarket\x12 .market.v1.DeactiveMarketRequest\x1a!.market.v1.DeactiveMarketResponse\x12g\n" +
	"\x14SetMarketMaintenance\x12&.market.v1.SetMarketMaintenanceRequest\x1a'.market.v1.SetMarketMaintenanceResponse\x12R\n" +
	"\rGetAllMarkets\x12\x1f.market.v1.GetAllMarketsRequest\x1a .market.v1.GetAllMarketsResponse\x12R\n" +
	"\rGetMarketById\x12\x1f.market.v1.GetMarketByIdRequest\x1a .market.v1.GetMarketByIdResponse\x12[\n" +
	"\x10GetActiveMarkets\x12\".market.v1.GetActiveMarketsRequest\x1a#.market.v1.GetActiveMarketsResponse\x12R\n" +
	"\rFilterMarkets\x12\x1f.market.v1.FilterMarketsRequest\x1a .market.v1.FilterMarketsResponseB\x1eZ\x1cgo_core_market/pkg/pb/marketb\x06proto3"

var (
	file_api_protobuf_market_proto_rawDescOnce sync.Once
	file_api_protobuf_market_proto_rawDescData []byte
)

func file_api_protobuf_market_proto_rawDescGZIP() []byte {
	file_api_protobuf_market_proto_rawDescOnce.Do(func() {
		file_api_protobuf_market_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protobuf_market_proto_rawDesc), len(file_api_protobuf_market_proto_rawDesc)))
	})
	return file_api_protobuf_market_proto_rawDescData
}

var file_api_protobuf_market_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_api_protobuf_market_proto_goTypes = []any{
	(*Market)(nil),                       // 0: market.v1.Market
	(*CreateMarketRequest)(nil),          // 1: market.v1.CreateMarketRequest
	(*CreateMarketResponse)(nil),         // 2: market.v1.CreateMarketResponse
	(*UpdateMarketRequest)(nil),          // 3: market.v1.UpdateMarketRequest
	(*UpdateMarketResponse)(nil),         // 4: market.v1.UpdateMarketResponse
	(*DeleteMarketRequest)(nil),          // 5: market.v1.DeleteMarketRequest
	(*DeleteMarketResponse)(nil),         // 6: market.v1.DeleteMarketResponse
	(*ActiveMarketRequest)(nil),          // 7: market.v1.ActiveMarketRequest
	(*ActiveMarketResponse)(nil),         // 8: market.v1.ActiveMarketResponse
	(*DeactiveMarketRequest)(nil),        // 9: market.v1.DeactiveMarketRequest
	(*DeactiveMarketResponse)(nil),       // 10: market.v1.DeactiveMarketResponse
	(*SetMarketMaintenanceRequest)(nil),  // 11: market.v1.SetMarketMaintenanceRequest
	(*SetMarketMaintenanceResponse)(nil), // 12: market.v1.SetMarketMaintenanceResponse
	(*GetAllMarketsRequest)(nil),         // 13: market.v1.GetAllMarketsRequest
	(*GetAllMarketsResponse)(nil),        // 14: market.v1.GetAllMarketsResponse
	(*GetMarketByIdRequest)(nil),         // 15: market.v1.GetMarketByIdRequest
	(*GetMarketByIdResponse)(nil),        // 16: market.v1.GetMarketByIdResponse
	(*GetActiveMarketsRequest)(nil),      // 17: market.v1.GetActiveMarketsRequest
	(*GetActiveMarketsResponse)(nil),     // 18: market.v1.GetActiveMarketsResponse
	(*FilterMarketsRequest)(nil),         // 19: market.v1.FilterMarketsRequest
	(*FilterMarketsResponse)(nil),        // 20: market.v1.FilterMarketsResponse
	(*timestamppb.Timestamp)(nil),        // 21: google.protobuf.Timestamp
}
var file_api_protobuf_market_proto_depIdxs = []int32{
	21, // 0: market.v1.Market.last_crawl_at:type_name -> google.protobuf.Timestamp
	21, // 1: market.v1.Market.created_at:type_name -> google.protobuf.Timestamp
	21, // 2: market.v1.Market.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 3: market.v1.GetAllMarketsResponse.markets:type_name -> market.v1.Market
	0,  // 4: market.v1.GetMarketByIdResponse.market:type_name -> market.v1.Market
	0,  // 5: market.v1.GetActiveMarketsResponse.markets:type_name -> market.v1.Market
	0,  // 6: market.v1.FilterMarketsResponse.markets:type_name -> market.v1.Market
	1,  // 7: market.v1.MarketService.CreateMarket:input_type -> market.v1.CreateMarketRequest
	3,  // 8: market.v1.MarketService.UpdateMarket:input_type -> market.v1.UpdateMarketRequest
	5,  // 9: market.v1.MarketService.DeleteMarket:input_type -> market.v1.DeleteMarketRequest
	7,  // 10: market.v1.MarketService.ActiveMarket:input_type -> market.v1.ActiveMarketRequest
	9,  // 11: market.v1.MarketService.DeactiveMarket:input_type -> market.v1.DeactiveMarketRequest
	11, // 12: market.v1.MarketService.SetMarketMaintenance:input_type -> market.v1.SetMarketMaintenanceRequest
	13, // 13: market.v1.MarketService.GetAllMarkets:input_type -> market.v1.GetAllMarketsRequest
	15, // 14: market.v1.MarketService.GetMarketById:input_type -> market.v1.GetMarketByIdRequest
	17, // 15: market.v1.MarketService.GetActiveMarkets:input_type -> market.v1.GetActiveMarketsRequest
	19, // 16: market.v1.MarketService.FilterMarkets:input_type -> market.v1.FilterMarketsRequest
	2,  // 17: market.v1.MarketService.CreateMarket:output_type -> market.v1.CreateMarketResponse
	4,  // 18: market.v1.MarketService.UpdateMarket:output_type -> market.v1.UpdateMarketResponse
	6,  // 19: market.v1.MarketService.DeleteMarket:output_type -> market.v1.DeleteMarketResponse
	8,  // 20: market.v1.MarketService.ActiveMarket:output_type -> market.v1.ActiveMarketResponse
	10, // 21: market.v1.MarketService.DeactiveMarket:output_type -> market.v1.DeactiveMarketResponse
	12, // 22: market.v1.MarketService.SetMarketMaintenance:output_type -> market.v1.SetMarketMaintenanceResponse
	14, // 23: market.v1.MarketService.GetAllMarkets:output_type -> market.v1.GetAllMarketsResponse
	16, // 24: market.v1.MarketService.GetMarketById:output_type -> market.v1.GetMarketByIdResponse
	18, // 25: market.v1.MarketService.GetActiveMarkets:output_type -> market.v1.GetActiveMarketsResponse
	20, // 26: market.v1.MarketService.FilterMarkets:output_type -> market.v1.FilterMarketsResponse
	17, // [17:27] is the sub-list for method output_type
	7,  // [7:17] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_protobuf_market_proto_init() }
func file_api_protobuf_market_proto_init() {
	if File_api_protobuf_market_proto != nil {
		return
	}
	file_api_protobuf_market_proto_msgTypes[19].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protobuf_market_proto_rawDesc), len(file_api_protobuf_market_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protobuf_market_proto_goTypes,
		DependencyIndexes: file_api_protobuf_market_proto_depIdxs,
		MessageInfos:      file_api_protobuf_market_proto_msgTypes,
	}.Build()
	File_api_protobuf_market_proto = out.File
	file_api_protobuf_market_proto_goTypes = nil
	file_api_protobuf_market_proto_depIdxs = nil
}
