package youpin

type saleTemplateRequest struct {
	ListSortType int `json:"listSortType"`
	PageIndex    int `json:"pageIndex"`
	PageSize     int `json:"pageSize"`
	SortType     int `json:"sortType"`
}
type CommodityItem struct {
	ID                 int    `json:"id"`
	CommodityName      string `json:"commodityName"`
	CommodityHashName  string `json:"commodityHashName"`
	IconUrl            string `json:"iconUrl"`
	IconUrlLarge       string `json:"iconUrlLarge"`
	OnSaleCount        int    `json:"onSaleCount"`
	OnLeaseCount       int    `json:"onLeaseCount"`
	LeaseUnitPrice     string `json:"leaseUnitPrice"`
	LongLeaseUnitPrice string `json:"longLeaseUnitPrice"`
	LeaseDeposit       string `json:"leaseDeposit"`
	Price              string `json:"price"`
	SteamPrice         string `json:"steamPrice"`
	SteamUsdPrice      string `json:"steamUsdPrice"`
	TypeName           string `json:"typeName"`
	HaveLease          int    `json:"haveLease"`
	SubsidyPurchase    int    `json:"subsidyPurchase"`
	Rent               string `json:"rent"`
	ListType           int    `json:"listType"`
}
type SaleTemplateResponse struct {
	Data       []CommodityItem `json:"Data"`
	Code       int             `json:"Code"`
	Msg        string          `json:"Msg"`
	TotalCount int             `json:"TotalCount"`
}
