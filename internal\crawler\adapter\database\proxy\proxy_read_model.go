package repository

import (
	"context"
	"time"

	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	sqlc2 "go_core_market/internal/crawler/adapter/database/proxy/sqlc"
	"go_core_market/pkg/database"
)

type proxyReadModel struct {
	db      database.DBTX
	queries sqlc2.Querier
}

// NewProxyReadModel creates a new proxy read model
func NewProxyReadModel(db database.DBTX) queryproxy.ProxyReadModel {
	return &proxyReadModel{
		db:      db,
		queries: sqlc2.New(db),
	}
}

// AllProxies retrieves all proxies with pagination and ordering
func (r *proxyReadModel) AllProxies(ctx context.Context, query queryproxy.GetAllProxiesQuery) ([]*queryproxy.Proxy, error) {
	// Calculate offset for pagination
	offset := (query.Page - 1) * query.PageSize
	
	// Prepare parameters for sqlc query
	params := sqlc2.GetAllProxiesParams{
		Limit:   int32(query.PageSize),
		Offset:  int32(offset),
		Column3: query.OrderBy, // This maps to the ORDER BY parameter
	}

	sqlcProxies, err := r.queries.GetAllProxies(ctx, params)
	if err != nil {
		return nil, err
	}

	// Convert sqlc models to query models
	proxies := make([]*queryproxy.Proxy, len(sqlcProxies))
	for i, sqlcProxy := range sqlcProxies {
		proxies[i] = fromSQLCToQueryModel(*sqlcProxy)
	}

	return proxies, nil
}

// GetProxyById retrieves a proxy by its ID
func (r *proxyReadModel) GetProxyById(ctx context.Context, id int) (*queryproxy.Proxy, error) {
	sqlcProxy, err := r.queries.FindProxyByID(ctx, int32(id))
	if err != nil {
		return nil, err
	}

	return fromSQLCToQueryModel(*sqlcProxy), nil
}

// CountProxies returns the total count of proxies
func (r *proxyReadModel) CountProxies(ctx context.Context) (int64, error) {
	return r.queries.CountProxies(ctx)
}

// GetAvailableProxies retrieves available proxies (active and not in use)
func (r *proxyReadModel) GetAvailableProxies(ctx context.Context, limit int) ([]*queryproxy.Proxy, error) {
	sqlcProxies, err := r.queries.GetAvailableProxies(ctx, int32(limit))
	if err != nil {
		return nil, err
	}

	// Convert sqlc models to query models
	proxies := make([]*queryproxy.Proxy, len(sqlcProxies))
	for i, sqlcProxy := range sqlcProxies {
		proxies[i] = fromSQLCToQueryModel(*sqlcProxy)
	}

	return proxies, nil
}

// fromSQLCToQueryModel converts sqlc.Proxy to queryproxy.Proxy
func fromSQLCToQueryModel(sqlcProxy sqlc2.Proxy) *queryproxy.Proxy {
	var lastUsedAt *time.Time
	if sqlcProxy.LastUsedAt.Valid {
		lastUsedAt = &sqlcProxy.LastUsedAt.Time
	}

	var createdAt time.Time
	if sqlcProxy.CreatedAt.Valid {
		createdAt = sqlcProxy.CreatedAt.Time
	}

	var updatedAt time.Time
	if sqlcProxy.UpdatedAt.Valid {
		updatedAt = sqlcProxy.UpdatedAt.Time
	}

	return &queryproxy.Proxy{
		Id:         int(sqlcProxy.ID),
		Host:       sqlcProxy.Host,
		Port:       int(sqlcProxy.Port),
		UserName:   sqlcProxy.UserName,
		Password:   sqlcProxy.Password,
		IsActive:   sqlcProxy.IsActive,
		IsUse:      sqlcProxy.IsUse,
		LastUsedAt: lastUsedAt,
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}
}
