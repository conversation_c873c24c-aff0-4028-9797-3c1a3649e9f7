package grpc

import (
	"context"
	app2 "go_core_market/internal/crawler/app"
	commandcrawlconfig "go_core_market/internal/crawler/app/command/crawlconfig"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	pb "go_core_market/pkg/pb/configcrawl"
)

type CrawlConfigGRPCServer struct {
	app app2.Application
	pb.UnimplementedCrawlConfigServiceServer
}

func NewCrawlConfigGRPCServer(app app2.Application) *CrawlConfigGRPCServer {
	return &CrawlConfigGRPCServer{
		app: app,
	}
}

// Command handlers
func (s *CrawlConfigGRPCServer) CreateCrawlConfig(ctx context.Context, req *pb.CreateCrawlConfigRequest) (*pb.CreateCrawlConfigResponse, error) {
	cmd := commandcrawlconfig.CreateCrawlConfig{
		MarketID:               int(req.MarketId),
		Description:            req.Description,
		TypeConfig:             req.TypeConfig,
		RequestsPerMinute:      int(req.RequestsPerMinute),
		RequireAuth:            req.RequireAuth,
		MaxNumberAuth:          int(req.MaxNumberAuth),
		MaxNumberProxy:         int(req.MaxNumberProxy),
		PerRequestDelaySeconds: int(req.PerRequestDelaySeconds),
		TimeoutSeconds:         int(req.TimeoutSeconds),
		MaxRetries:             int(req.MaxRetries),
		RetryDelaySeconds:      int(req.RetryDelaySeconds),
		MaxConcurrent:          int(req.MaxConcurrent),
		CreatedBy:              req.CreatedBy,
	}

	err := s.app.Commands.CrawlConfig.CreateCrawlConfig.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateCrawlConfigResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateCrawlConfigResponse{
		Success: true,
		Message: "Crawl config created successfully",
	}, nil
}

func (s *CrawlConfigGRPCServer) UpdateCrawlConfig(ctx context.Context, req *pb.UpdateCrawlConfigRequest) (*pb.UpdateCrawlConfigResponse, error) {

	return &pb.UpdateCrawlConfigResponse{
		Success: true,
		Message: "Crawl config updated successfully",
	}, nil
}

func (s *CrawlConfigGRPCServer) ActivateCrawlConfig(ctx context.Context, req *pb.ActivateCrawlConfigRequest) (*pb.ActivateCrawlConfigResponse, error) {
	cmd := commandcrawlconfig.ActivateCrawlConfig{
		ID:          int(req.Id),
		ActivatedBy: req.ActivatedBy,
	}

	err := s.app.Commands.CrawlConfig.ActivateCrawlConfig.Handle(ctx, cmd)
	if err != nil {
		return &pb.ActivateCrawlConfigResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ActivateCrawlConfigResponse{
		Success: true,
		Message: "Crawl config activated successfully",
	}, nil
}

func (s *CrawlConfigGRPCServer) DeactivateCrawlConfig(ctx context.Context, req *pb.DeactivateCrawlConfigRequest) (*pb.DeactivateCrawlConfigResponse, error) {
	cmd := commandcrawlconfig.DeactivateCrawlConfig{
		ID:            int(req.Id),
		DeactivatedBy: req.DeactivatedBy,
	}

	err := s.app.Commands.CrawlConfig.DeactivateCrawlConfig.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeactivateCrawlConfigResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeactivateCrawlConfigResponse{
		Success: true,
		Message: "Crawl config deactivated successfully",
	}, nil
}

func (s *CrawlConfigGRPCServer) UpdateLastUsed(ctx context.Context, req *pb.UpdateLastUsedRequest) (*pb.UpdateLastUsedResponse, error) {
	cmd := commandcrawlconfig.UpdateLastUsed{
		ID:     int(req.Id),
		UsedBy: req.UsedBy,
	}

	err := s.app.Commands.CrawlConfig.UpdateLastUsed.Handle(ctx, cmd)
	if err != nil {
		return &pb.UpdateLastUsedResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.UpdateLastUsedResponse{
		Success: true,
		Message: "Last used updated successfully",
	}, nil
}

func (s *CrawlConfigGRPCServer) DeleteCrawlConfig(ctx context.Context, req *pb.DeleteCrawlConfigRequest) (*pb.DeleteCrawlConfigResponse, error) {
	cmd := commandcrawlconfig.DeleteCrawlConfig{
		ID:        int(req.Id),
		DeletedBy: req.DeletedBy,
	}

	err := s.app.Commands.CrawlConfig.DeleteCrawlConfig.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeleteCrawlConfigResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteCrawlConfigResponse{
		Success: true,
		Message: "Crawl config deleted successfully",
	}, nil
}

// Query handlers
func (s *CrawlConfigGRPCServer) GetAllCrawlConfigs(ctx context.Context, req *pb.GetAllCrawlConfigsRequest) (*pb.GetAllCrawlConfigsResponse, error) {
	q := querycrawlconfig.GetAllCrawlConfigsQuery{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		OrderBy:  req.OrderBy,
	}

	result, err := s.app.Queries.CrawlConfig.GetAllCrawlConfigs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbConfigs []*pb.CrawlConfig
	for _, config := range result.Data {
		pbConfigs = append(pbConfigs, CrawlConfigToProto(config))
	}

	return &pb.GetAllCrawlConfigsResponse{
		Configs:    pbConfigs,
		Total:      int32(result.Pagination.Total),
		Page:       int32(result.Pagination.Page),
		PageSize:   int32(result.Pagination.PageSize),
		TotalPages: int32(result.Pagination.Pages),
	}, nil
}

func (s *CrawlConfigGRPCServer) GetCrawlConfigById(ctx context.Context, req *pb.GetCrawlConfigByIdRequest) (*pb.GetCrawlConfigByIdResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetCrawlConfigByIdResponse{
		Config: &pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetActiveCrawlConfigs(ctx context.Context, req *pb.GetActiveCrawlConfigsRequest) (*pb.GetActiveCrawlConfigsResponse, error) {
	q := querycrawlconfig.GetActiveCrawlConfigsQuery{}

	configs, err := s.app.Queries.CrawlConfig.GetActiveCrawlConfigs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetActiveCrawlConfigsResponse{
		Configs: CrawlConfigsToProto(configs),
	}, nil
}

func (s *CrawlConfigGRPCServer) GetCrawlConfigsByMarketId(ctx context.Context, req *pb.GetCrawlConfigsByMarketIdRequest) (*pb.GetCrawlConfigsByMarketIdResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetCrawlConfigsByMarketIdResponse{
		Configs: []*pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetCrawlConfigsByType(ctx context.Context, req *pb.GetCrawlConfigsByTypeRequest) (*pb.GetCrawlConfigsByTypeResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetCrawlConfigsByTypeResponse{
		Configs: []*pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetActiveCrawlConfigsByMarket(ctx context.Context, req *pb.GetActiveCrawlConfigsByMarketRequest) (*pb.GetActiveCrawlConfigsByMarketResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetActiveCrawlConfigsByMarketResponse{
		Configs: []*pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetActiveCrawlConfigsByType(ctx context.Context, req *pb.GetActiveCrawlConfigsByTypeRequest) (*pb.GetActiveCrawlConfigsByTypeResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetActiveCrawlConfigsByTypeResponse{
		Configs: []*pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetActiveCrawlConfigsByMarketAndType(ctx context.Context, req *pb.GetActiveCrawlConfigsByMarketAndTypeRequest) (*pb.GetActiveCrawlConfigsByMarketAndTypeResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetActiveCrawlConfigsByMarketAndTypeResponse{
		Configs: []*pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) FilterCrawlConfigs(ctx context.Context, req *pb.FilterCrawlConfigsRequest) (*pb.FilterCrawlConfigsResponse, error) {
	q := querycrawlconfig.FilterCrawlConfigsQuery{
		OrderBy: req.OrderBy,
	}

	// Handle optional fields
	if req.MarketId != nil {
		q.MarketId = int(*req.MarketId)
	}
	if req.TypeConfig != nil {
		q.TypeConfig = *req.TypeConfig
	}
	if req.IsActive != nil {
		q.IsActive = req.IsActive
	}
	if req.RequireAuth != nil {
		q.RequireAuth = req.RequireAuth
	}
	if req.CreatedFrom != nil {
		t := req.CreatedFrom.AsTime()
		q.CreatedFrom = &t
	}
	if req.CreatedTo != nil {
		t := req.CreatedTo.AsTime()
		q.CreatedTo = &t
	}

	configs, err := s.app.Queries.CrawlConfig.FilterCrawlConfigs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.FilterCrawlConfigsResponse{
		Configs: CrawlConfigsToProto(configs),
	}, nil
}

func (s *CrawlConfigGRPCServer) GetLeastUsedActiveConfig(ctx context.Context, req *pb.GetLeastUsedActiveConfigRequest) (*pb.GetLeastUsedActiveConfigResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetLeastUsedActiveConfigResponse{
		Config: &pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetLeastUsedActiveConfigByMarket(ctx context.Context, req *pb.GetLeastUsedActiveConfigByMarketRequest) (*pb.GetLeastUsedActiveConfigByMarketResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetLeastUsedActiveConfigByMarketResponse{
		Config: &pb.CrawlConfig{},
	}, nil
}

func (s *CrawlConfigGRPCServer) GetLeastUsedActiveConfigByType(ctx context.Context, req *pb.GetLeastUsedActiveConfigByTypeRequest) (*pb.GetLeastUsedActiveConfigByTypeResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetLeastUsedActiveConfigByTypeResponse{
		Config: &pb.CrawlConfig{},
	}, nil
}
