package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ReleaseAuth struct {
	ID         int
	ReleasedBy string
}

type ReleaseAuthHandler decorator.CommandHandler[ReleaseAuth]

type releaseAuthHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewReleaseAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ReleaseAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ReleaseAuth](
		releaseAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h releaseAuthHandler) Handle(ctx context.Context, cmd ReleaseAuth) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Auth) (*entity.Auth, error) {
		err := p.Release()
		if err != nil {
			return nil, err
		}
		return p, nil
	})
}

type ReleaseManyAuth struct {
	IDs        []int
	ReleasedBy string
}

type ReleaseManyAuthHandler decorator.CommandHandler[ReleaseManyAuth]

type releaseManyAuthHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewReleaseManyAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ReleaseManyAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ReleaseManyAuth](
		releaseManyAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h releaseManyAuthHandler) Handle(ctx context.Context, cmd ReleaseManyAuth) error {
	return h.repo.UpdateMany(ctx, cmd.IDs, func(p []*entity.Auth) ([]*entity.Auth, error) {
		for _, v := range p {
			err := v.Release()
			if err != nil {
				return nil, err
			}
		}
		return p, nil
	})
}
