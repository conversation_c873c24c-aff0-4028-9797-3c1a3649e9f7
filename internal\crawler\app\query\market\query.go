package querymarket

import (
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type MarketQueries struct {
	GetAllMarkets    GetAllMarketsQueryHandler
	GetMarketById    GetMarketByIdQueryHandler
	GetActiveMarkets GetActiveMarketsQueryHandler
	FilterMarkets    FilterMarketsQueryHandler
}

func NewMarketQueries(
	repo MarketReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) *MarketQueries {
	return &MarketQueries{
		GetAllMarkets:    NewGetAllMarketsQueryHandler(repo, logger, metricsClient),
		GetMarketById:    NewGetMarketByIdQueryHandler(repo, logger, metricsClient),
		GetActiveMarkets: NewGetActiveMarketsQueryHandler(repo, logger, metricsClient),
		FilterMarkets:    NewFilterMarketsQueryHandler(repo, logger, metricsClient),
	}
}
