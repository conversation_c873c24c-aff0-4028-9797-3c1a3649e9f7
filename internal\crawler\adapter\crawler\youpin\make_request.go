package youpin

import (
	"context"
	"fmt"
	"log"
	"time"
)

func (c *YoupinCrawler) GetCurrentHeaderSetInfo() (int, string) {
	index := c.headerIndex % len(defaultHeadersSets)
	headerSetNames := []string{
		"Chrome Windows", "Firefox Windows", "Safari macOS", "Edge Windows", "Chrome macOS",
		"Firefox macOS", "Chrome Linux", "Firefox Linux", "Opera Windows", "Brave Windows",
		"Vivaldi Windows", "Chrome Android", "Safari iOS", "Firefox Android", "Samsung Internet",
		"Chrome Tablet", "iPad Safari", "Chrome Chromebook", "UC Browser", "Opera Mobile",
	}
	return index + 1, headerSetNames[index]
}

func (c *YoupinCrawler) SetHeaderSet(index int) {
	c.rotationMutex.Lock()
	defer c.rotationMutex.Unlock()
	if index >= 0 && index < len(defaultHeadersSets) {
		c.headerIndex = index
		c.setDefaultHeaders()
	}
}

func (c *YoupinCrawler) rotateCredentials() {
	c.rotationMutex.Lock()
	defer c.rotationMutex.Unlock()

	// Rotate headers
	c.headerIndex = (c.headerIndex + 1) % len(defaultHeadersSets)
	c.setDefaultHeaders()

	if len(c.config.Cookies) > 0 {
		c.client.SetCookie(c.config.Cookies[c.cookieIndex])
		c.cookieIndex = (c.cookieIndex + 1) % len(c.config.Cookies)
	}
	if len(c.config.APIKeys) > 0 {
		c.client.SetAPIKey(c.config.APIKeys[c.apiKeyIndex])
		c.apiKeyIndex = (c.apiKeyIndex + 1) % len(c.config.APIKeys)
	}
	if len(c.config.Proxies) > 0 {
		c.client.SetProxy(c.config.Proxies[c.proxyIndex])
		c.proxyIndex = (c.proxyIndex + 1) % len(c.config.Proxies)
	}
}

func (c *YoupinCrawler) enforceRateLimit() {
	c.requestMutex.Lock()
	defer c.requestMutex.Unlock()
	if c.config.PerRequestDelay > 0 {
		delay := time.Duration(c.config.PerRequestDelay) * time.Second
		elapsed := time.Since(c.lastRequestTime)
		if elapsed < delay {
			time.Sleep(delay - elapsed)
		}
	}
	c.lastRequestTime = time.Now()
}

func (c *YoupinCrawler) makeRequest(ctx context.Context, path string, params map[string]string, body interface{}) ([]byte, error) {
	c.requestMutex.Lock()
	if c.config.PerRequestDelay > 0 {
		delay := time.Duration(c.config.PerRequestDelay) * time.Second
		elapsed := time.Since(c.lastRequestTime)
		if elapsed < delay {
			time.Sleep(delay - elapsed)
		}
	}
	c.requestMutex.Unlock()
	var lastErr error
	for attempt := 0; attempt <= c.config.MaxRetries; attempt++ {
		c.rotateCredentials()
		log.Printf("Request: %s %s ", path, params)
		data, err := c.client.Post(path, nil, body)
		if err == nil {
			c.requestMutex.Lock()
			c.lastRequestTime = time.Now()
			c.requestMutex.Unlock()
			return data, nil
		}
		lastErr = err
		log.Println(lastErr)
		if attempt < c.config.MaxRetries {
			delay := time.Duration(c.config.RetryDelay) * time.Second
			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return nil, ctx.Err()
			}
		}
	}
	return nil, fmt.Errorf("request failed after %d attempts: %w", c.config.MaxRetries+1, lastErr)
}

func (c *YoupinCrawler) validateAPIResponse(code int, msg string) error {
	if code != 0 {
		if msg != "success" {
			return fmt.Errorf("API error: %s", msg)
		}
		return fmt.Errorf("API error")
	}
	return nil
}
