-- name: Create :one
INSERT INTO crawl_configs (
    market_id,
    description,
    type_config,
    requests_per_minute,
    require_auth,
    max_number_auth,
    max_number_proxy,
    per_request_delay_seconds,
    timeout_seconds,
    max_retries,
    retry_delay_seconds,
    max_concurrent,
    is_active
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
)
RETURNING id;

-- name: CreateMany :copyfrom
INSERT INTO crawl_configs (
    market_id,
    description,
    type_config,
    requests_per_minute,
    require_auth,
    max_number_auth,
    max_number_proxy,
    per_request_delay_seconds,
    timeout_seconds,
    max_retries,
    retry_delay_seconds,
    max_concurrent,
    is_active
) VALUES
    ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13);

-- name: FindByID :one
SELECT * FROM crawl_configs WHERE id = $1 LIMIT 1;

-- name: UpdateCrawlConfig :one
UPDATE crawl_configs
SET market_id = $2,
    description = $3,
    type_config = $4,
    requests_per_minute = $5,
    require_auth = $6,
    max_number_auth = $7,
    max_number_proxy = $8,
    per_request_delay_seconds = $9,
    timeout_seconds = $10,
    max_retries = $11,
    retry_delay_seconds = $12,
    max_concurrent = $13,
    is_active = $14,
    last_used_at = $15
WHERE id = $1
RETURNING *;

-- name: UpdateLastUsedAt :one
UPDATE crawl_configs
SET last_used_at = NOW()
WHERE id = $1
RETURNING *;

-- name: ActivateConfig :one
UPDATE crawl_configs
SET is_active = true
WHERE id = $1
RETURNING *;

-- name: DeactivateConfig :one
UPDATE crawl_configs
SET is_active = false
WHERE id = $1
RETURNING *;

-- name: DeleteCrawlConfig :exec
DELETE FROM crawl_configs WHERE id = $1;

-- Query methods for CrawlConfigQueryRepository

-- name: GetAllCrawlConfigsWithPagination :many
SELECT * FROM crawl_configs
ORDER BY
    CASE WHEN $3 = 'description' THEN description END ASC,
    CASE WHEN $3 = 'description_desc' THEN description END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = 'last_used_at' THEN last_used_at END ASC,
    CASE WHEN $3 = 'last_used_at_desc' THEN last_used_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2;

-- name: GetCrawlConfigByIdQuery :one
SELECT * FROM crawl_configs WHERE id = $1 LIMIT 1;

-- name: GetActiveCrawlConfigsQuery :many
SELECT * FROM crawl_configs WHERE is_active = true ORDER BY last_used_at ASC NULLS FIRST;

-- name: GetCrawlConfigsByMarketIdQuery :many
SELECT * FROM crawl_configs WHERE market_id = $1 ORDER BY created_at DESC;

-- name: GetCrawlConfigsByTypeQuery :many
SELECT * FROM crawl_configs WHERE type_config = $1 ORDER BY created_at DESC;

-- name: GetActiveCrawlConfigsByMarketQuery :many
SELECT * FROM crawl_configs 
WHERE market_id = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST;

-- name: GetActiveCrawlConfigsByTypeQuery :many
SELECT * FROM crawl_configs 
WHERE type_config = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST;

-- name: GetActiveCrawlConfigsByMarketAndTypeQuery :many
SELECT * FROM crawl_configs 
WHERE market_id = $1 AND type_config = $2 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST;

-- name: CountCrawlConfigsQuery :one
SELECT COUNT(*) FROM crawl_configs;

-- name: CountActiveCrawlConfigsQuery :one
SELECT COUNT(*) FROM crawl_configs WHERE is_active = true;

-- name: CountCrawlConfigsByMarketQuery :one
SELECT COUNT(*) FROM crawl_configs WHERE market_id = $1;

-- name: FilterCrawlConfigsQuery :many
SELECT * FROM crawl_configs
WHERE
    ($1 = 0 OR market_id = $1)
    AND ($2 = '' OR type_config = $2)
    AND ($3::boolean IS NULL OR is_active = $3)
    AND ($4::boolean IS NULL OR require_auth = $4)
    AND ($5::timestamptz IS NULL OR created_at >= $5)
    AND ($6::timestamptz IS NULL OR created_at <= $6)
ORDER BY
    CASE WHEN $7 = 'description' THEN description END ASC,
    CASE WHEN $7 = 'description_desc' THEN description END DESC,
    CASE WHEN $7 = 'created_at' THEN created_at END ASC,
    CASE WHEN $7 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $7 = 'last_used_at' THEN last_used_at END ASC,
    CASE WHEN $7 = 'last_used_at_desc' THEN last_used_at END DESC,
    CASE WHEN $7 = '' OR $7 IS NULL THEN id END ASC;

-- name: GetLeastUsedActiveConfigQuery :one
SELECT * FROM crawl_configs 
WHERE is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1;

-- name: GetLeastUsedActiveConfigByMarketQuery :one
SELECT * FROM crawl_configs 
WHERE market_id = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1;

-- name: GetLeastUsedActiveConfigByTypeQuery :one
SELECT * FROM crawl_configs 
WHERE type_config = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1;
