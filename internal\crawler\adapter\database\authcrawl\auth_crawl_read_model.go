package repository

import (
	"context"
	"time"

	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	sqlc2 "go_core_market/internal/crawler/adapter/database/authcrawl/sqlc"
	"go_core_market/pkg/database"
)

type authCrawlReadModel struct {
	db      database.DBTX
	queries sqlc2.Querier
}

// NewAuthCrawlReadModel creates a new auth crawl read model
func NewAuthCrawlReadModel(db database.DBTX) queryauthcral.RepositoryQueryAuth {
	return &authCrawlReadModel{
		db:      db,
		queries: sqlc2.New(db),
	}
}

// AllAuth retrieves all auth crawl records with pagination and ordering
func (r *authCrawlReadModel) AllAuth(ctx context.Context, query queryauthcral.AllAuthCrawlQuery) ([]*queryauthcral.AuthCrawl, error) {
	// Calculate offset for pagination
	offset := (query.Page - 1) * query.PageSize
	
	// Prepare parameters for sqlc query
	params := sqlc2.GetAllAuthCrawlParams{
		Limit:   int32(query.PageSize),
		Offset:  int32(offset),
		Column3: query.Order, // This maps to the ORDER BY parameter
	}

	sqlcAuthCrawls, err := r.queries.GetAllAuthCrawl(ctx, params)
	if err != nil {
		return nil, err
	}

	// Convert sqlc models to query models
	authCrawls := make([]*queryauthcral.AuthCrawl, len(sqlcAuthCrawls))
	for i, sqlcAuthCrawl := range sqlcAuthCrawls {
		authCrawls[i] = fromSQLCToQueryModel(*sqlcAuthCrawl)
	}

	return authCrawls, nil
}

// GetAuthCrawlById retrieves an auth crawl record by its ID
func (r *authCrawlReadModel) GetAuthCrawlById(ctx context.Context, id int) (*queryauthcral.AuthCrawl, error) {
	sqlcAuthCrawl, err := r.queries.FindAuthByID(ctx, id)
	if err != nil {
		return nil, err
	}

	return fromSQLCToQueryModel(*sqlcAuthCrawl), nil
}

// GetAuthCrawlByMarketId retrieves an auth crawl record by market ID
func (r *authCrawlReadModel) GetAuthCrawlByMarketId(ctx context.Context, marketId int) (*queryauthcral.AuthCrawl, error) {
	sqlcAuthCrawl, err := r.queries.GetAuthCrawlByMarketId(ctx, marketId)
	if err != nil {
		return nil, err
	}

	return fromSQLCToQueryModel(*sqlcAuthCrawl), nil
}

// GetAvailableAuthCrawlByMarket retrieves available auth crawl records for a specific market
func (r *authCrawlReadModel) GetAvailableAuthCrawlByMarket(ctx context.Context, marketId int, limit int) ([]*queryauthcral.AuthCrawl, error) {
	params := sqlc2.GetAvailableAuthCrawlByMarketParams{
		MarketID: marketId,
		Limit:    int32(limit),
	}

	sqlcAuthCrawls, err := r.queries.GetAvailableAuthCrawlByMarket(ctx, params)
	if err != nil {
		return nil, err
	}

	// Convert sqlc models to query models
	authCrawls := make([]*queryauthcral.AuthCrawl, len(sqlcAuthCrawls))
	for i, sqlcAuthCrawl := range sqlcAuthCrawls {
		authCrawls[i] = fromSQLCToQueryModel(*sqlcAuthCrawl)
	}

	return authCrawls, nil
}

// CountAuthCrawl returns the total count of auth crawl records
func (r *authCrawlReadModel) CountAuthCrawl(ctx context.Context) (int64, error) {
	return r.queries.CountAuthCrawl(ctx)
}

// fromSQLCToQueryModel converts sqlc.AuthCrawl to queryauthcral.AuthCrawl
func fromSQLCToQueryModel(sqlcAuthCrawl sqlc2.AuthCrawl) *queryauthcral.AuthCrawl {
	var lastUsedAt *time.Time
	if sqlcAuthCrawl.LastUsedAt.Valid {
		lastUsedAt = &sqlcAuthCrawl.LastUsedAt.Time
	}

	var expiredAt *time.Time
	if sqlcAuthCrawl.ExpiredAt.Valid {
		expiredAt = &sqlcAuthCrawl.ExpiredAt.Time
	}

	var createdAt time.Time
	if sqlcAuthCrawl.CreatedAt.Valid {
		createdAt = sqlcAuthCrawl.CreatedAt.Time
	}

	var updatedAt time.Time
	if sqlcAuthCrawl.UpdatedAt.Valid {
		updatedAt = sqlcAuthCrawl.UpdatedAt.Time
	}

	return &queryauthcral.AuthCrawl{
		Id:         sqlcAuthCrawl.ID,
		MarketID:   sqlcAuthCrawl.MarketID,
		AuthType:   sqlcAuthCrawl.AuthType,
		Value:      sqlcAuthCrawl.Value,
		IsActive:   sqlcAuthCrawl.IsActive,
		IsUse:      sqlcAuthCrawl.IsUse,
		LastUsedAt: lastUsedAt,
		ExpiredAt:  expiredAt,
		CreatedAt:  createdAt,
		UpdatedAt:  updatedAt,
	}
}
