package repository

import (
	"context"
	"errors"
	sqlc2 "go_core_market/internal/crawler/adapter/database/proxy/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/database"
)

var (
	ErrProxyNotFound = errors.New("proxy not found")
	ErrProxyExist    = errors.New("proxy exist")
	ErrProxyNotExist = errors.New("proxy not exist")
	ErrNoFunFN       = errors.New("no fun function")
)

type proxyRepository struct {
	db      database.DBTX
	queries sqlc2.Querier
}

// NewProxyRepository creates a new proxy repository
func NewProxyRepository(db database.DBTX) repository.RepositoryProxy {
	return &proxyRepository{
		db:      db,
		queries: sqlc2.New(db),
	}
}

func (p proxyRepository) Create(ctx context.Context, proxy *entity.Proxy) error {
	q := ToCreateProxyParams(proxy)
	_, err := p.queries.CreateProxy(ctx, q)
	if err != nil {
		return err
	}
	return nil
}

func (p proxyRepository) CreateMany(ctx context.Context, proxies []*entity.Proxy) error {
	ps := ToCreateProxiesParams(proxies)
	_, err := p.queries.CreateProxies(ctx, ps)
	if err != nil {
		return err
	}
	return nil
}

func (p proxyRepository) Update(ctx context.Context, id int, updateFn func(p *entity.Proxy) (*entity.Proxy, error)) error {
	proxy, err := p.queries.FindProxyByID(ctx, int32(id))
	if err != nil {
		return ErrProxyNotFound
	}
	domainProxy := FromSQLCModel(*proxy)
	if updateFn != nil {
		domainProxy, err = updateFn(domainProxy)
		if err != nil {
			return err
		}
		pr := ToUpdateProxyParams(domainProxy)
		_, err := p.queries.UpdateProxy(ctx, pr)
		if err != nil {
			return err
		}
		return nil
	}
	return ErrNoFunFN
}

func (p proxyRepository) UpdateMany(ctx context.Context, ids []int, updateFn func(p []*entity.Proxy) ([]*entity.Proxy, error)) error {

	panic("implement me")
}

func (p proxyRepository) Delete(ctx context.Context, id int) error {
	proxy, err := p.queries.FindProxyByID(ctx, int32(id))
	if err != nil {
		return ErrProxyNotFound
	}
	return p.queries.DeleteProxy(ctx, int32(proxy.ID))
}

func (p proxyRepository) DeleteMany(ctx context.Context, ids []int) error {
	panic("implement me")
}

func (p proxyRepository) FindByID(ctx context.Context, id int) (*entity.Proxy, error) {
	proxy, err := p.queries.FindProxyByID(ctx, int32(id))
	if err != nil {
		return nil, ErrProxyNotFound
	}
	d := FromSQLCModel(*proxy)
	return d, nil
}

func (r *proxyRepository) FindByIDs(ctx context.Context, ids []int) ([]*entity.Proxy, error) {
	if len(ids) == 0 {
		return []*entity.Proxy{}, nil
	}

	ids32 := make([]int32, len(ids))
	for i, id := range ids {
		ids32[i] = int32(id)
	}

	sqlcProxies, err := r.queries.FindProxiesByIDs(ctx, ids32)
	if err != nil {
		return nil, err
	}

	return FromSQLCModels(sqlcProxies), nil
}
