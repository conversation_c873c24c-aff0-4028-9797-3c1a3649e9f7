// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateMany implements pgx.CopyFromSource.
type iteratorForCreateMany struct {
	rows                 []CreateManyParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateMany) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateMany) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].Name,
		r.rows[0].DisplayName,
		r.rows[0].MarketType,
		r.rows[0].Status,
		r.rows[0].BaseUrl,
		r.rows[0].Currency,
		r.rows[0].BuyerFeePercent,
		r.rows[0].<PERSON><PERSON><PERSON><PERSON><PERSON>er<PERSON>,
		r.rows[0].CountryCode,
		r.rows[0].Language,
		r.rows[0].Description,
		r.rows[0].IsActive,
		r.rows[0].LastCrawlAt,
	}, nil
}

func (r iteratorForCreateMany) Err() error {
	return nil
}

func (q *Queries) CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"markets"}, []string{"name", "display_name", "market_type", "status", "base_url", "currency", "buyer_fee_percent", "seller_fee_percent", "country_code", "language", "description", "is_active", "last_crawl_at"}, &iteratorForCreateMany{rows: arg})
}
