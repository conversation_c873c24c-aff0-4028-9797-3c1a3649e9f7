package repository

import (
	"context"
	"errors"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/authcrawl/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"

	"go_core_market/pkg/database"
)

var (
	ErrAuthNotFound = errors.New("auth not found")
)

type authRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewAuthRepository creates a new auth repository
func NewAuthRepository(db database.DBTX) repository.RepositoryAuthCrawl {
	return &authRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

func (r *authRepository) Create(ctx context.Context, auth *entity.Auth) error {
	// Convert domain.Auth to sqlc.CreateAuthParams
	params := ToCreateAuthParams(auth)

	// Execute query
	_, err := r.queries.CreateAuth(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to save auth: %w", err)
	}
	return nil
}
func (r *authRepository) CreateMany(ctx context.Context, auths []*entity.Auth) error {
	params := ToCreateAuthBatchParams(auths)

	// Execute batch query
	_, err := r.queries.CreateMany(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to save auths: %w", err)
	}
	return nil
}

func (r *authRepository) Update(ctx context.Context, id int, updateFn func(p *entity.Auth) (*entity.Auth, error)) error {
	authSql, err := r.queries.FindAuthByID(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to find auth by id: %w", err)
	}
	auth := FromSQLCModel(*authSql)
	auth, err = updateFn(auth)
	if err != nil {
		return fmt.Errorf("failed to update auth: %w", err)
	}
	_, err = r.queries.UpdateAuth(ctx, ToUpdateAuthParams(auth))
	if err != nil {
		return fmt.Errorf("failed to save auth: %w", err)
	}
	return nil
}

func (r *authRepository) UpdateMany(ctx context.Context, ids []int, updateFn func(p []*entity.Auth) ([]*entity.Auth, error)) error {

	panic("implement me")
}

func (r *authRepository) DeleteMany(ctx context.Context, ids []int) error {
	//TODO implement me
	panic("implement me")
}
func (r *authRepository) Delete(ctx context.Context, id int) error {
	return r.queries.DeleteAuth(ctx, id)
}
