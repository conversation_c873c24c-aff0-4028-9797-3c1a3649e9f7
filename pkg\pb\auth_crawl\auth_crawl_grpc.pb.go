// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: api/protobuf/auth_crawl.proto

package auth_crawl

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AuthCrawlService_CreateAuth_FullMethodName                    = "/auth_crawl.v1.AuthCrawlService/CreateAuth"
	AuthCrawlService_CreateAuthBatch_FullMethodName               = "/auth_crawl.v1.AuthCrawlService/CreateAuthBatch"
	AuthCrawlService_UpdateAuth_FullMethodName                    = "/auth_crawl.v1.AuthCrawlService/UpdateAuth"
	AuthCrawlService_DeleteAuth_FullMethodName                    = "/auth_crawl.v1.AuthCrawlService/DeleteAuth"
	AuthCrawlService_ActivateAuth_FullMethodName                  = "/auth_crawl.v1.AuthCrawlService/ActivateAuth"
	AuthCrawlService_DeactivateAuth_FullMethodName                = "/auth_crawl.v1.AuthCrawlService/DeactivateAuth"
	AuthCrawlService_MarkAuthUsed_FullMethodName                  = "/auth_crawl.v1.AuthCrawlService/MarkAuthUsed"
	AuthCrawlService_MarkAuthsUsed_FullMethodName                 = "/auth_crawl.v1.AuthCrawlService/MarkAuthsUsed"
	AuthCrawlService_ReleaseAuth_FullMethodName                   = "/auth_crawl.v1.AuthCrawlService/ReleaseAuth"
	AuthCrawlService_ReleaseManyAuth_FullMethodName               = "/auth_crawl.v1.AuthCrawlService/ReleaseManyAuth"
	AuthCrawlService_GetAllAuthCrawl_FullMethodName               = "/auth_crawl.v1.AuthCrawlService/GetAllAuthCrawl"
	AuthCrawlService_GetAuthCrawlById_FullMethodName              = "/auth_crawl.v1.AuthCrawlService/GetAuthCrawlById"
	AuthCrawlService_GetAuthCrawlByMarketId_FullMethodName        = "/auth_crawl.v1.AuthCrawlService/GetAuthCrawlByMarketId"
	AuthCrawlService_GetAvailableAuthCrawlByMarket_FullMethodName = "/auth_crawl.v1.AuthCrawlService/GetAvailableAuthCrawlByMarket"
)

// AuthCrawlServiceClient is the client API for AuthCrawlService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC Service
type AuthCrawlServiceClient interface {
	// Commands
	CreateAuth(ctx context.Context, in *CreateAuthRequest, opts ...grpc.CallOption) (*CreateAuthResponse, error)
	CreateAuthBatch(ctx context.Context, in *CreateAuthBatchRequest, opts ...grpc.CallOption) (*CreateAuthBatchResponse, error)
	UpdateAuth(ctx context.Context, in *UpdateAuthRequest, opts ...grpc.CallOption) (*UpdateAuthResponse, error)
	DeleteAuth(ctx context.Context, in *DeleteAuthRequest, opts ...grpc.CallOption) (*DeleteAuthResponse, error)
	ActivateAuth(ctx context.Context, in *ActivateAuthRequest, opts ...grpc.CallOption) (*ActivateAuthResponse, error)
	DeactivateAuth(ctx context.Context, in *DeactivateAuthRequest, opts ...grpc.CallOption) (*DeactivateAuthResponse, error)
	MarkAuthUsed(ctx context.Context, in *MarkAuthUsedRequest, opts ...grpc.CallOption) (*MarkAuthUsedResponse, error)
	MarkAuthsUsed(ctx context.Context, in *MarkAuthsUsedRequest, opts ...grpc.CallOption) (*MarkAuthsUsedResponse, error)
	ReleaseAuth(ctx context.Context, in *ReleaseAuthRequest, opts ...grpc.CallOption) (*ReleaseAuthResponse, error)
	ReleaseManyAuth(ctx context.Context, in *ReleaseManyAuthRequest, opts ...grpc.CallOption) (*ReleaseManyAuthResponse, error)
	// Queries
	GetAllAuthCrawl(ctx context.Context, in *GetAllAuthCrawlRequest, opts ...grpc.CallOption) (*GetAllAuthCrawlResponse, error)
	GetAuthCrawlById(ctx context.Context, in *GetAuthCrawlByIdRequest, opts ...grpc.CallOption) (*GetAuthCrawlByIdResponse, error)
	GetAuthCrawlByMarketId(ctx context.Context, in *GetAuthCrawlByMarketIdRequest, opts ...grpc.CallOption) (*GetAuthCrawlByMarketIdResponse, error)
	GetAvailableAuthCrawlByMarket(ctx context.Context, in *GetAvailableAuthCrawlByMarketRequest, opts ...grpc.CallOption) (*GetAvailableAuthCrawlByMarketResponse, error)
}

type authCrawlServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthCrawlServiceClient(cc grpc.ClientConnInterface) AuthCrawlServiceClient {
	return &authCrawlServiceClient{cc}
}

func (c *authCrawlServiceClient) CreateAuth(ctx context.Context, in *CreateAuthRequest, opts ...grpc.CallOption) (*CreateAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_CreateAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) CreateAuthBatch(ctx context.Context, in *CreateAuthBatchRequest, opts ...grpc.CallOption) (*CreateAuthBatchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAuthBatchResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_CreateAuthBatch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) UpdateAuth(ctx context.Context, in *UpdateAuthRequest, opts ...grpc.CallOption) (*UpdateAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_UpdateAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) DeleteAuth(ctx context.Context, in *DeleteAuthRequest, opts ...grpc.CallOption) (*DeleteAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_DeleteAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) ActivateAuth(ctx context.Context, in *ActivateAuthRequest, opts ...grpc.CallOption) (*ActivateAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ActivateAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_ActivateAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) DeactivateAuth(ctx context.Context, in *DeactivateAuthRequest, opts ...grpc.CallOption) (*DeactivateAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeactivateAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_DeactivateAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) MarkAuthUsed(ctx context.Context, in *MarkAuthUsedRequest, opts ...grpc.CallOption) (*MarkAuthUsedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkAuthUsedResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_MarkAuthUsed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) MarkAuthsUsed(ctx context.Context, in *MarkAuthsUsedRequest, opts ...grpc.CallOption) (*MarkAuthsUsedResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MarkAuthsUsedResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_MarkAuthsUsed_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) ReleaseAuth(ctx context.Context, in *ReleaseAuthRequest, opts ...grpc.CallOption) (*ReleaseAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_ReleaseAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) ReleaseManyAuth(ctx context.Context, in *ReleaseManyAuthRequest, opts ...grpc.CallOption) (*ReleaseManyAuthResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ReleaseManyAuthResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_ReleaseManyAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) GetAllAuthCrawl(ctx context.Context, in *GetAllAuthCrawlRequest, opts ...grpc.CallOption) (*GetAllAuthCrawlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllAuthCrawlResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_GetAllAuthCrawl_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) GetAuthCrawlById(ctx context.Context, in *GetAuthCrawlByIdRequest, opts ...grpc.CallOption) (*GetAuthCrawlByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthCrawlByIdResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_GetAuthCrawlById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) GetAuthCrawlByMarketId(ctx context.Context, in *GetAuthCrawlByMarketIdRequest, opts ...grpc.CallOption) (*GetAuthCrawlByMarketIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAuthCrawlByMarketIdResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_GetAuthCrawlByMarketId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authCrawlServiceClient) GetAvailableAuthCrawlByMarket(ctx context.Context, in *GetAvailableAuthCrawlByMarketRequest, opts ...grpc.CallOption) (*GetAvailableAuthCrawlByMarketResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAvailableAuthCrawlByMarketResponse)
	err := c.cc.Invoke(ctx, AuthCrawlService_GetAvailableAuthCrawlByMarket_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthCrawlServiceServer is the server API for AuthCrawlService service.
// All implementations must embed UnimplementedAuthCrawlServiceServer
// for forward compatibility.
//
// gRPC Service
type AuthCrawlServiceServer interface {
	// Commands
	CreateAuth(context.Context, *CreateAuthRequest) (*CreateAuthResponse, error)
	CreateAuthBatch(context.Context, *CreateAuthBatchRequest) (*CreateAuthBatchResponse, error)
	UpdateAuth(context.Context, *UpdateAuthRequest) (*UpdateAuthResponse, error)
	DeleteAuth(context.Context, *DeleteAuthRequest) (*DeleteAuthResponse, error)
	ActivateAuth(context.Context, *ActivateAuthRequest) (*ActivateAuthResponse, error)
	DeactivateAuth(context.Context, *DeactivateAuthRequest) (*DeactivateAuthResponse, error)
	MarkAuthUsed(context.Context, *MarkAuthUsedRequest) (*MarkAuthUsedResponse, error)
	MarkAuthsUsed(context.Context, *MarkAuthsUsedRequest) (*MarkAuthsUsedResponse, error)
	ReleaseAuth(context.Context, *ReleaseAuthRequest) (*ReleaseAuthResponse, error)
	ReleaseManyAuth(context.Context, *ReleaseManyAuthRequest) (*ReleaseManyAuthResponse, error)
	// Queries
	GetAllAuthCrawl(context.Context, *GetAllAuthCrawlRequest) (*GetAllAuthCrawlResponse, error)
	GetAuthCrawlById(context.Context, *GetAuthCrawlByIdRequest) (*GetAuthCrawlByIdResponse, error)
	GetAuthCrawlByMarketId(context.Context, *GetAuthCrawlByMarketIdRequest) (*GetAuthCrawlByMarketIdResponse, error)
	GetAvailableAuthCrawlByMarket(context.Context, *GetAvailableAuthCrawlByMarketRequest) (*GetAvailableAuthCrawlByMarketResponse, error)
	mustEmbedUnimplementedAuthCrawlServiceServer()
}

// UnimplementedAuthCrawlServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthCrawlServiceServer struct{}

func (UnimplementedAuthCrawlServiceServer) CreateAuth(context.Context, *CreateAuthRequest) (*CreateAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) CreateAuthBatch(context.Context, *CreateAuthBatchRequest) (*CreateAuthBatchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAuthBatch not implemented")
}
func (UnimplementedAuthCrawlServiceServer) UpdateAuth(context.Context, *UpdateAuthRequest) (*UpdateAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) DeleteAuth(context.Context, *DeleteAuthRequest) (*DeleteAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) ActivateAuth(context.Context, *ActivateAuthRequest) (*ActivateAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ActivateAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) DeactivateAuth(context.Context, *DeactivateAuthRequest) (*DeactivateAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeactivateAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) MarkAuthUsed(context.Context, *MarkAuthUsedRequest) (*MarkAuthUsedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkAuthUsed not implemented")
}
func (UnimplementedAuthCrawlServiceServer) MarkAuthsUsed(context.Context, *MarkAuthsUsedRequest) (*MarkAuthsUsedResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MarkAuthsUsed not implemented")
}
func (UnimplementedAuthCrawlServiceServer) ReleaseAuth(context.Context, *ReleaseAuthRequest) (*ReleaseAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) ReleaseManyAuth(context.Context, *ReleaseManyAuthRequest) (*ReleaseManyAuthResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseManyAuth not implemented")
}
func (UnimplementedAuthCrawlServiceServer) GetAllAuthCrawl(context.Context, *GetAllAuthCrawlRequest) (*GetAllAuthCrawlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllAuthCrawl not implemented")
}
func (UnimplementedAuthCrawlServiceServer) GetAuthCrawlById(context.Context, *GetAuthCrawlByIdRequest) (*GetAuthCrawlByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthCrawlById not implemented")
}
func (UnimplementedAuthCrawlServiceServer) GetAuthCrawlByMarketId(context.Context, *GetAuthCrawlByMarketIdRequest) (*GetAuthCrawlByMarketIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAuthCrawlByMarketId not implemented")
}
func (UnimplementedAuthCrawlServiceServer) GetAvailableAuthCrawlByMarket(context.Context, *GetAvailableAuthCrawlByMarketRequest) (*GetAvailableAuthCrawlByMarketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAvailableAuthCrawlByMarket not implemented")
}
func (UnimplementedAuthCrawlServiceServer) mustEmbedUnimplementedAuthCrawlServiceServer() {}
func (UnimplementedAuthCrawlServiceServer) testEmbeddedByValue()                          {}

// UnsafeAuthCrawlServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthCrawlServiceServer will
// result in compilation errors.
type UnsafeAuthCrawlServiceServer interface {
	mustEmbedUnimplementedAuthCrawlServiceServer()
}

func RegisterAuthCrawlServiceServer(s grpc.ServiceRegistrar, srv AuthCrawlServiceServer) {
	// If the following call pancis, it indicates UnimplementedAuthCrawlServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuthCrawlService_ServiceDesc, srv)
}

func _AuthCrawlService_CreateAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).CreateAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_CreateAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).CreateAuth(ctx, req.(*CreateAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_CreateAuthBatch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAuthBatchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).CreateAuthBatch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_CreateAuthBatch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).CreateAuthBatch(ctx, req.(*CreateAuthBatchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_UpdateAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).UpdateAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_UpdateAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).UpdateAuth(ctx, req.(*UpdateAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_DeleteAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).DeleteAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_DeleteAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).DeleteAuth(ctx, req.(*DeleteAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_ActivateAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ActivateAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).ActivateAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_ActivateAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).ActivateAuth(ctx, req.(*ActivateAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_DeactivateAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeactivateAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).DeactivateAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_DeactivateAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).DeactivateAuth(ctx, req.(*DeactivateAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_MarkAuthUsed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAuthUsedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).MarkAuthUsed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_MarkAuthUsed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).MarkAuthUsed(ctx, req.(*MarkAuthUsedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_MarkAuthsUsed_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MarkAuthsUsedRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).MarkAuthsUsed(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_MarkAuthsUsed_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).MarkAuthsUsed(ctx, req.(*MarkAuthsUsedRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_ReleaseAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).ReleaseAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_ReleaseAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).ReleaseAuth(ctx, req.(*ReleaseAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_ReleaseManyAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseManyAuthRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).ReleaseManyAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_ReleaseManyAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).ReleaseManyAuth(ctx, req.(*ReleaseManyAuthRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_GetAllAuthCrawl_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllAuthCrawlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).GetAllAuthCrawl(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_GetAllAuthCrawl_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).GetAllAuthCrawl(ctx, req.(*GetAllAuthCrawlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_GetAuthCrawlById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthCrawlByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).GetAuthCrawlById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_GetAuthCrawlById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).GetAuthCrawlById(ctx, req.(*GetAuthCrawlByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_GetAuthCrawlByMarketId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAuthCrawlByMarketIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).GetAuthCrawlByMarketId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_GetAuthCrawlByMarketId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).GetAuthCrawlByMarketId(ctx, req.(*GetAuthCrawlByMarketIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthCrawlService_GetAvailableAuthCrawlByMarket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAvailableAuthCrawlByMarketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthCrawlServiceServer).GetAvailableAuthCrawlByMarket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthCrawlService_GetAvailableAuthCrawlByMarket_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthCrawlServiceServer).GetAvailableAuthCrawlByMarket(ctx, req.(*GetAvailableAuthCrawlByMarketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthCrawlService_ServiceDesc is the grpc.ServiceDesc for AuthCrawlService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthCrawlService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "auth_crawl.v1.AuthCrawlService",
	HandlerType: (*AuthCrawlServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateAuth",
			Handler:    _AuthCrawlService_CreateAuth_Handler,
		},
		{
			MethodName: "CreateAuthBatch",
			Handler:    _AuthCrawlService_CreateAuthBatch_Handler,
		},
		{
			MethodName: "UpdateAuth",
			Handler:    _AuthCrawlService_UpdateAuth_Handler,
		},
		{
			MethodName: "DeleteAuth",
			Handler:    _AuthCrawlService_DeleteAuth_Handler,
		},
		{
			MethodName: "ActivateAuth",
			Handler:    _AuthCrawlService_ActivateAuth_Handler,
		},
		{
			MethodName: "DeactivateAuth",
			Handler:    _AuthCrawlService_DeactivateAuth_Handler,
		},
		{
			MethodName: "MarkAuthUsed",
			Handler:    _AuthCrawlService_MarkAuthUsed_Handler,
		},
		{
			MethodName: "MarkAuthsUsed",
			Handler:    _AuthCrawlService_MarkAuthsUsed_Handler,
		},
		{
			MethodName: "ReleaseAuth",
			Handler:    _AuthCrawlService_ReleaseAuth_Handler,
		},
		{
			MethodName: "ReleaseManyAuth",
			Handler:    _AuthCrawlService_ReleaseManyAuth_Handler,
		},
		{
			MethodName: "GetAllAuthCrawl",
			Handler:    _AuthCrawlService_GetAllAuthCrawl_Handler,
		},
		{
			MethodName: "GetAuthCrawlById",
			Handler:    _AuthCrawlService_GetAuthCrawlById_Handler,
		},
		{
			MethodName: "GetAuthCrawlByMarketId",
			Handler:    _AuthCrawlService_GetAuthCrawlByMarketId_Handler,
		},
		{
			MethodName: "GetAvailableAuthCrawlByMarket",
			Handler:    _AuthCrawlService_GetAvailableAuthCrawlByMarket_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/protobuf/auth_crawl.proto",
}
