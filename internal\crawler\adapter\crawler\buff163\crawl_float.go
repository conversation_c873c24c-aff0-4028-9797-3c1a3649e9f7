package buff163

import (
	"context"
	"encoding/json"
	"fmt"
	"go_core_market/internal/crawler/domain/value_object"
	"strconv"
	"time"
)

type FloatItemPrice struct {
	FloatRange FloatRangeItemPrice
	Price      string  `json:"price"`
	Num        int     `json:"num"`
	Float      float64 `json:"float"`
}

type FloatRangeItemPrice struct {
	Min float64 `json:"min"`
	Max float64 `json:"max"`
}

func (c *Buff163Crawler) CrawlFloat(
	ctx context.Context,
	id int,
	name string,
	floatConds []value_object.ConditionFloat,
) ([]*value_object.CrawledPrice, error) {
	if c.config == nil {
		return nil, fmt.Errorf("crawler not configured")
	}

	var (
		allItemSells []FloatItemPrice
		page         = 1
		total        = 1
	)

	for page <= total {
		pageItems, nextTotal, err := c.fetchSellFloatPage(ctx, id, page)
		if err != nil {
			return nil, fmt.E<PERSON><PERSON>("failed to fetch sell float page %d: %w", page, err)
		}
		allItemSells = append(allItemSells, pageItems...)
		total = nextTotal
		page++
	}

	allItemBuys, err := c.fetchOrderItemFloat(ctx, id)
	if err != nil {
		return nil, err
	}

	var results []*value_object.CrawledPrice
	now := time.Now()

	for _, cond := range floatConds {
		// Giá thấp nhất cho bán (linh hoạt theo vùng float thấp hơn nhưng giá rẻ hơn)
		minSellPrice := -1.0
		numSell := 0
		for _, item := range allItemSells {
			itemPrice, err := strconv.ParseFloat(item.Price, 64)
			if err != nil {
				continue
			}

			// Nếu float nằm trong vùng hiện tại, hoặc float nhỏ hơn vùng hiện tại nhưng giá tốt hơn min hiện tại
			if (item.Float >= cond.FloatMin && item.Float < cond.FloatMax) ||
				(item.Float < cond.FloatMin && (minSellPrice == -1.0 || itemPrice < minSellPrice)) {

				if minSellPrice == -1.0 || itemPrice < minSellPrice {
					minSellPrice = itemPrice
				}
				numSell += item.Num
			}
		}

		// Giá cao nhất cho mua (nếu vùng float giao nhau với điều kiện)
		maxBuyPrice := 0.0
		numBuy := 0
		for _, item := range allItemBuys {
			if item.FloatRange.Min <= cond.FloatMin && item.FloatRange.Max >= cond.FloatMax {
				price, err := strconv.ParseFloat(item.Price, 64)
				if err != nil {
					continue
				}
				if price > maxBuyPrice {
					maxBuyPrice = price
				}
				numBuy += item.Num
			}
		}

		results = append(results, &value_object.CrawledPrice{
			ItemName:   name,
			MarketID:   id,
			SellPrice:  minSellPrice,
			BuyPrice:   maxBuyPrice,
			NumSell:    numSell,
			NumBuy:     numBuy,
			Condition:  &cond,
			RecordedAt: now,
		})
	}

	return results, nil
}

func (c *Buff163Crawler) fetchOrderItemFloat(ctx context.Context, id int) ([]FloatItemPrice, error) {
	params := map[string]string{
		"goods_id":  strconv.Itoa(id),
		"page_num":  strconv.Itoa(1),
		"page_size": "100",
		"game":      "csgo",
	}
	data, err := c.makeRequest(ctx, "/api/market/goods/buy_order", params)

	if err != nil {
		return nil, err
	}

	var response BuffItemResponse[OrderItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, err
	}

	if response.Data == nil {
		return nil, fmt.Errorf("no data in response")
	}

	var items []FloatItemPrice
	for _, item := range response.Data.Items {
		if len(item.Specific) != 1 {
			continue
		}
		spec := item.Specific[0]
		if spec.Type != "paintwear" {
			continue
		}
		if len(spec.Values) != 2 {
			continue
		}
		minStr, ok1 := spec.Values[0].(string)
		maxStr, ok2 := spec.Values[1].(string)
		if !ok1 || !ok2 {
			continue
		}
		minFloat, err1 := strconv.ParseFloat(minStr, 64)
		maxFloat, err2 := strconv.ParseFloat(maxStr, 64)
		if err1 != nil || err2 != nil {
			continue
		}
		items = append(items, FloatItemPrice{
			FloatRange: FloatRangeItemPrice{
				Min: minFloat,
				Max: maxFloat,
			},
			Price: item.Price,
			Num:   item.Num,
		})
	}
	return items, nil

}

func (c *Buff163Crawler) fetchSellFloatPage(ctx context.Context, id, page int) ([]FloatItemPrice, int, error) {
	params := map[string]string{
		"goods_id":                strconv.Itoa(id),
		"page_num":                strconv.Itoa(page),
		"page_size":               "100",
		"game":                    "csgo",
		"appid":                   "730",
		"sort_by":                 "price.asc",
		"allow_tradable_cooldown": "1",
	}

	data, err := c.makeRequest(ctx, "/api/market/goods/sell_order", params)
	if err != nil {
		return nil, 0, err
	}

	var response BuffItemResponse[SellItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, err
	}
	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, 0, err
	}

	if response.Data == nil {
		return nil, 0, fmt.Errorf("no data in response")
	}

	var items []FloatItemPrice
	for _, item := range response.Data.Items {
		paintWear, err := strconv.ParseFloat(item.AssetInfo.PaintWear, 64)
		if err != nil {
			continue
		}
		items = append(items, FloatItemPrice{
			Float: paintWear,
			Price: item.Price,
			Num:   1,
		})
	}

	return items, response.Data.TotalPage, nil
}
