package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeleteProxy struct {
	ID        int
	DeletedBy string
}

type DeleteProxy<PERSON>andler decorator.CommandHandler[DeleteProxy]

type deleteProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewDeleteProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeleteProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeleteProxy](
		deleteProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deleteProxyHandler) Handle(ctx context.Context, cmd DeleteProxy) error {
	return h.repo.Delete(ctx, cmd.ID)
}
