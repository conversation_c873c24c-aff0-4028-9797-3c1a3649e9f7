package serializer

import (
	"encoding/json"
	"fmt"
	"go_core_market/pkg/message"
	"reflect"
	"strings"
	"time"

	"go_core_market/pkg/message/event"
)

// TypedSerializer handles serialization with event registry
type TypedSerializer struct {
	registry *EventRegistry
}

type EventRegistry struct {
	eventTypes map[string]reflect.Type
}

func NewEventRegistry() *EventRegistry {
	return &EventRegistry{
		eventTypes: make(map[string]reflect.Type),
	}
}

func (r *EventRegistry) RegisterEvent(eventType string, eventInstance event.Event) {
	r.eventTypes[eventType] = reflect.TypeOf(eventInstance).Elem()
}

func (r *EventRegistry) CreateEvent(eventType string) (event.Event, error) {
	eventTypeRef, exists := r.eventTypes[eventType]
	if !exists {
		return nil, fmt.Errorf("unknown event type: %s", eventType)
	}

	eventValue := reflect.New(eventTypeRef)
	evt, ok := eventValue.Interface().(event.Event)
	if !ok {
		return nil, fmt.Errorf("event type %s does not implement Event interface", eventType)
	}

	return evt, nil
}

func (r *EventRegistry) DeserializeEvent(eventType string, data []byte) (event.Event, error) {
	evt, err := r.CreateEvent(eventType)
	if err != nil {
		return nil, err
	}

	if err := json.Unmarshal(data, evt); err != nil {
		return nil, fmt.Errorf("failed to unmarshal event data: %w", err)
	}

	return evt, nil
}

func NewTypedSerializer(registry *EventRegistry) *TypedSerializer {
	return &TypedSerializer{
		registry: registry,
	}
}

func (s *TypedSerializer) Serialize(evt event.Event) (*message.Message, error) {
	// Serialize event data
	data, err := json.Marshal(evt)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal event: %w", err)
	}
	topic, _, found := strings.Cut(evt.GetType(), ".")
	if !found {
		return nil, fmt.Errorf("failed to find topic")
	}
	// Create message with headers
	headers := map[string]string{
		"event_id":      evt.GetID(),
		"event_type":    evt.GetType(),
		"event_version": evt.GetEventVersion(),
		"aggregate_id":  evt.GetAggregateID(),
		"timestamp":     evt.GetTimestamp().Format(time.RFC3339),
		"content_type":  "application/json",
		"serializer":    "typed",
	}

	return &message.Message{
		Key:         []byte(evt.GetAggregateID()),
		Value:       data,
		ContentType: "application/json",
		Headers:     headers,
		Topic:       topic,
	}, nil
}

func (s *TypedSerializer) Deserialize(msg *message.Message) (event.Event, error) {
	if msg.ContentType != "application/json" {
		return nil, fmt.Errorf("unsupported content type: %s", msg.ContentType)
	}

	// Get event type from headers
	eventType, exists := msg.Headers["event_type"]
	if !exists {
		return nil, fmt.Errorf("event_type header is missing")
	}

	// Deserialize using registry
	evt, err := s.registry.DeserializeEvent(eventType, msg.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to deserialize event: %w", err)
	}

	return evt, nil
}
