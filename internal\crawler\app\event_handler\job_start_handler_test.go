package eventhandler

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"go_core_market/internal/crawler/app/service"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message/event"
)

// Mock implementations
type MockCrawlService struct {
	mock.Mock
}

func (m *MockCrawlService) CrawlPriceNormal(ctx context.Context, config *querycrawlconfig.CrawlConfig) error {
	args := m.Called(ctx, config)
	return args.Error(0)
}

type MockCrawlConfigQueries struct {
	mock.Mock
}

func (m *MockCrawlConfigQueries) GetConfigID(ctx context.Context, query querycrawlconfig.GetConfigIDQuery) (*querycrawlconfig.CrawlConfig, error) {
	args := m.Called(ctx, query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*querycrawlconfig.CrawlConfig), args.Error(1)
}

type MockJobQueries struct {
	mock.Mock
}

func (m *MockJobQueries) GetJobByID(ctx context.Context, query queryjob.GetJobByIDQuery) (*queryjob.Job, error) {
	args := m.Called(ctx, query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*queryjob.Job), args.Error(1)
}

type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Debug(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Info(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Fatal(msg string, fields ...interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Sync() error {
	args := m.Called()
	return args.Error(0)
}

func TestJobStartEventHandler_Handle_NonJobStartedEvent(t *testing.T) {
	// Arrange
	mockCrawlService := &MockCrawlService{}
	mockCrawlConfigQueries := &MockCrawlConfigQueries{}
	mockJobQueries := &MockJobQueries{}
	mockLogger := &MockLogger{}

	handler := NewJobStartEventHandler(
		mockCrawlService,
		&querycrawlconfig.CrawlConfigQueries{GetConfigID: mockCrawlConfigQueries},
		&queryjob.JobQueries{GetJobByID: mockJobQueries},
		mockLogger,
	)

	// Create a non-job-started event
	evt := event.NewJobCreatedEvent("job-1", event.JobCreatedData{
		JobID:   1,
		JobType: "crawl",
		Name:    "Test Job",
	})

	mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()

	// Act
	err := handler.Handle(context.Background(), evt)

	// Assert
	assert.NoError(t, err)
	mockCrawlService.AssertNotCalled(t, "CrawlPriceNormal")
}

func TestJobStartEventHandler_Handle_NonCrawlJob(t *testing.T) {
	// Arrange
	mockCrawlService := &MockCrawlService{}
	mockCrawlConfigQueries := &MockCrawlConfigQueries{}
	mockJobQueries := &MockJobQueries{}
	mockLogger := &MockLogger{}

	handler := NewJobStartEventHandler(
		mockCrawlService,
		&querycrawlconfig.CrawlConfigQueries{GetConfigID: mockCrawlConfigQueries},
		&queryjob.JobQueries{GetJobByID: mockJobQueries},
		mockLogger,
	)

	// Create a job started event for non-crawl job
	evt := event.NewJobStartedEvent("job-1", event.JobStartedData{
		JobID:     1,
		JobType:   "analysis",
		Name:      "Analysis Job",
		StartedAt: time.Now(),
	})

	mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()

	// Act
	err := handler.Handle(context.Background(), evt)

	// Assert
	assert.NoError(t, err)
	mockCrawlService.AssertNotCalled(t, "CrawlPriceNormal")
}

func TestJobStartEventHandler_Handle_CrawlJobWithNormalConfig_Success(t *testing.T) {
	// Arrange
	mockCrawlService := &MockCrawlService{}
	mockCrawlConfigQueries := &MockCrawlConfigQueries{}
	mockJobQueries := &MockJobQueries{}
	mockLogger := &MockLogger{}

	handler := NewJobStartEventHandler(
		mockCrawlService,
		&querycrawlconfig.CrawlConfigQueries{GetConfigID: mockCrawlConfigQueries},
		&queryjob.JobQueries{GetJobByID: mockJobQueries},
		mockLogger,
	)

	// Create a job started event for crawl job
	evt := event.NewJobStartedEvent("job-1", event.JobStartedData{
		JobID:     1,
		JobType:   string(entity.JobTypeCrawl),
		Name:      "Crawl Job",
		StartedAt: time.Now(),
	})

	// Mock job with crawl config ID
	configID := 123
	job := &queryjob.Job{
		Id:            1,
		JobType:       string(entity.JobTypeCrawl),
		Name:          "Crawl Job",
		CrawlConfigId: &configID,
	}

	// Mock crawl config with normal type
	config := &querycrawlconfig.CrawlConfig{
		Id:         123,
		TypeConfig: string(entity.TypeConfigNormal),
		MarketId:   1,
	}

	mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()

	mockJobQueries.On("GetJobByID", mock.Anything, queryjob.GetJobByIDQuery{ID: 1}).Return(job, nil)
	mockCrawlConfigQueries.On("GetConfigID", mock.Anything, querycrawlconfig.GetConfigIDQuery{ConfigID: 123}).Return(config, nil)
	mockCrawlService.On("CrawlPriceNormal", mock.Anything, config).Return(nil)

	// Act
	err := handler.Handle(context.Background(), evt)

	// Assert
	assert.NoError(t, err)
	mockJobQueries.AssertExpectations(t)
	mockCrawlConfigQueries.AssertExpectations(t)
	mockCrawlService.AssertExpectations(t)
}

func TestJobStartEventHandler_Handle_CrawlJobWithNonNormalConfig(t *testing.T) {
	// Arrange
	mockCrawlService := &MockCrawlService{}
	mockCrawlConfigQueries := &MockCrawlConfigQueries{}
	mockJobQueries := &MockJobQueries{}
	mockLogger := &MockLogger{}

	handler := NewJobStartEventHandler(
		mockCrawlService,
		&querycrawlconfig.CrawlConfigQueries{GetConfigID: mockCrawlConfigQueries},
		&queryjob.JobQueries{GetJobByID: mockJobQueries},
		mockLogger,
	)

	// Create a job started event for crawl job
	evt := event.NewJobStartedEvent("job-1", event.JobStartedData{
		JobID:     1,
		JobType:   string(entity.JobTypeCrawl),
		Name:      "Crawl Job",
		StartedAt: time.Now(),
	})

	// Mock job with crawl config ID
	configID := 123
	job := &queryjob.Job{
		Id:            1,
		JobType:       string(entity.JobTypeCrawl),
		Name:          "Crawl Job",
		CrawlConfigId: &configID,
	}

	// Mock crawl config with doppler type (not normal)
	config := &querycrawlconfig.CrawlConfig{
		Id:         123,
		TypeConfig: "doppler", // Not normal type
		MarketId:   1,
	}

	mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()

	mockJobQueries.On("GetJobByID", mock.Anything, queryjob.GetJobByIDQuery{ID: 1}).Return(job, nil)
	mockCrawlConfigQueries.On("GetConfigID", mock.Anything, querycrawlconfig.GetConfigIDQuery{ConfigID: 123}).Return(config, nil)

	// Act
	err := handler.Handle(context.Background(), evt)

	// Assert
	assert.NoError(t, err)
	mockJobQueries.AssertExpectations(t)
	mockCrawlConfigQueries.AssertExpectations(t)
	mockCrawlService.AssertNotCalled(t, "CrawlPriceNormal") // Should not be called for non-normal config
}

func TestJobStartEventHandler_Handle_CrawlJobWithoutConfigID(t *testing.T) {
	// Arrange
	mockCrawlService := &MockCrawlService{}
	mockCrawlConfigQueries := &MockCrawlConfigQueries{}
	mockJobQueries := &MockJobQueries{}
	mockLogger := &MockLogger{}

	handler := NewJobStartEventHandler(
		mockCrawlService,
		&querycrawlconfig.CrawlConfigQueries{GetConfigID: mockCrawlConfigQueries},
		&queryjob.JobQueries{GetJobByID: mockJobQueries},
		mockLogger,
	)

	// Create a job started event for crawl job
	evt := event.NewJobStartedEvent("job-1", event.JobStartedData{
		JobID:     1,
		JobType:   string(entity.JobTypeCrawl),
		Name:      "Crawl Job",
		StartedAt: time.Now(),
	})

	// Mock job without crawl config ID
	job := &queryjob.Job{
		Id:            1,
		JobType:       string(entity.JobTypeCrawl),
		Name:          "Crawl Job",
		CrawlConfigId: nil, // No config ID
	}

	mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	mockLogger.On("Debug", mock.AnythingOfType("string"), mock.Anything).Return()

	mockJobQueries.On("GetJobByID", mock.Anything, queryjob.GetJobByIDQuery{ID: 1}).Return(job, nil)

	// Act
	err := handler.Handle(context.Background(), evt)

	// Assert
	assert.NoError(t, err)
	mockJobQueries.AssertExpectations(t)
	mockCrawlConfigQueries.AssertNotCalled(t, "GetConfigID")
	mockCrawlService.AssertNotCalled(t, "CrawlPriceNormal")
}
