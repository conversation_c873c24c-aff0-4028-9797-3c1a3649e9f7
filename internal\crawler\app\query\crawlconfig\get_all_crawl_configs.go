package querycrawlconfig

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/pagination"
)

type GetAllCrawlConfigsQuery struct {
	Page     int
	PageSize int
	OrderBy  string
}

type GetAllCrawlConfigsQueryHandler decorator.QueryHandler[GetAllCrawlConfigsQuery, *pagination.PaginatedResponse[CrawlConfig]]

type getAllCrawlConfigsQueryHandler struct {
	repo CrawlConfigReadModel
}

func NewGetAllCrawlConfigsQueryHandler(
	repo CrawlConfigReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAllCrawlConfigsQueryHandler {

	return decorator.ApplyQueryDecorators[GetAllCrawlConfigsQuery, *pagination.PaginatedResponse[CrawlConfig]](
		getAllCrawlConfigsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAllCrawlConfigsQueryHandler) Handle(ctx context.Context, query GetAllCrawlConfigsQuery) (*pagination.PaginatedResponse[CrawlConfig], error) {
	p := pagination.NewPagination(query.Page, query.PageSize)
	total, err := h.repo.CountCrawlConfigs(ctx)
	if err != nil {
		return nil, err
	}
	p.SetTotal(total)
	
	configs, err := h.repo.GetAllCrawlConfigs(ctx, query)
	if err != nil {
		return nil, err
	}
	
	return pagination.CreateResponse[CrawlConfig](p, configs), nil
}
