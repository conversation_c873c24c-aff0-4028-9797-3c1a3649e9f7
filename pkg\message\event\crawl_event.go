package event

import (
	"time"
)

// ====== Event Types Constants ======
const (
	PricesCrawledEventType = "crawl.prices_crawled"
	ItemsCrawledEventType  = "crawl.items_crawled"
)

// ====== Event DTOs ======
type CrawledPriceDTO struct {
	ItemName   string       `json:"item_name"`
	MarketID   int          `json:"market_id"`
	SellPrice  float64      `json:"sell_price"`
	BuyPrice   float64      `json:"buy_price"`
	NumSell    int          `json:"num_sell"`
	NumBuy     int          `json:"num_buy"`
	Condition  ConditionDTO `json:"condition,omitempty"`
	RecordedAt time.Time    `json:"recorded_at"`
}

type CrawledItemDTO struct {
	Name            string   `json:"name"`
	ShortName       string   `json:"short_name"`
	IconURL         string   `json:"icon_url"`
	OriginalIconURL string   `json:"original_icon_url"`
	Description     string   `json:"description"`
	SteamMarketURL  string   `json:"steam_market_url"`
	Game            string   `json:"game"`
	Tags            []TagDTO `json:"tags"`
	MarketID        int      `json:"market_id"`
}

type TagDTO struct {
	InternalName  string `json:"internal_name"`
	LocalizedName string `json:"localized_name"`
	TagType       string `json:"tag_type"`
}

type ConditionDTO struct {
	Name     string  `json:"name"`
	Type     string  `json:"type"` // "doppler", "float", "fade"
	FloatMin float64 `json:"float_min,omitempty"`
	FloatMax float64 `json:"float_max,omitempty"`
	FadeMin  float64 `json:"fade_min,omitempty"`
	FadeMax  float64 `json:"fade_max,omitempty"`
}

// ====== Prices Crawled Event ======
type PricesCrawledData struct {
	MarketID     int                `json:"market_id"`
	MarketName   string             `json:"market_name,omitempty"`
	CrawlType    string             `json:"crawl_type"` // "normal", "doppler", "float", "fade"
	ItemID       int                `json:"item_id,omitempty"`
	ItemName     string             `json:"item_name,omitempty"`
	Prices       []CrawledPriceDTO  `json:"prices"`
	TotalPrices  int                `json:"total_prices"`
	CrawledAt    time.Time          `json:"crawled_at"`
	CrawledBy    string             `json:"crawled_by,omitempty"`    // Optional: service/process that crawled
	RequestID    string             `json:"request_id,omitempty"`    // Optional: trace request
	Page         int                `json:"page,omitempty"`          // Optional: page number for normal crawl
	Conditions   []ConditionDTO     `json:"conditions,omitempty"`    // Optional: conditions used for crawl
	Duration     time.Duration      `json:"duration,omitempty"`      // Optional: crawl duration
	Success      bool               `json:"success"`                 // Whether crawl was successful
	ErrorMessage string             `json:"error_message,omitempty"` // Optional: error message if failed
}

type PricesCrawledEvent struct {
	*BaseEvent[PricesCrawledData]
}

func NewPricesCrawledEvent(aggregateID string, data PricesCrawledData) *PricesCrawledEvent {
	return &PricesCrawledEvent{
		BaseEvent: NewBaseEvent(PricesCrawledEventType, aggregateID, data),
	}
}

// ====== Items Crawled Event ======
type ItemsCrawledData struct {
	MarketID     int               `json:"market_id"`
	MarketName   string            `json:"market_name,omitempty"`
	Items        []CrawledItemDTO  `json:"items"`
	TotalItems   int               `json:"total_items"`
	CrawledAt    time.Time         `json:"crawled_at"`
	CrawledBy    string            `json:"crawled_by,omitempty"`    // Optional: service/process that crawled
	RequestID    string            `json:"request_id,omitempty"`    // Optional: trace request
	Page         int               `json:"page,omitempty"`          // Optional: page number
	Duration     time.Duration     `json:"duration,omitempty"`      // Optional: crawl duration
	Success      bool              `json:"success"`                 // Whether crawl was successful
	ErrorMessage string            `json:"error_message,omitempty"` // Optional: error message if failed
}

type ItemsCrawledEvent struct {
	*BaseEvent[ItemsCrawledData]
}

func NewItemsCrawledEvent(aggregateID string, data ItemsCrawledData) *ItemsCrawledEvent {
	return &ItemsCrawledEvent{
		BaseEvent: NewBaseEvent(ItemsCrawledEventType, aggregateID, data),
	}
}

// ====== Helper Functions for Domain to DTO Conversion ======
// Note: Conversion functions are implemented in crawl_converter.go
// Use ConvertCrawledPricesToDTOs() and ConvertCrawledItemsToDTOs()
// in your application layer before creating events
