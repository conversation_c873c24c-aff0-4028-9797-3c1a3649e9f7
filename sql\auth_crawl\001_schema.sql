-- Create auth_crawl table for storing authentication credentials
CREATE TABLE IF NOT EXISTS auth_crawl (
id SERIAL PRIMARY KEY,
                                          market_id INTEGER NOT NULL,
                                          auth_type VARCHAR(50) NOT NULL CHECK (auth_type IN ('api_key', 'cookie')),
    value TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    is_use BOOLEAN NOT NULL DEFAULT false,
    last_used_at TIMESTAMPTZ,
    expired_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

