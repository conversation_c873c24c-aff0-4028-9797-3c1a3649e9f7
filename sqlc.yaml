version: "2"
sql:
  - name: "market"
    engine: "postgresql"
    queries: "sql/market/"
    schema: "sql/market/"
    gen:
      go:
        package: "sqlc"
        out: "internal/crawler/adapter/database/market/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_interface: true
        emit_pointers_for_null_types: true
        emit_prepared_queries: false
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "markets.id"
            go_type: "int"
          - column: "markets.buyer_fee_percent"
            go_type: "float64"
          - column: "markets.seller_fee_percent"
            go_type: "float64"
          - column: "markets.country_code"
            go_type:
              type: "string"

  - name: "configcrawl"
    engine: "postgresql"
    queries: "sql/configcrawl/"
    schema: "sql/configcrawl/"
    gen:
      go:
        package: "sqlc"
        out: "internal/crawler/adapter/database/configcrawl/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_interface: true
        emit_pointers_for_null_types: true
        emit_prepared_queries: false
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "crawl_configs.id"
            go_type: "int"
          - column: "crawl_configs.market_id"
            go_type: "int"
          - column: "crawl_configs.requests_per_minute"
            go_type: "int"
          - column: "crawl_configs.max_number_auth"
            go_type: "int"
          - column: "crawl_configs.max_number_proxy"
            go_type: "int"
          - column: "crawl_configs.per_request_delay_seconds"
            go_type: "int"
          - column: "crawl_configs.timeout_seconds"
            go_type: "int"
          - column: "crawl_configs.max_retries"
            go_type: "int"

  - name: "job"
    engine: "postgresql"
    queries: "sql/job/"
    schema: "sql/job/"
    gen:
      go:
        package: "sqlc"
        out: "internal/crawler/adapter/database/job/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_interface: true
        emit_pointers_for_null_types: true
        emit_prepared_queries: false
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "jobs.id"
            go_type: "int"
          - column: "jobs.max_retries"
            go_type: "int"
          - column: "jobs.retry_count"
            go_type: "int"
          - column: "jobs.timeout_seconds"
            go_type: "int"

  - name: "proxy"
    engine: "postgresql"
    queries: "sql/proxy/"
    schema: "sql/proxy/"
    gen:
      go:
        package: "sqlc"
        out: "internal/crawler/adapter/database/proxy/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_interface: true
        emit_pointers_for_null_types: true
        emit_prepared_queries: false
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_enum_valid_method: true
        emit_all_enum_values: true

  - name: "auth_crawl"
    engine: "postgresql"
    queries: "sql/auth_crawl/"
    schema: "sql/auth_crawl/"
    gen:
      go:
        package: "sqlc"
        out: "internal/crawler/adapter/database/authcrawl/sqlc"
        sql_package: "pgx/v5"
        emit_json_tags: true
        emit_db_tags: true
        emit_empty_slices: true
        emit_exact_table_names: false
        emit_interface: true
        emit_pointers_for_null_types: true
        emit_prepared_queries: false
        emit_exported_queries: true
        emit_result_struct_pointers: true
        emit_params_struct_pointers: false
        emit_methods_with_db_argument: false
        emit_enum_valid_method: true
        emit_all_enum_values: true
        overrides:
          - column: "auth_crawl.id"
            go_type: "int"
          - column: "auth_crawl.market_id"
            go_type: "int"


