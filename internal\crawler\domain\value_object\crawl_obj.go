package value_object

import "time"

type CrawledPrice struct {
	ItemName   string    `json:"item_name"`
	MarketID   int       `json:"market_id"`
	SellPrice  float64   `json:"sell_price"`
	BuyPrice   float64   `json:"buy_price"`
	NumSell    int       `json:"num_sell"`
	NumBuy     int       `json:"num_buy"`
	Condition  Condition `json:"condition"`
	RecordedAt time.Time `json:"recorded_at"`
}

type CrawledItem struct {
	Name            string `json:"name"`
	ShortName       string `json:"short_name"`
	IconURL         string `json:"icon_url"`
	OriginalIconURL string `json:"original_icon_url"`
	Description     string `json:"description"`
	SteamMarketURL  string `json:"steam_market_url"`
	Game            string `json:"game"`
	Tags            []Tag  `json:"tag"`
	MarketID        int    `json:"market_id"`
}

type Tag struct {
	InternalName  string
	LocalizedName string
	TagType       string
}

type Condition interface {
	GetCondition() string
	GetType() string
}

type ConditionDoppler struct {
	Name string
}

func (c *ConditionDoppler) GetCondition() string {
	return c.Name
}

func (c *ConditionDoppler) GetType() string {
	return "doppler"
}

type ConditionFloat struct {
	Name     string
	FloatMin float64
	FloatMax float64
}

func (c *ConditionFloat) GetCondition() string {
	return c.Name
}

func (c *ConditionFloat) GetType() string {
	return "float"
}

type ConditionFade struct {
	Name    string
	FadeMin float64
	FadeMax float64
}

func (c *ConditionFade) GetCondition() string {
	return c.Name
}
func (c *ConditionFade) GetType() string {
	return "fade"
}
