package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"

	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ActivateAuth struct {
	ID          int
	ActivatedBy string
}

type ActivateAuthHandler decorator.CommandHandler[ActivateAuth]

type activateAuthHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewActivateAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ActivateAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ActivateAuth](
		activateAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h activateAuthHandler) Handle(ctx context.Context, cmd ActivateAuth) error {
	return h.repo.Update(ctx, cmd.ID, func(a *entity.Auth) (*entity.Auth, error) {
		err := a.Activate()
		if err != nil {
			return nil, err
		}
		return a, nil
	})
}

type DeactivateAuth struct {
	ID            int
	DeactivatedBy string
}

type DeactivateAuthHandler decorator.CommandHandler[DeactivateAuth]

type deactivateAuthHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewDeactivateAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeactivateAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeactivateAuth](
		deactivateAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deactivateAuthHandler) Handle(ctx context.Context, cmd DeactivateAuth) error {
	return h.repo.Update(ctx, cmd.ID, func(a *entity.Auth) (*entity.Auth, error) {
		err := a.Deactivate()
		if err != nil {
			return nil, err
		}
		return a, nil
	})
}
