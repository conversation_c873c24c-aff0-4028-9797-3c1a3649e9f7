package entity

import (
	"errors"
	"time"
)

type JobType string

const (
	JobTypeCrawl    JobType = "crawl"
	JobTypeAnalysis JobType = "analysis"
	JobTypeCleanup  JobType = "cleanup"
	JobTypeExport   JobType = "export"
)

func (j JobType) IsValid() bool {
	switch j {
	case JobTypeCrawl, JobTypeAnalysis, JobTypeCleanup, JobTypeExport:
		return true
	default:
		return false
	}
}

type JobStatus string

const (
	JobStatusPending   JobStatus = "pending"
	JobStatusScheduled JobStatus = "scheduled"
	JobStatusRunning   JobStatus = "running"
	JobStatusPaused    JobStatus = "paused"
	JobStatusCompleted JobStatus = "completed"
	JobStatusFailed    JobStatus = "failed"
	JobStatusCancelled JobStatus = "cancelled"
)

type JobParams struct {
	Type          string
	Name          string
	Description   *string
	ScheduledAt   *time.Time // Khi nào job sẽ chạy
	MaxRetries    int
	Timeout       int
	CreatedBy     string
	CrawlConfigId *int
}

type RebuildJobParams struct {
	Id            int
	JobType       string
	Name          string
	Description   *string
	Status        string
	CrawlConfigId *int
	ScheduledAt   *time.Time
	StartedAt     *time.Time
	CompletedAt   *time.Time
	// Progress tracking
	Progress *JobProgress
	// Error handling
	MaxRetries int
	RetryCount int
	LastError  *string
	Timeout    int
	CreatedBy  string
}

func NewJob(params JobParams) (*Job, error) {
	if params.Type == "" {
		return nil, errors.New("job type cannot be empty")
	}
	typer := JobType(params.Type)
	if !typer.IsValid() {
		return nil, errors.New("invalid job type")
	}
	if params.Name == "" {
		return nil, errors.New("job name cannot be empty")
	}

	if params.MaxRetries < 0 {
		params.MaxRetries = 3
	}
	if params.Timeout <= 0 {
		return nil, errors.New("job timeout cannot be zero")
	}
	return &Job{
		jobType:     typer,
		name:        params.Name,
		description: params.Description,
		status:      JobStatusPending,
		scheduledAt: params.ScheduledAt,
		maxRetries:  params.MaxRetries,
		timeout:     time.Duration(params.Timeout) * time.Second,
		createdBy:   params.CreatedBy,
		progress:    nil,
	}, nil
}

func RebuildJob(params RebuildJobParams) (*Job, error) {
	return &Job{
		id:            params.Id,
		jobType:       JobType(params.JobType),
		name:          params.Name,
		description:   params.Description,
		status:        JobStatus(params.Status),
		crawlConfigId: params.CrawlConfigId,
		scheduledAt:   params.ScheduledAt,
		startedAt:     params.StartedAt,
		completedAt:   params.CompletedAt,
		progress:      params.Progress,
		maxRetries:    params.MaxRetries,
		retryCount:    params.RetryCount,
		lastError:     params.LastError,
		timeout:       time.Duration(params.Timeout) * time.Second,
		createdBy:     params.CreatedBy,
	}, nil
}

type Job struct {
	id            int
	jobType       JobType
	name          string
	description   *string
	status        JobStatus
	crawlConfigId *int
	scheduledAt   *time.Time
	startedAt     *time.Time
	completedAt   *time.Time
	// Progress tracking
	progress *JobProgress
	// Error handling
	maxRetries int
	retryCount int
	lastError  *string
	timeout    time.Duration
	createdBy  string
}

type JobProgress struct {
	CurrentStep int
	TotalSteps  int
	Message     string
	Percentage  int
}

func (j *JobProgress) Valid() error {
	if j.CurrentStep == 0 {
		return errors.New("invalid job progress")
	}
	if j.TotalSteps == 0 {
		return errors.New("invalid job progress")
	}
	if j.CurrentStep > j.TotalSteps {
		return errors.New("invalid job progress")
	}
	if j.CurrentStep < 0 {
		return errors.New("invalid job progress")
	}
	if j.CurrentStep < 0 {
		return errors.New("invalid job progress")
	}
	return nil
}

func (j *Job) ID() int                 { return j.id }
func (j *Job) Name() string            { return j.name }
func (j *Job) Description() *string    { return j.description }
func (j *Job) ScheduledAt() *time.Time { return j.scheduledAt }
func (j *Job) MaxRetries() int         { return j.maxRetries }
func (j *Job) JobType() JobType        { return j.jobType }
func (j *Job) Timeout() time.Duration  { return j.timeout }
func (j *Job) CreatedBy() string       { return j.createdBy }
func (j *Job) Status() JobStatus       { return j.status }
func (j *Job) CrawlConfigId() *int     { return j.crawlConfigId }
func (j *Job) CompletedAt() *time.Time { return j.completedAt }
func (j *Job) Progress() *JobProgress  { return j.progress }
func (j *Job) LastError() *string      { return j.lastError }
func (j *Job) RetryCount() int         { return j.retryCount }
func (j *Job) StartedAt() *time.Time   { return j.startedAt }

func (j *Job) Schedule(scheduledAt time.Time) error {
	if j.status != JobStatusPending {
		return errors.New("can only schedule pending jobs")
	}

	j.status = JobStatusScheduled
	j.scheduledAt = &scheduledAt
	return nil
}

func (j *Job) Start() error {
	if j.status != JobStatusPending && j.status != JobStatusScheduled {
		return errors.New("can only start pending or scheduled jobs")
	}

	now := time.Now()
	j.status = JobStatusRunning
	j.startedAt = &now
	return nil
}

func (j *Job) Pause() error {
	if j.status != JobStatusRunning {
		return errors.New("can only pause running jobs")
	}

	j.status = JobStatusPaused
	return nil
}

func (j *Job) Resume() error {
	if j.status != JobStatusPaused {
		return errors.New("can only resume paused jobs")
	}
	j.status = JobStatusRunning
	return nil
}

func (j *Job) Complete() error {
	if j.status != JobStatusRunning {
		return errors.New("can only complete running jobs")
	}
	now := time.Now()
	j.status = JobStatusCompleted
	j.completedAt = &now
	j.progress.Percentage = 100
	return nil
}

func (j *Job) Fail(errorMsg string) error {
	if j.status != JobStatusRunning {
		return errors.New("can only fail running jobs")
	}

	j.status = JobStatusFailed
	j.lastError = &errorMsg
	return nil
}

func (j *Job) Cancel() error {
	if j.status == JobStatusCompleted || j.status == JobStatusFailed {
		return errors.New("cannot cancel completed or failed jobs")
	}
	j.status = JobStatusCancelled

	return nil
}

func (j *Job) Retry() error {
	if j.status != JobStatusFailed {
		return errors.New("can only retry failed jobs")
	}
	if j.retryCount >= j.maxRetries {
		return errors.New("maximum retry count exceeded")
	}

	j.retryCount++
	j.status = JobStatusPending
	j.lastError = nil
	return nil
}

func (j *Job) UpdateProgress(progress *JobProgress) error {
	if j.status != JobStatusCompleted && j.status != JobStatusFailed {
		return errors.New("can only update completed or failed jobs")
	}
	err := progress.Valid()
	if err != nil {
		return err
	}
	j.progress = progress
	return nil
}

func (j *Job) IsExpired() bool {
	if j.status != JobStatusRunning || j.startedAt == nil {
		return false
	}
	return time.Since(*j.startedAt) > j.timeout
}

func (j *Job) CanRetry() bool {
	return j.status == JobStatusFailed && j.retryCount < j.maxRetries
}

func (j *Job) IsActive() bool {
	return j.status == JobStatusRunning || j.status == JobStatusPaused
}

func (j *Job) IsFinished() bool {
	return j.status == JobStatusCompleted || j.status == JobStatusFailed || j.status == JobStatusCancelled
}
