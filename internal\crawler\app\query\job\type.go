package queryjob

import (
	"time"
)

// Job represents a job in the query model
type Job struct {
	Id            int        `json:"id"`
	JobType       string     `json:"job_type"`
	Name          string     `json:"name"`
	Description   string     `json:"description"`
	Status        string     `json:"status"`
	CrawlConfigId *int       `json:"crawl_config_id,omitempty"`
	ScheduledAt   *time.Time `json:"scheduled_at,omitempty"`
	StartedAt     *time.Time `json:"started_at,omitempty"`
	CompletedAt   *time.Time `json:"completed_at,omitempty"`
	
	// Progress tracking
	CurrentStep        *int    `json:"current_step,omitempty"`
	TotalSteps         *int    `json:"total_steps,omitempty"`
	ProgressMessage    *string `json:"progress_message,omitempty"`
	ProgressPercentage *int    `json:"progress_percentage,omitempty"`
	
	// Error handling
	MaxRetries int     `json:"max_retries"`
	RetryCount int     `json:"retry_count"`
	LastError  *string `json:"last_error,omitempty"`
	Timeout    int     `json:"timeout_seconds"`
	
	// Metadata
	CreatedBy string    `json:"created_by"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// JobProgress represents job progress information
type JobProgress struct {
	CurrentStep int    `json:"current_step"`
	TotalSteps  int    `json:"total_steps"`
	Message     string `json:"message"`
	Percentage  int    `json:"percentage"`
}

// GetProgress returns the job progress if available
func (j *Job) GetProgress() *JobProgress {
	if j.CurrentStep != nil && j.TotalSteps != nil && 
	   j.ProgressMessage != nil && j.ProgressPercentage != nil {
		return &JobProgress{
			CurrentStep: *j.CurrentStep,
			TotalSteps:  *j.TotalSteps,
			Message:     *j.ProgressMessage,
			Percentage:  *j.ProgressPercentage,
		}
	}
	return nil
}
