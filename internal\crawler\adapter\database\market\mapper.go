package repository

import (
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jinzhu/copier"
	"go_core_market/internal/crawler/adapter/database/market/sqlc"
	querymarket "go_core_market/internal/crawler/app/query/market"
	"go_core_market/internal/crawler/domain/entity"
)

// FromSQLCModel converts sqlc.Market to domain.Market
func FromSQLCModel(sqlcMarket *sqlc.Market) (*entity.Market, error) {
	target := entity.MarketRebuildParams{}
	err := copier.Copy(&target, &sqlcMarket)
	if err != nil {
		return nil, err
	}
	return entity.RebuildMarket(target), nil
}

// FromSQLCModels converts slice of sqlc.Market to slice of domain.Market
func FromSQLCModels(sqlcMarkets []*sqlc.Market) ([]*entity.Market, error) {
	markets := make([]*entity.Market, len(sqlcMarkets))
	for i, sqlcMarket := range sqlcMarkets {
		m, err := FromSQLCModel(sqlcMarket)
		if err != nil {
			return nil, err
		}
		markets[i] = m
	}
	return markets, nil
}

func ToCreateParams(market *entity.Market) (*sqlc.CreateParams, error) {
	var lastCrawlAt pgtype.Timestamptz
	if market.LastCrawlAt() != nil {
		lastCrawlAt.Time = *market.LastCrawlAt()
		lastCrawlAt.Valid = true
	} else {
		lastCrawlAt.Valid = false
	}

	return &sqlc.CreateParams{
		Name:             market.Name(),
		DisplayName:      market.DisplayName(),
		MarketType:       string(market.Type()),
		Status:           string(market.Status()),
		BaseUrl:          market.BaseURL(),
		Currency:         string(market.Currency()),
		BuyerFeePercent:  market.BuyerFeePercent(),
		SellerFeePercent: market.SellerFeePercent(),
		CountryCode:      market.CountryCode(),
		Language:         market.Language(),
		Description:      market.Description(),
		IsActive:         market.IsActive(),
		LastCrawlAt:      lastCrawlAt,
	}, nil
}

func ToUpdateMarketParams(market *entity.Market) (*sqlc.UpdateMarketParams, error) {
	var lastCrawlAt pgtype.Timestamptz
	if market.LastCrawlAt() != nil {
		lastCrawlAt.Time = *market.LastCrawlAt()
		lastCrawlAt.Valid = true
	} else {
		lastCrawlAt.Valid = false
	}

	return &sqlc.UpdateMarketParams{
		ID:               market.ID(),
		Name:             market.Name(),
		DisplayName:      market.DisplayName(),
		MarketType:       string(market.Type()),
		Status:           string(market.Status()),
		BaseUrl:          market.BaseURL(),
		Currency:         string(market.Currency()),
		BuyerFeePercent:  market.BuyerFeePercent(),
		SellerFeePercent: market.SellerFeePercent(),
		CountryCode:      market.CountryCode(),
		Language:         market.Language(),
		Description:      market.Description(),
		IsActive:         market.IsActive(),
		LastCrawlAt:      lastCrawlAt,
	}, nil
}

func ToSaveBatchParams(markets []*entity.Market) ([]sqlc.CreateManyParams, error) {
	params := make([]sqlc.CreateManyParams, len(markets))
	for i, market := range markets {
		var lastCrawlAt pgtype.Timestamptz
		if market.LastCrawlAt() != nil {
			lastCrawlAt.Time = *market.LastCrawlAt()
			lastCrawlAt.Valid = true
		} else {
			lastCrawlAt.Valid = false
		}

		params[i] = sqlc.CreateManyParams{
			Name:             market.Name(),
			DisplayName:      market.DisplayName(),
			MarketType:       string(market.Type()),
			Status:           string(market.Status()),
			BaseUrl:          market.BaseURL(),
			Currency:         string(market.Currency()),
			BuyerFeePercent:  market.BuyerFeePercent(),
			SellerFeePercent: market.SellerFeePercent(),
			CountryCode:      market.CountryCode(),
			Language:         market.Language(),
			Description:      market.Description(),
			IsActive:         market.IsActive(),
			LastCrawlAt:      lastCrawlAt,
		}
	}
	return params, nil
}

// FromSQLCModelToQueryModel converts sqlc.Market to query.Market
func FromSQLCModelToQueryModel(sqlcMarket *sqlc.Market) (*querymarket.Market, error) {
	var lastCrawlAt *time.Time
	if sqlcMarket.LastCrawlAt.Valid {
		lastCrawlAt = &sqlcMarket.LastCrawlAt.Time
	}

	return &querymarket.Market{
		Id:               sqlcMarket.ID,
		Name:             sqlcMarket.Name,
		DisplayName:      sqlcMarket.DisplayName,
		Type:             sqlcMarket.MarketType,
		Status:           sqlcMarket.Status,
		BaseURL:          sqlcMarket.BaseUrl,
		Currency:         sqlcMarket.Currency,
		BuyerFeePercent:  sqlcMarket.BuyerFeePercent,
		SellerFeePercent: sqlcMarket.SellerFeePercent,
		CountryCode:      sqlcMarket.CountryCode,
		Language:         sqlcMarket.Language,
		Description:      sqlcMarket.Description,
		IsActive:         sqlcMarket.IsActive,
		LastCrawlAt:      lastCrawlAt,
		CreatedAt:        sqlcMarket.CreatedAt.Time,
		UpdatedAt:        sqlcMarket.UpdatedAt.Time,
	}, nil
}

// FromSQLCModelsToQueryModels converts slice of sqlc.Market to slice of query.Market
func FromSQLCModelsToQueryModels(sqlcMarkets []*sqlc.Market) ([]*querymarket.Market, error) {
	markets := make([]*querymarket.Market, len(sqlcMarkets))
	for i, sqlcMarket := range sqlcMarkets {
		market, err := FromSQLCModelToQueryModel(sqlcMarket)
		if err != nil {
			return nil, err
		}
		markets[i] = market
	}
	return markets, nil
}
