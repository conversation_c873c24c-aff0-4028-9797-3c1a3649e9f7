package querymarket

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type FilterMarketsQuery struct {
	Types       []string
	Statuses    []string
	Currencies  []string
	IsActive    *bool
	CountryCode string
	OrderBy     string
}

type FilterMarketsQueryHandler decorator.QueryHandler[FilterMarketsQuery, []*Market]

type filterMarketsQueryHandler struct {
	repo MarketReadModel
}

func NewFilterMarketsQueryHandler(
	repo MarketReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) FilterMarketsQueryHandler {

	return decorator.ApplyQueryDecorators[FilterMarketsQuery, []*Market](
		filterMarketsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h filterMarketsQueryHandler) Handle(ctx context.Context, query FilterMarketsQuery) ([]*Market, error) {
	return h.repo.FilterMarkets(ctx, query)
}
