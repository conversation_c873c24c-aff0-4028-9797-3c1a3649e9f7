package repository

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
)

type RepositoryProxy interface {
	Create(ctx context.Context, proxy *entity.Proxy) error
	CreateMany(ctx context.Context, proxies []*entity.Proxy) error

	Update(
		ctx context.Context,
		id int,
		updateFn func(p *entity.Proxy) (*entity.Proxy, error),
	) error

	UpdateMany(ctx context.Context,
		ids []int,
		updateFn func(p []*entity.Proxy) ([]*entity.Proxy, error),
	) error

	Delete(ctx context.Context, id int) error
	DeleteMany(ctx context.Context, ids []int) error

	FindByID(ctx context.Context, id int) (*entity.Proxy, error)
	FindByIDs(ctx context.Context, ids []int) ([]*entity.Proxy, error)
}
