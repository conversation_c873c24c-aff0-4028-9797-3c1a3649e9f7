package server

import (
	"context"
	"fmt"
	"net"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	"go_core_market/pkg/logger"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/status"

	grpc_middleware "github.com/grpc-ecosystem/go-grpc-middleware"
	grpc_auth "github.com/grpc-ecosystem/go-grpc-middleware/auth"
	grpc_recovery "github.com/grpc-ecosystem/go-grpc-middleware/recovery"
	grpc_ctxtags "github.com/grpc-ecosystem/go-grpc-middleware/tags"
)

type Config struct {
	Host   string
	Port   int
	APIKey string // API key for authentication
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	APIKey string
	// Add other auth methods if needed (JWT, OAuth, etc.)
}

func RunGRPCServer(registerServer func(server *grpc.Server), log logger.Logger, config Config) error {
	// Create authentication function
	authFunc := createAuthFunc(config.APIKey, log)

	// Setup recovery options
	recoveryOpts := []grpc_recovery.Option{
		grpc_recovery.WithRecoveryHandler(func(p interface{}) (err error) {
			log.Error("Recovered from panic", "panic", p)
			return status.Errorf(codes.Internal, "Internal server error")
		}),
	}

	// Create gRPC server with middleware chain
	grpcServer := grpc.NewServer(
		grpc.UnaryInterceptor(grpc_middleware.ChainUnaryServer(
			grpc_ctxtags.UnaryServerInterceptor(), // Add context tags
			grpc_recovery.UnaryServerInterceptor(recoveryOpts...), // Recovery from panics
			grpc_auth.UnaryServerInterceptor(authFunc), // Authentication
			loggingInterceptor(log), // Request logging
			metricsInterceptor(log), // Metrics collection
		)),
		grpc.StreamInterceptor(grpc_middleware.ChainStreamServer(
			grpc_ctxtags.StreamServerInterceptor(), // Add context tags
			grpc_recovery.StreamServerInterceptor(recoveryOpts...), // Recovery from panics
			grpc_auth.StreamServerInterceptor(authFunc), // Authentication
			streamLoggingInterceptor(log), // Stream logging
		)),
	)

	// Register the server
	registerServer(grpcServer)

	// Create listener
	address := fmt.Sprintf("%s:%d", config.Host, config.Port)
	listener, err := net.Listen("tcp", address)
	if err != nil {
		return fmt.Errorf("failed to listen on %s: %w", address, err)
	}

	log.Info("Starting gRPC server", "address", address)

	// Start server in a goroutine
	serverErr := make(chan error, 1)
	go func() {
		if err := grpcServer.Serve(listener); err != nil {
			serverErr <- fmt.Errorf("gRPC server failed: %w", err)
		}
	}()

	// Wait for interrupt signal
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	select {
	case err := <-serverErr:
		return err
	case sig := <-quit:
		log.Info("Received shutdown signal", "signal", sig)

		// Graceful shutdown
		log.Info("Shutting down gRPC server...")
		grpcServer.GracefulStop()

		log.Info("gRPC server stopped")
		return nil
	}
}

// createAuthFunc creates an authentication function for API key validation
func createAuthFunc(apiKey string, log logger.Logger) grpc_auth.AuthFunc {
	return func(ctx context.Context) (context.Context, error) {
		// Skip authentication for health check and reflection endpoints
		if method, ok := grpc.Method(ctx); ok {
			if strings.HasPrefix(method, "/grpc.health.v1.Health/") ||
				strings.HasPrefix(method, "/grpc.reflection.v1alpha.ServerReflection/") {
				return ctx, nil
			}
		}

		// Extract token from metadata
		token, err := grpc_auth.AuthFromMD(ctx, "bearer")
		if err != nil {
			log.Warn("Failed to extract auth token", "error", err)
			return nil, status.Errorf(codes.Unauthenticated, "missing or invalid authentication token")
		}

		// Validate API key
		if token != apiKey {
			log.Warn("Invalid API key provided", "provided_token", token)
			return nil, status.Errorf(codes.Unauthenticated, "invalid API key")
		}

		// Add user info to context if needed
		newCtx := context.WithValue(ctx, "authenticated", true)
		newCtx = context.WithValue(newCtx, "api_key", token)

		return newCtx, nil
	}
}

// loggingInterceptor logs gRPC requests and responses
func loggingInterceptor(log logger.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		start := time.Now()

		// Extract request ID if available
		requestID := extractRequestID(ctx)

		log.Info("gRPC request started",
			"method", info.FullMethod,
			"request_id", requestID,
		)

		resp, err := handler(ctx, req)

		duration := time.Since(start)

		if err != nil {
			log.Error("gRPC request failed",
				"method", info.FullMethod,
				"request_id", requestID,
				"duration", duration,
				"error", err,
			)
		} else {
			log.Info("gRPC request completed",
				"method", info.FullMethod,
				"request_id", requestID,
				"duration", duration,
			)
		}

		return resp, err
	}
}

// metricsInterceptor collects metrics for gRPC requests
func metricsInterceptor(log logger.Logger) grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req interface{}, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (interface{}, error) {
		start := time.Now()

		resp, err := handler(ctx, req)

		duration := time.Since(start)

		// Log metrics (in production, you might want to send to Prometheus or similar)
		method := strings.ReplaceAll(info.FullMethod, "/", "_")
		method = strings.TrimPrefix(method, "_")

		if err != nil {
			log.Info("gRPC metrics",
				"metric_type", "request_duration",
				"method", method,
				"status", "error",
				"duration_ms", duration.Milliseconds(),
			)
		} else {
			log.Info("gRPC metrics",
				"metric_type", "request_duration",
				"method", method,
				"status", "success",
				"duration_ms", duration.Milliseconds(),
			)
		}

		return resp, err
	}
}

// streamLoggingInterceptor logs gRPC stream requests
func streamLoggingInterceptor(log logger.Logger) grpc.StreamServerInterceptor {
	return func(srv interface{}, ss grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		start := time.Now()

		requestID := extractRequestIDFromStream(ss.Context())

		log.Info("gRPC stream started",
			"method", info.FullMethod,
			"request_id", requestID,
		)

		err := handler(srv, ss)

		duration := time.Since(start)

		if err != nil {
			log.Error("gRPC stream failed",
				"method", info.FullMethod,
				"request_id", requestID,
				"duration", duration,
				"error", err,
			)
		} else {
			log.Info("gRPC stream completed",
				"method", info.FullMethod,
				"request_id", requestID,
				"duration", duration,
			)
		}

		return err
	}
}

// extractRequestID extracts request ID from context metadata
func extractRequestID(ctx context.Context) string {
	if md, ok := metadata.FromIncomingContext(ctx); ok {
		if requestIDs := md.Get("x-request-id"); len(requestIDs) > 0 {
			return requestIDs[0]
		}
	}
	return "unknown"
}

// extractRequestIDFromStream extracts request ID from stream context
func extractRequestIDFromStream(ctx context.Context) string {
	return extractRequestID(ctx)
}

// ValidateAPIKey validates the provided API key
func ValidateAPIKey(ctx context.Context, expectedKey string) error {
	md, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return status.Errorf(codes.Unauthenticated, "missing metadata")
	}

	authHeaders := md.Get("authorization")
	if len(authHeaders) == 0 {
		return status.Errorf(codes.Unauthenticated, "missing authorization header")
	}

	authHeader := authHeaders[0]
	if !strings.HasPrefix(authHeader, "Bearer ") {
		return status.Errorf(codes.Unauthenticated, "invalid authorization header format")
	}

	token := strings.TrimPrefix(authHeader, "Bearer ")
	if token != expectedKey {
		return status.Errorf(codes.Unauthenticated, "invalid API key")
	}

	return nil
}
