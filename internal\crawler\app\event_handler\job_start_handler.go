package eventhandler

import (
	"context"
	"fmt"
	"go_core_market/internal/crawler/app/service"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message/event"
)

// JobStartEventHandler handles job start events
type JobStartEventHandler struct {
	crawlService      service.CrawlService
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries
	jobQueries        *queryjob.JobQueries
	logger           logger.Logger
}

// NewJobStartEventHandler creates a new job start event handler
func NewJobStartEventHandler(
	crawlService service.CrawlService,
	crawlConfigQueries *querycrawlconfig.CrawlConfigQueries,
	jobQueries *queryjob.JobQueries,
	logger logger.Logger,
) *JobStartEventHandler {
	return &JobStartEventHandler{
		crawlService:      crawlService,
		crawlConfigQueries: crawlConfigQueries,
		jobQueries:        jobQueries,
		logger:           logger,
	}
}

// Handle processes job start events
func (h *JobStartEventHandler) Handle(ctx context.Context, evt event.Event) error {
	h.logger.Info("Received job start event", "event_id", evt.GetID(), "event_type", evt.GetType())

	// Check if this is a job started event
	if evt.GetType() != event.JobStartedEventType {
		h.logger.Debug("Ignoring non-job-started event", "event_type", evt.GetType())
		return nil
	}

	// Cast to job started event
	jobStartedEvent, ok := evt.(*event.JobStartedEvent)
	if !ok {
		return fmt.Errorf("failed to cast event to JobStartedEvent")
	}

	data := jobStartedEvent.GetData()
	h.logger.Info("Processing job start", 
		"job_id", data.JobID, 
		"job_type", data.JobType, 
		"job_name", data.Name)

	// Check if job type is crawl
	if data.JobType != string(entity.JobTypeCrawl) {
		h.logger.Debug("Job is not a crawl job, skipping", 
			"job_type", data.JobType, 
			"job_id", data.JobID)
		return nil
	}

	// Get job details to find crawl config ID
	// Note: We need to get the job details from the job repository to get the crawl config ID
	// Since the event data doesn't include crawl config ID, we need to query it
	// For now, we'll assume the job name or some other mechanism provides the config ID
	// This is a limitation that should be addressed by including crawl_config_id in the event data

	h.logger.Info("Job is a crawl job, processing crawl request", "job_id", data.JobID)

	// Get job details to find crawl config ID
	job, err := h.jobQueries.GetJobByID.Handle(ctx, queryjob.GetJobByIDQuery{
		ID: data.JobID,
	})
	if err != nil {
		return fmt.Errorf("failed to get job details for job %d: %w", data.JobID, err)
	}

	// Check if job has crawl config ID
	if job.CrawlConfigId == nil {
		h.logger.Debug("Crawl job has no associated crawl config, skipping", "job_id", data.JobID)
		return nil
	}

	return h.processCrawlJob(ctx, data.JobID, *job.CrawlConfigId)
}

// processCrawlJob handles the crawl job processing
func (h *JobStartEventHandler) processCrawlJob(ctx context.Context, jobID int, configID int) error {
	h.logger.Info("Processing crawl job", "job_id", jobID, "config_id", configID)

	// Get crawl config details
	config, err := h.crawlConfigQueries.GetConfigID.Handle(ctx, querycrawlconfig.GetConfigIDQuery{
		ConfigID: configID,
	})
	if err != nil {
		return fmt.Errorf("failed to get crawl config %d: %w", configID, err)
	}

	// Check if config type is normal
	if config.TypeConfig != string(entity.TypeConfigNormal) {
		h.logger.Debug("Config is not normal type, skipping",
			"config_type", config.TypeConfig,
			"config_id", config.Id,
			"job_id", jobID)
		return nil
	}

	h.logger.Info("Config is normal type, starting normal price crawl",
		"config_id", config.Id,
		"market_id", config.MarketId,
		"job_id", jobID)

	// Call crawl service
	err = h.crawlService.CrawlPriceNormal(ctx, config)
	if err != nil {
		return fmt.Errorf("failed to crawl normal prices for job %d: %w", jobID, err)
	}

	h.logger.Info("Successfully completed normal price crawl", "job_id", jobID, "config_id", configID)

	return nil
}
