-- name: CreateAuth :one
INSERT INTO auth_crawl (market_id, auth_type, value, is_active, is_use, expired_at)
VALUES ($1, $2, $3, $4, $5, $6)
RETURNING id;

-- name: CreateMany :copyfrom
INSERT INTO auth_crawl (market_id, auth_type, value, is_active, is_use, expired_at)
VALUES ($1, $2, $3, $4, $5, $6);

-- name: FindAuthByID :one
SELECT * FROM auth_crawl WHERE id = $1;

-- name: FindByValue :one
SELECT * FROM auth_crawl WHERE value = $1 LIMIT 1;

-- name: UpdateAuth :one
UPDATE auth_crawl 
SET market_id = $2, auth_type = $3, value = $4, is_active = $5, is_use = $6, 
    last_used_at = $7, expired_at = $8
WHERE id = $1
RETURNING *;

-- name: FindAuthByIDs :many
SELECT *
FROM auth_crawl WHERE id = ANY(@ids::int[]);

-- name: DeleteAuth :exec
DELETE FROM auth_crawl WHERE id = $1;

-- name: GetAllAuthCrawl :many
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
ORDER BY
    CASE WHEN $3 = 'id' THEN id END,
    CASE WHEN $3 = 'market_id' THEN market_id END,
    CASE WHEN $3 = 'auth_type' THEN auth_type END,
    CASE WHEN $3 = 'created_at' THEN created_at END,
    CASE WHEN $3 = '' OR $3 IS NULL THEN created_at END DESC
LIMIT $1 OFFSET $2;

-- name: CountAuthCrawl :one
SELECT COUNT(*) FROM auth_crawl;

-- name: GetAuthCrawlByMarketId :one
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
WHERE market_id = $1
LIMIT 1;

-- name: GetAvailableAuthCrawlByMarket :many
SELECT id, market_id, auth_type, value, is_active, is_use, last_used_at, expired_at, created_at, updated_at
FROM auth_crawl
WHERE market_id = $1 AND is_active = true AND is_use = false
  AND (expired_at IS NULL OR expired_at > NOW())
ORDER BY last_used_at ASC NULLS FIRST
LIMIT $2;