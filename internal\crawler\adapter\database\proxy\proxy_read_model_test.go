package repository

import (
	"context"
	"testing"
	"time"

	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	sqlc2 "go_core_market/internal/crawler/adapter/database/proxy/sqlc"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockQuerier is a mock implementation of sqlc2.Querier
type MockQuerier struct {
	mock.Mock
}

func (m *<PERSON>ck<PERSON>uerier) CountProxies(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *<PERSON>ck<PERSON>uerier) CreateProxies(ctx context.Context, arg []sqlc2.CreateProxiesParams) (int64, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(int64), args.Error(1)
}

func (m *<PERSON><PERSON>uer<PERSON>) CreateProxy(ctx context.Context, arg sqlc2.CreateProxyParams) (int32, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(int32), args.Error(1)
}

func (m *MockQuerier) DeleteManyProxies(ctx context.Context, dollar_1 []int32) error {
	args := m.Called(ctx, dollar_1)
	return args.Error(0)
}

func (m *MockQuerier) DeleteProxy(ctx context.Context, id int32) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockQuerier) FindProxiesByIDs(ctx context.Context, dollar_1 []int32) ([]*sqlc2.Proxy, error) {
	args := m.Called(ctx, dollar_1)
	return args.Get(0).([]*sqlc2.Proxy), args.Error(1)
}

func (m *MockQuerier) FindProxyByID(ctx context.Context, id int32) (*sqlc2.Proxy, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*sqlc2.Proxy), args.Error(1)
}

func (m *MockQuerier) GetAllProxies(ctx context.Context, arg sqlc2.GetAllProxiesParams) ([]*sqlc2.Proxy, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).([]*sqlc2.Proxy), args.Error(1)
}

func (m *MockQuerier) GetAvailableProxies(ctx context.Context, limit int32) ([]*sqlc2.Proxy, error) {
	args := m.Called(ctx, limit)
	return args.Get(0).([]*sqlc2.Proxy), args.Error(1)
}

func (m *MockQuerier) UpdateProxy(ctx context.Context, arg sqlc2.UpdateProxyParams) (*sqlc2.Proxy, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(*sqlc2.Proxy), args.Error(1)
}

func TestProxyReadModel_GetProxyById(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &proxyReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	proxyID := 1
	expectedSQLCProxy := &sqlc2.Proxy{
		ID:       1,
		Host:     "127.0.0.1",
		Port:     8080,
		UserName: "user",
		Password: "pass",
		IsActive: true,
		IsUse:    false,
		LastUsedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
		CreatedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
	}

	mockQuerier.On("FindProxyByID", ctx, int32(proxyID)).Return(expectedSQLCProxy, nil)

	// Act
	result, err := readModel.GetProxyById(ctx, proxyID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, proxyID, result.Id)
	assert.Equal(t, "127.0.0.1", result.Host)
	assert.Equal(t, 8080, result.Port)
	assert.Equal(t, "user", result.UserName)
	assert.Equal(t, "pass", result.Password)
	assert.True(t, result.IsActive)
	assert.False(t, result.IsUse)

	mockQuerier.AssertExpectations(t)
}

func TestProxyReadModel_CountProxies(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &proxyReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	expectedCount := int64(10)

	mockQuerier.On("CountProxies", ctx).Return(expectedCount, nil)

	// Act
	result, err := readModel.CountProxies(ctx)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedCount, result)

	mockQuerier.AssertExpectations(t)
}

func TestProxyReadModel_GetAvailableProxies(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &proxyReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	limit := 5
	expectedSQLCProxies := []*sqlc2.Proxy{
		{
			ID:       1,
			Host:     "127.0.0.1",
			Port:     8080,
			UserName: "user1",
			Password: "pass1",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
		{
			ID:       2,
			Host:     "*********",
			Port:     8081,
			UserName: "user2",
			Password: "pass2",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}

	mockQuerier.On("GetAvailableProxies", ctx, int32(limit)).Return(expectedSQLCProxies, nil)

	// Act
	result, err := readModel.GetAvailableProxies(ctx, limit)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, 1, result[0].Id)
	assert.Equal(t, "127.0.0.1", result[0].Host)
	assert.Equal(t, 2, result[1].Id)
	assert.Equal(t, "*********", result[1].Host)

	mockQuerier.AssertExpectations(t)
}

func TestProxyReadModel_AllProxies(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &proxyReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	query := queryproxy.GetAllProxiesQuery{
		Page:     1,
		PageSize: 10,
		OrderBy:  "id",
	}

	expectedSQLCProxies := []*sqlc2.Proxy{
		{
			ID:       1,
			Host:     "127.0.0.1",
			Port:     8080,
			UserName: "user1",
			Password: "pass1",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}

	expectedParams := sqlc2.GetAllProxiesParams{
		Limit:   10,
		Offset:  0,
		Column3: "id",
	}

	mockQuerier.On("GetAllProxies", ctx, expectedParams).Return(expectedSQLCProxies, nil)

	// Act
	result, err := readModel.AllProxies(ctx, query)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, 1, result[0].Id)
	assert.Equal(t, "127.0.0.1", result[0].Host)

	mockQuerier.AssertExpectations(t)
}
