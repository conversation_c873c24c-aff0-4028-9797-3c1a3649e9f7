package youpin

import (
	"go_core_market/internal/crawler/domain/value_object"
	"log"
	"strconv"
	"time"
)

func ItemToCrawledPrice(item CommodityItem, marketId int) *value_object.CrawledPrice {
	sellPrice, err1 := strconv.ParseFloat(item.Price, 64)
	buyPrice := 0.0
	if err1 != nil {
		log.Printf("Error parsing price: %v, %v", err1)
	}
	return &value_object.CrawledPrice{
		ItemName:   item.CommodityHashName,
		MarketID:   marketId,
		SellPrice:  sellPrice,
		BuyPrice:   buyPrice,
		NumSell:    item.OnSaleCount,
		NumBuy:     0,
		Condition:  nil,
		RecordedAt: time.Now(),
	}
}
