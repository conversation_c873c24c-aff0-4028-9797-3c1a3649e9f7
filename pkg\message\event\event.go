package event

import (
	"fmt"
	"time"
)

type Event interface {
	GetID() string
	GetType() string
	GetTimestamp() time.Time
	GetEventVersion() string
	GetAggregateID() string
	GetData() interface{}
}

type BaseEvent[T any] struct {
	EventID      string    `json:"event_id"`
	EventType    string    `json:"event_type"`
	EventVersion string    `json:"event_version"`
	Timestamp    time.Time `json:"timestamp"`
	Data         T         `json:"data"`
	AggregateID  string    `json:"aggregate_id"`
}

func (e *BaseEvent[T]) GetTimestamp() time.Time {
	return e.Timestamp
}

func (e *BaseEvent[T]) GetID() string {
	return e.EventID
}

func (e *BaseEvent[T]) GetType() string {
	return e.EventType
}

func (e *BaseEvent[T]) GetEventVersion() string {
	return e.EventVersion
}

func (e *BaseEvent[T]) GetAggregateID() string {
	return e.AggregateID
}

func (e *BaseEvent[T]) GetData() interface{} {
	return e.Data
}

func (e *BaseEvent[T]) GetTypedData() T {
	return e.Data
}

func NewBaseEvent[T any](eventType, aggregateID string, data T) *BaseEvent[T] {
	return &BaseEvent[T]{
		EventID:      generateEventID(),
		EventType:    eventType,
		EventVersion: "1.0",
		Timestamp:    time.Now(),
		Data:         data,
		AggregateID:  aggregateID,
	}
}

// Helper function to generate event IDs
func generateEventID() string {
	return fmt.Sprintf("evt-%d", time.Now().UnixNano())
}
