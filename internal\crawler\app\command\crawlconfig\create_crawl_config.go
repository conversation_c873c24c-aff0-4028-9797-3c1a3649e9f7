package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CreateCrawlConfig struct {
	MarketID               int
	Description            string
	TypeConfig             string
	RequestsPerMinute      int
	RequireAuth            bool
	MaxNumberAuth          int
	MaxNumberProxy         int
	PerRequestDelaySeconds int
	TimeoutSeconds         int
	MaxRetries             int
	RetryDelaySeconds      int
	MaxConcurrent          int
	CreatedBy              string
}

type CreateCrawlConfigHandler decorator.CommandHandler[CreateCrawlConfig]

type createCrawlConfigHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewCreateCrawlConfigHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateCrawlConfigHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateCrawlConfig](
		createCrawlConfigHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createCrawlConfigHandler) Handle(ctx context.Context, cmd CreateCrawlConfig) error {
	// Create domain crawl config
	configParams := entity.CrawlConfigParams{
		MarketID:          cmd.MarketID,
		Description:       cmd.Description,
		TypeConfig:        cmd.TypeConfig,
		RequestsPerMinute: cmd.RequestsPerMinute,
		RequireAuth:       cmd.RequireAuth,
		MaxNumberAuth:     cmd.MaxNumberAuth,
		MaxNumberProxy:    cmd.MaxNumberProxy,
		PerRequestDelay:   cmd.PerRequestDelaySeconds,
		Timeout:           cmd.TimeoutSeconds,
		MaxRetries:        cmd.MaxRetries,
		RetryDelay:        cmd.RetryDelaySeconds,
		MaxConcurrent:     cmd.MaxConcurrent,
	}

	config, err := entity.NewCrawlConfig(configParams)
	if err != nil {
		return err
	}

	err = h.repo.Create(ctx, config)
	if err != nil {
		return err
	}

	return nil
}
