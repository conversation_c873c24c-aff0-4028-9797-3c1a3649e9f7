package queryjob

import (
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type JobQueries struct {
	GetAllJobs        GetAllJobsQueryHandler
	GetJobById        GetJobByIdQueryHandler
	GetJobsByStatus   GetJobsByStatusQueryHandler
	FilterJobs        FilterJobsQueryHandler
	GetPendingJobs    GetPendingJobsQueryHandler
}

func NewJobQueries(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) *JobQueries {
	return &JobQueries{
		GetAllJobs:        NewGetAllJobsQueryHandler(repo, logger, metricsClient),
		GetJobById:        NewGetJobByIdQueryHandler(repo, logger, metricsClient),
		GetJobsByStatus:   NewGetJobsByStatusQueryHandler(repo, logger, metricsClient),
		FilterJobs:        NewFilterJobsQueryHandler(repo, logger, metricsClient),
		GetPendingJobs:    NewGetPendingJobsQueryHandler(repo, logger, metricsClient),
	}
}
