package logger

import (
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"

	"go_core_market/pkg/config"
)

// Import config types from pkg/config
type LoggerConfig = config.LoggerConfig
type FileLogConfig = config.FileLogConfig

// ZapLogger triển khai Logger interface sử dụng zap
type ZapLogger struct {
	logger *zap.Logger
	config LoggerConfig
}

// NewZapLogger tạo một instance mới của ZapLogger với config đơn giản
func NewZapLogger(level string) (Logger, error) {
	config := LoggerConfig{
		Level:       level,
		Format:      "json",
		Output:      []string{"stdout"},
		ServiceName: "unknown",
		Environment: "development",
	}
	return NewZapLoggerWithConfig(config)
}

// NewZapLoggerWithConfig tạo một instance mới của ZapLogger với config chi tiết
func NewZapLoggerWithConfig(config LoggerConfig) (Logger, error) {
	// Parse log level
	zapLevel := zapcore.InfoLevel
	if err := zapLevel.UnmarshalText([]byte(config.Level)); err != nil {
		return nil, fmt.Errorf("invalid log level %s: %w", config.Level, err)
	}

	// Create encoder config
	var encoderConfig zapcore.EncoderConfig
	if config.Environment == "development" {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	} else {
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	}

	// Create encoder
	var encoder zapcore.Encoder
	if config.Format == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// Create cores for different outputs
	var cores []zapcore.Core

	// Add cores for each output
	for _, output := range config.Output {
		var writeSyncer zapcore.WriteSyncer
		var err error

		switch output {
		case "stdout":
			writeSyncer = zapcore.AddSync(os.Stdout)
		case "stderr":
			writeSyncer = zapcore.AddSync(os.Stderr)
		case "file":
			if config.FileConfig.Enabled {
				writeSyncer, err = createFileWriteSyncer(config.FileConfig)
				if err != nil {
					return nil, fmt.Errorf("failed to create file writer: %w", err)
				}
			} else {
				continue
			}
		default:
			// Treat as file path
			writeSyncer, err = createFileWriteSyncerFromPath(output)
			if err != nil {
				return nil, fmt.Errorf("failed to create file writer for %s: %w", output, err)
			}
		}

		core := zapcore.NewCore(encoder, writeSyncer, zapLevel)
		cores = append(cores, core)
	}

	// Combine all cores
	combinedCore := zapcore.NewTee(cores...)

	// Create logger with initial fields
	logger := zap.New(combinedCore, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))
	logger = logger.With(
		zap.String("service", config.ServiceName),
		zap.String("environment", config.Environment),
		zap.String("version", getVersion()),
	)

	return &ZapLogger{
		logger: logger,
		config: config,
	}, nil
}

func createFileWriteSyncer(config FileLogConfig) (zapcore.WriteSyncer, error) {
	// Ensure directory exists
	dir := filepath.Dir(config.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	lumberjackLogger := &lumberjack.Logger{
		Filename:   config.Path,
		MaxSize:    config.MaxSize,
		MaxBackups: config.MaxBackups,
		MaxAge:     config.MaxAge,
		Compress:   config.Compress,
	}

	return zapcore.AddSync(lumberjackLogger), nil
}

func createFileWriteSyncerFromPath(path string) (zapcore.WriteSyncer, error) {
	// Ensure directory exists
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create log directory: %w", err)
	}

	file, err := os.OpenFile(path, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return nil, fmt.Errorf("failed to open log file: %w", err)
	}

	return zapcore.AddSync(file), nil
}

func getVersion() string {
	// In a real application, this could be set during build time
	return "1.0.0"
}

// NewZapLoggerForDevelopment tạo một instance ZapLogger cho môi trường development
func NewZapLoggerForDevelopment() (Logger, error) {
	config := LoggerConfig{
		Level:       "debug",
		Format:      "console",
		Output:      []string{"stdout"},
		ServiceName: "development",
		Environment: "development",
	}
	return NewZapLoggerWithConfig(config)
}

func (l *ZapLogger) Debug(msg string, fields ...interface{}) {
	l.logger.Sugar().Debugw(msg, fields...)
}

func (l *ZapLogger) Info(msg string, fields ...interface{}) {
	l.logger.Sugar().Infow(msg, fields...)
}

func (l *ZapLogger) Warn(msg string, fields ...interface{}) {
	l.logger.Sugar().Warnw(msg, fields...)
}

func (l *ZapLogger) Error(msg string, fields ...interface{}) {
	l.logger.Sugar().Errorw(msg, fields...)
}

func (l *ZapLogger) Fatal(msg string, fields ...interface{}) {
	l.logger.Sugar().Fatalw(msg, fields...)
}

// WithFields tạo một logger mới với các fields được thêm vào
func (l *ZapLogger) WithFields(fields map[string]any) Logger {
	zapFields := make([]zap.Field, 0, len(fields))
	for key, value := range fields {
		zapFields = append(zapFields, zap.Any(key, value))
	}
	return &ZapLogger{
		logger: l.logger.With(zapFields...),
		config: l.config,
	}
}

// WithError tạo một logger mới với error field được thêm vào
func (l *ZapLogger) WithError(err error) Logger {
	return &ZapLogger{
		logger: l.logger.With(zap.Error(err)),
		config: l.config,
	}
}

func (l *ZapLogger) Sync() error {
	return l.logger.Sync()
}

// GetConfig returns the logger configuration
func (l *ZapLogger) GetConfig() LoggerConfig {
	return l.config
}

// SetLevel dynamically changes the log level
func (l *ZapLogger) SetLevel(level string) error {
	zapLevel := zapcore.InfoLevel
	if err := zapLevel.UnmarshalText([]byte(level)); err != nil {
		return fmt.Errorf("invalid log level %s: %w", level, err)
	}

	// Note: This is a simplified implementation
	// In a production system, you might want to use zap.AtomicLevel
	l.config.Level = level
	return nil
}
