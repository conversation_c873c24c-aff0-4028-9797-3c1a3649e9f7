package repository

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
)

type RepositoryMarket interface {
	Create(ctx context.Context, auth *entity.Market) error
	CreateMany(ctx context.Context, auths []*entity.Market) error
	Update(ctx context.Context,
		id int,
		updateFn func(m *entity.Market) (*entity.Market, error),
	) error

	UpdateMany(ctx context.Context,
		ids []int,
		updateFn func(m []*entity.Market) ([]*entity.Market, error),
	) error

	Delete(ctx context.Context, id int) error
	DeleteMany(ctx context.Context, ids []int) error
}
