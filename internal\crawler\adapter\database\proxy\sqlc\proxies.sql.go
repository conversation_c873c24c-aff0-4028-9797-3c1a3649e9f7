// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: proxies.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountProxies = `-- name: CountProxies :one
SELECT COUNT(*) FROM proxies
`

func (q *Queries) CountProxies(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountProxies)
	var count int64
	err := row.Scan(&count)
	return count, err
}

type CreateProxiesParams struct {
	Host       string             `db:"host" json:"host"`
	Port       int32              `db:"port" json:"port"`
	UserName   string             `db:"user_name" json:"user_name"`
	Password   string             `db:"password" json:"password"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
}

const CreateProxy = `-- name: CreateProxy :one
INSERT INTO proxies (
    host,
    port,
    user_name,
    password,
    is_active,
    is_use,
    last_used_at
) VALUES (
             $1, $2, $3, $4, $5, $6, $7
         )
    RETURNING id
`

type CreateProxyParams struct {
	Host       string             `db:"host" json:"host"`
	Port       int32              `db:"port" json:"port"`
	UserName   string             `db:"user_name" json:"user_name"`
	Password   string             `db:"password" json:"password"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
}

func (q *Queries) CreateProxy(ctx context.Context, arg CreateProxyParams) (int32, error) {
	row := q.db.QueryRow(ctx, CreateProxy,
		arg.Host,
		arg.Port,
		arg.UserName,
		arg.Password,
		arg.IsActive,
		arg.IsUse,
		arg.LastUsedAt,
	)
	var id int32
	err := row.Scan(&id)
	return id, err
}

const DeleteManyProxies = `-- name: DeleteManyProxies :exec
DELETE FROM proxies WHERE id = ANY($1::int[])
`

func (q *Queries) DeleteManyProxies(ctx context.Context, dollar_1 []int32) error {
	_, err := q.db.Exec(ctx, DeleteManyProxies, dollar_1)
	return err
}

const DeleteProxy = `-- name: DeleteProxy :exec
DELETE FROM proxies WHERE id = $1
`

func (q *Queries) DeleteProxy(ctx context.Context, id int32) error {
	_, err := q.db.Exec(ctx, DeleteProxy, id)
	return err
}

const FindProxiesByIDs = `-- name: FindProxiesByIDs :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies WHERE id = ANY($1::int[])
`

func (q *Queries) FindProxiesByIDs(ctx context.Context, dollar_1 []int32) ([]*Proxy, error) {
	rows, err := q.db.Query(ctx, FindProxiesByIDs, dollar_1)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Proxy{}
	for rows.Next() {
		var i Proxy
		if err := rows.Scan(
			&i.ID,
			&i.Host,
			&i.Port,
			&i.UserName,
			&i.Password,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const FindProxyByID = `-- name: FindProxyByID :one
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies WHERE id = $1
`

func (q *Queries) FindProxyByID(ctx context.Context, id int32) (*Proxy, error) {
	row := q.db.QueryRow(ctx, FindProxyByID, id)
	var i Proxy
	err := row.Scan(
		&i.ID,
		&i.Host,
		&i.Port,
		&i.UserName,
		&i.Password,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetAllProxies = `-- name: GetAllProxies :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies
ORDER BY
    CASE WHEN $3 = 'id' THEN id END,
    CASE WHEN $3 = 'host' THEN host END,
    CASE WHEN $3 = 'created_at' THEN created_at END,
    CASE WHEN $3 = '' OR $3 IS NULL THEN created_at END DESC
LIMIT $1 OFFSET $2
`

type GetAllProxiesParams struct {
	Limit   int32       `db:"limit" json:"limit"`
	Offset  int32       `db:"offset" json:"offset"`
	Column3 interface{} `db:"column_3" json:"column_3"`
}

func (q *Queries) GetAllProxies(ctx context.Context, arg GetAllProxiesParams) ([]*Proxy, error) {
	rows, err := q.db.Query(ctx, GetAllProxies, arg.Limit, arg.Offset, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Proxy{}
	for rows.Next() {
		var i Proxy
		if err := rows.Scan(
			&i.ID,
			&i.Host,
			&i.Port,
			&i.UserName,
			&i.Password,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetAvailableProxies = `-- name: GetAvailableProxies :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies
WHERE is_active = true AND is_use = false
ORDER BY last_used_at ASC NULLS FIRST
LIMIT $1
`

func (q *Queries) GetAvailableProxies(ctx context.Context, limit int32) ([]*Proxy, error) {
	rows, err := q.db.Query(ctx, GetAvailableProxies, limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Proxy{}
	for rows.Next() {
		var i Proxy
		if err := rows.Scan(
			&i.ID,
			&i.Host,
			&i.Port,
			&i.UserName,
			&i.Password,
			&i.IsActive,
			&i.IsUse,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const UpdateProxy = `-- name: UpdateProxy :one
UPDATE proxies SET
                   host = $2,
                   port = $3,
                   user_name = $4,
                   password = $5,
                   is_active = $6,
                   is_use = $7,
                   last_used_at = $8,
                   updated_at = CURRENT_TIMESTAMP
WHERE id = $1
    RETURNING id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
`

type UpdateProxyParams struct {
	ID         int32              `db:"id" json:"id"`
	Host       string             `db:"host" json:"host"`
	Port       int32              `db:"port" json:"port"`
	UserName   string             `db:"user_name" json:"user_name"`
	Password   string             `db:"password" json:"password"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
}

func (q *Queries) UpdateProxy(ctx context.Context, arg UpdateProxyParams) (*Proxy, error) {
	row := q.db.QueryRow(ctx, UpdateProxy,
		arg.ID,
		arg.Host,
		arg.Port,
		arg.UserName,
		arg.Password,
		arg.IsActive,
		arg.IsUse,
		arg.LastUsedAt,
	)
	var i Proxy
	err := row.Scan(
		&i.ID,
		&i.Host,
		&i.Port,
		&i.UserName,
		&i.Password,
		&i.IsActive,
		&i.IsUse,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
