package queryauthcral

import "context"

type RepositoryQueryAuth interface {
	AllAuth(ctx context.Context, query AllAuthCrawlQuery) ([]*AuthCrawl, error)
	GetAuthCrawlById(ctx context.Context, id int) (*AuthCrawl, error)
	GetAuthCrawlByMarketId(ctx context.Context, marketId int) (*AuthCrawl, error)
	GetAvailableAuthCrawlByMarket(ctx context.Context, marketId int, limit int) ([]*AuthCrawl, error)
	CountAuthCrawl(ctx context.Context) (int64, error)
}
