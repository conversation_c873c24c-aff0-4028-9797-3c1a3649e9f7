package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
	"time"
)

type UpdateAuth struct {
	ID        int
	MarketID  int
	AuthType  string
	Value     string
	ExpiredAt *time.Time
	UpdatedBy string
}

type UpdateAuthHandler decorator.CommandHandler[UpdateAuth]

type updateAuth<PERSON>and<PERSON> struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewUpdateAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateAuth](
		updateAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateAuthHandler) Handle(ctx context.Context, cmd UpdateAuth) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Auth) (*entity.Auth, error) {
		err := p.UpdateInfo(cmd.MarketID, cmd.AuthType, cmd.Value, cmd.ExpiredAt)
		if err != nil {
			return nil, err
		}
		return p, nil
	})

}
