package querymarket

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetMarketByIdQuery struct {
	Id int
}

type GetMarketByIdQueryHandler decorator.QueryHandler[GetMarketByIdQuery, *Market]

type getMarketByIdQueryHandler struct {
	repo MarketReadModel
}

func NewGetMarketByIdQueryHandler(
	repo MarketReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetMarketByIdQueryHandler {

	return decorator.ApplyQueryDecorators[GetMarketByIdQuery, *Market](
		getMarketByIdQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getMarketByIdQueryHandler) Handle(ctx context.Context, query GetMarketByIdQuery) (*Market, error) {
	return h.repo.GetMarketById(ctx, query.Id)
}
