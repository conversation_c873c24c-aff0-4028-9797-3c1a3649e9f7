package repository

import (
	"github.com/jackc/pgx/v5/pgtype"
	"go_core_market/internal/crawler/adapter/database/configcrawl/sqlc"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	"go_core_market/internal/crawler/domain/entity"
	"time"
)

// FromSQLCModel converts sqlc.CrawlConfig to domain.CrawlConfig
func FromSQLCModel(sqlcConfig *sqlc.CrawlConfig) (*entity.CrawlConfig, error) {
	var lastUsedAt time.Time
	if sqlcConfig.LastUsedAt.Valid {
		lastUsedAt = sqlcConfig.LastUsedAt.Time
	}
	target := entity.ReBuildConfigParams{
		ID:                sqlcConfig.ID,
		MarketID:          sqlcConfig.MarketID,
		Description:       sqlcConfig.Description,
		TypeConfig:        sqlcConfig.TypeConfig,
		RequestsPerMinute: sqlcConfig.RequestsPerMinute,
		RequireAuth:       sqlcConfig.RequireAuth,
		MaxNumberAuth:     sqlcConfig.MaxNumberAuth,
		MaxNumberProxy:    sqlcConfig.MaxNumberProxy,
		PerRequestDelay:   sqlcConfig.PerRequestDelaySeconds,
		Timeout:           sqlcConfig.TimeoutSeconds,
		MaxRetries:        sqlcConfig.MaxRetries,
		RetryDelay:        int(sqlcConfig.RetryDelaySeconds),
		MaxConcurrent:     int(sqlcConfig.MaxConcurrent),
		IsActive:          sqlcConfig.IsActive,
		LastUsedAt:        &lastUsedAt,
	}

	return entity.RebuildConfig(target)
}

// FromSQLCModels converts slice of sqlc.CrawlConfig to slice of domain.CrawlConfig
func FromSQLCModels(sqlcConfigs []*sqlc.CrawlConfig) ([]*entity.CrawlConfig, error) {
	configs := make([]*entity.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		c, err := FromSQLCModel(sqlcConfig)
		if err != nil {
			return nil, err
		}
		configs[i] = c
	}
	return configs, nil
}

func ToCreateParams(config *entity.CrawlConfig) (*sqlc.CreateParams, error) {
	return &sqlc.CreateParams{
		MarketID:               config.MarketID(),
		Description:            config.Description(),
		TypeConfig:             string(config.TypeConfig()),
		RequestsPerMinute:      config.RequestsPerMinute(),
		RequireAuth:            config.RequireAuth(),
		MaxNumberAuth:          config.MaxNumberAuth(),
		MaxNumberProxy:         config.MaxNumberProxy(),
		PerRequestDelaySeconds: int(config.PerRequestDelay().Seconds()),
		TimeoutSeconds:         int(config.Timeout().Seconds()),
		MaxRetries:             config.MaxRetries(),
		RetryDelaySeconds:      int32(config.RetryDelay().Seconds()),
		MaxConcurrent:          int32(config.MaxConcurrent()),
		IsActive:               config.IsActive(),
	}, nil
}

func ToUpdateCrawlConfigParams(config *entity.CrawlConfig) (*sqlc.UpdateCrawlConfigParams, error) {
	lastUsedAt := pgtype.Timestamptz{}
	err := lastUsedAt.Scan(config.LastUsedAt()) // hoặc: lastUsedAt.Set(config.LastUsedAt()) nếu dùng pgtype.v2
	if err != nil {
		return nil, err
	}

	return &sqlc.UpdateCrawlConfigParams{
		ID:                     config.ID(),
		MarketID:               config.MarketID(),
		Description:            config.Description(),
		TypeConfig:             string(config.TypeConfig()),
		RequestsPerMinute:      config.RequestsPerMinute(),
		RequireAuth:            config.RequireAuth(),
		MaxNumberAuth:          config.MaxNumberAuth(),
		MaxNumberProxy:         config.MaxNumberProxy(),
		PerRequestDelaySeconds: int(config.PerRequestDelay().Seconds()),
		TimeoutSeconds:         int(config.Timeout().Seconds()),
		MaxRetries:             config.MaxRetries(),
		RetryDelaySeconds:      int32(config.RetryDelay().Seconds()),
		MaxConcurrent:          int32(config.MaxConcurrent()),
		IsActive:               config.IsActive(),
		LastUsedAt:             lastUsedAt,
	}, nil
}

func ToCreateManyParams(configs []*entity.CrawlConfig) ([]sqlc.CreateManyParams, error) {
	params := make([]sqlc.CreateManyParams, len(configs))
	for i, config := range configs {
		param, err := ToCreateParams(config)
		if err != nil {
			return nil, err
		}
		params[i] = sqlc.CreateManyParams{
			MarketID:               param.MarketID,
			Description:            param.Description,
			TypeConfig:             param.TypeConfig,
			RequestsPerMinute:      param.RequestsPerMinute,
			RequireAuth:            param.RequireAuth,
			MaxNumberAuth:          param.MaxNumberAuth,
			MaxNumberProxy:         param.MaxNumberProxy,
			PerRequestDelaySeconds: param.PerRequestDelaySeconds,
			TimeoutSeconds:         param.TimeoutSeconds,
			MaxRetries:             param.MaxRetries,
			RetryDelaySeconds:      param.RetryDelaySeconds,
			MaxConcurrent:          param.MaxConcurrent,
			IsActive:               param.IsActive,
		}
	}
	return params, nil
}

// FromSQLCModelToQueryModel converts sqlc.CrawlConfig to query.CrawlConfig
func FromSQLCModelToQueryModel(sqlcConfig *sqlc.CrawlConfig) (*querycrawlconfig.CrawlConfig, error) {
	var lastUsedAt time.Time

	if sqlcConfig.LastUsedAt.Valid {
		lastUsedAt = sqlcConfig.LastUsedAt.Time
	}

	return &querycrawlconfig.CrawlConfig{
		Id:                     sqlcConfig.ID,
		MarketId:               sqlcConfig.MarketID,
		Description:            sqlcConfig.Description,
		TypeConfig:             sqlcConfig.TypeConfig,
		RequestsPerMinute:      sqlcConfig.RequestsPerMinute,
		RequireAuth:            sqlcConfig.RequireAuth,
		MaxNumberAuth:          sqlcConfig.MaxNumberAuth,
		MaxNumberProxy:         sqlcConfig.MaxNumberProxy,
		PerRequestDelaySeconds: sqlcConfig.PerRequestDelaySeconds,
		TimeoutSeconds:         sqlcConfig.TimeoutSeconds,
		MaxRetries:             sqlcConfig.MaxRetries,
		RetryDelaySeconds:      int(sqlcConfig.RetryDelaySeconds),
		MaxConcurrent:          int(sqlcConfig.MaxConcurrent),
		IsActive:               sqlcConfig.IsActive,
		LastUsedAt:             &lastUsedAt,
		CreatedAt:              sqlcConfig.CreatedAt.Time,
		UpdatedAt:              sqlcConfig.UpdatedAt.Time,
	}, nil
}

// FromSQLCModelsToQueryModels converts slice of sqlc.CrawlConfig to slice of query.CrawlConfig
func FromSQLCModelsToQueryModels(sqlcConfigs []*sqlc.CrawlConfig) ([]*querycrawlconfig.CrawlConfig, error) {
	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, err
		}
		configs[i] = config
	}
	return configs, nil
}
