package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ReleaseProxy struct {
	ID         int
	ReleasedBy string
}

type ReleaseProxyHandler decorator.CommandHandler[ReleaseProxy]

type releaseProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewReleaseProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ReleaseProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ReleaseProxy](
		releaseProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h releaseProxyHandler) Handle(ctx context.Context, cmd ReleaseProxy) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Proxy) (*entity.Proxy, error) {
		err := p.Release()
		if err != nil {
			return nil, err
		}
		return p, nil
	})
}

type ReleaseManyProxies struct {
	IDs        []int
	ReleasedBy string
}

type ReleaseManyProxiesHandler decorator.CommandHandler[ReleaseManyProxies]

type releaseManyProxiesHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewReleaseManyProxiesHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ReleaseManyProxiesHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ReleaseManyProxies](
		releaseManyProxiesHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h releaseManyProxiesHandler) Handle(ctx context.Context, cmd ReleaseManyProxies) error {
	return h.repo.UpdateMany(ctx, cmd.IDs, func(p []*entity.Proxy) ([]*entity.Proxy, error) {
		for _, proxy := range p {
			err := proxy.Release()
			if err != nil {
				return nil, err
			}
		}
		return p, nil
	})
}
