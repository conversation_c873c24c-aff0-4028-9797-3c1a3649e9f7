package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CreateProxyBatch struct {
	Proxies   []CreateProxy
	CreatedBy string
}

type CreateProxyBatchHandler decorator.CommandHandler[CreateProxyBatch]

type createProxyBatchHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewCreateProxyBatchHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateProxyBatchHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateProxyBatch](
		createProxyBatchHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createProxyBatchHandler) Handle(ctx context.Context, cmd CreateProxyBatch) error {
	// Create domain proxies
	var proxies []*entity.Proxy
	for _, proxyCmd := range cmd.Proxies {
		proxyParams := entity.ProxyParams{
			Host:     proxyCmd.Host,
			Port:     proxyCmd.Port,
			UserName: proxyCmd.UserName,
			Password: proxyCmd.Password,
			IsActive: proxyCmd.IsActive,
		}

		proxy, err := entity.NewProxy(proxyParams)
		if err != nil {
			return err
		}
		proxies = append(proxies, proxy)
	}
	return h.repo.CreateMany(ctx, proxies)
}
