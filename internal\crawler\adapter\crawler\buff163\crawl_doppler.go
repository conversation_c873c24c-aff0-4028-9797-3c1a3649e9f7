package buff163

import (
	"context"
	"encoding/json"
	"fmt"
	"go_core_market/internal/crawler/domain/value_object"
	"strconv"
)

type DopplerItemPrice struct {
	Phase string `json:"phase"`
	Price string `json:"price"`
	Num   int    `json:"num"`
}

func (c *Buff163Crawler) CrawlDoppler(ctx context.Context,
	id int,
	name string,
	typesDoppler []value_object.ConditionDoppler) (
	[]*value_object.CrawledPrice, error,
) {
	if c.config == nil {
		return nil, fmt.Errorf("crawler not configured")
	}
	validPhases := make(map[string]*value_object.ConditionDoppler)
	for i := range typesDoppler {
		cond := &typesDoppler[i]
		validPhases[cond.Name] = cond
	}

	var (
		allItemSells []DopplerItemPrice
		page         = 1
		total        = 1 // giả định ít nhất 1 page để bắt đầu vòng lặp
	)
	for page <= total {
		pageItems, nextTotal, err := c.fetchSellDopplerPage(ctx, id, page)
		if err != nil {
			return nil, fmt.Errorf("failed to fetch Doppler page %d: %w", page, err)
		}
		allItemSells = append(allItemSells, pageItems...)
		total = nextTotal
		page++
	}
	allItemBuys, err := c.fetchOrderItemDoppler(ctx, id)
	if err != nil {
		return nil, err
	}
	grouped := groupPricesByPhase(allItemSells, allItemBuys, validPhases)
	return PriceDopplerToCrawledPrices(grouped, validPhases, name, c.config.MarketID), nil
}
func (c *Buff163Crawler) fetchSellDopplerPage(ctx context.Context, id, page int) ([]DopplerItemPrice, int, error) {
	params := map[string]string{
		"goods_id":                strconv.Itoa(id),
		"page_num":                strconv.Itoa(page),
		"page_size":               "100",
		"game":                    "csgo",
		"appid":                   "730",
		"sort_by":                 "price.asc",
		"allow_tradable_cooldown": "1",
	}

	data, err := c.makeRequest(ctx, "/api/market/goods/sell_order", params)
	if err != nil {
		return nil, 0, err
	}

	var response BuffItemResponse[SellItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, err
	}
	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, 0, err
	}

	if response.Data == nil {
		return nil, 0, fmt.Errorf("no data in response")
	}

	var items []DopplerItemPrice
	for _, item := range response.Data.Items {
		items = append(items, DopplerItemPrice{
			Phase: normalizePhase(item.AssetInfo.Info.PhaseData.Name),
			Price: item.Price,
			Num:   1,
		})
	}

	return items, response.Data.TotalPage, nil
}

func (c *Buff163Crawler) fetchOrderItemDoppler(ctx context.Context, id int) ([]DopplerItemPrice, error) {
	params := map[string]string{
		"goods_id":       strconv.Itoa(id),
		"page_num":       strconv.Itoa(1),
		"page_size":      "100",
		"max_price_only": "yes",
		"game":           "csgo",
	}
	data, err := c.makeRequest(ctx, "/api/market/goods/buy_order", params)

	if err != nil {
		return nil, err
	}

	var response BuffItemResponse[OrderItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}

	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, err
	}

	if response.Data == nil {
		return nil, fmt.Errorf("no data in response")
	}

	var items []DopplerItemPrice
	for _, item := range response.Data.Items {
		if len(item.Specific) != 1 {
			continue
		}
		if item.Specific[0].Type == "paintwear" {
			continue
		}
		items = append(items, DopplerItemPrice{
			Phase: item.Specific[0].SimpleText,
			Price: item.Price,
			Num:   item.Num,
		})
	}
	return items, nil

}

func normalizePhase(phase string) string {
	switch phase {
	case "P1":
		return "Phase1"
	case "P2":
		return "Phase2"
	case "P3":
		return "Phase3"
	case "P4":
		return "Phase4"
	default:
		return phase
	}
}

func groupPricesByPhase(
	sells []DopplerItemPrice,
	buys []DopplerItemPrice,
	validPhases map[string]*value_object.ConditionDoppler,
) map[string]struct {
	MinSell float64
	MaxBuy  float64
	NumSell int
	NumBuy  int
} {
	grouped := make(map[string]struct {
		MinSell float64
		MaxBuy  float64
		NumSell int
		NumBuy  int
	})
	// xử lý sells
	for _, item := range sells {
		if _, ok := validPhases[item.Phase]; !ok {
			continue
		}
		sellPrice, err := strconv.ParseFloat(item.Price, 64)
		if err != nil {
			continue
		}
		grp := grouped[item.Phase]
		if grp.MinSell == 0 || sellPrice < grp.MinSell {
			grp.MinSell = sellPrice
		}
		grp.NumSell += item.Num
		grouped[item.Phase] = grp
	}
	// xử lý buys
	for _, item := range buys {
		if _, ok := validPhases[item.Phase]; !ok {
			continue
		}
		buyPrice, err := strconv.ParseFloat(item.Price, 64)
		if err != nil {
			continue
		}
		grp := grouped[item.Phase]
		if grp.MaxBuy == 0 || buyPrice > grp.MaxBuy {
			grp.MaxBuy = buyPrice
		}
		grp.NumBuy += item.Num
		grouped[item.Phase] = grp
	}
	return grouped
}
