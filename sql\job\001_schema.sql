-- Create jobs table for job domain
-- This table stores all job information including status, progress, and configuration

CREATE TABLE jobs (
id serial PRIMARY KEY,
job_type VARCHAR(50) NOT NULL,
name VARCHAR(200) NOT NULL,
description TEXT,
status VARCHAR(20) NOT NULL DEFAULT 'pending',
crawl_config_id INTEGER,
scheduled_at TIMESTAMPTZ,
started_at TIMESTAMPTZ,
completed_at TIMESTAMPTZ,

    -- Progress tracking
current_step INTEGER DEFAULT 0,
total_steps INTEGER DEFAULT 0,
progress_message TEXT,
progress_percentage INTEGER DEFAULT 0,

    -- Error handling
max_retries INTEGER NOT NULL DEFAULT 3,
retry_count INTEGER NOT NULL DEFAULT 0,
last_error TEXT,
timeout_seconds INTEGER NOT NULL DEFAULT 3600,

    -- Metadata
created_by VARCHAR(100) NOT NULL,
created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
