// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: crawl_configs.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const ActivateConfig = `-- name: ActivateConfig :one
UPDATE crawl_configs
SET is_active = true
WHERE id = $1
RETURNING id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at
`

func (q *Queries) ActivateConfig(ctx context.Context, id int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, ActivateConfig, id)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.<PERSON>um<PERSON>uth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const CountActiveCrawlConfigsQuery = `-- name: CountActiveCrawlConfigsQuery :one
SELECT COUNT(*) FROM crawl_configs WHERE is_active = true
`

func (q *Queries) CountActiveCrawlConfigsQuery(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountActiveCrawlConfigsQuery)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountCrawlConfigsByMarketQuery = `-- name: CountCrawlConfigsByMarketQuery :one
SELECT COUNT(*) FROM crawl_configs WHERE market_id = $1
`

func (q *Queries) CountCrawlConfigsByMarketQuery(ctx context.Context, marketID int) (int64, error) {
	row := q.db.QueryRow(ctx, CountCrawlConfigsByMarketQuery, marketID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountCrawlConfigsQuery = `-- name: CountCrawlConfigsQuery :one
SELECT COUNT(*) FROM crawl_configs
`

func (q *Queries) CountCrawlConfigsQuery(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountCrawlConfigsQuery)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const Create = `-- name: Create :one
INSERT INTO crawl_configs (
    market_id,
    description,
    type_config,
    requests_per_minute,
    require_auth,
    max_number_auth,
    max_number_proxy,
    per_request_delay_seconds,
    timeout_seconds,
    max_retries,
    retry_delay_seconds,
    max_concurrent,
    is_active
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13
)
RETURNING id
`

type CreateParams struct {
	MarketID               int    `db:"market_id" json:"market_id"`
	Description            string `db:"description" json:"description"`
	TypeConfig             string `db:"type_config" json:"type_config"`
	RequestsPerMinute      int    `db:"requests_per_minute" json:"requests_per_minute"`
	RequireAuth            bool   `db:"require_auth" json:"require_auth"`
	MaxNumberAuth          int    `db:"max_number_auth" json:"max_number_auth"`
	MaxNumberProxy         int    `db:"max_number_proxy" json:"max_number_proxy"`
	PerRequestDelaySeconds int    `db:"per_request_delay_seconds" json:"per_request_delay_seconds"`
	TimeoutSeconds         int    `db:"timeout_seconds" json:"timeout_seconds"`
	MaxRetries             int    `db:"max_retries" json:"max_retries"`
	RetryDelaySeconds      int32  `db:"retry_delay_seconds" json:"retry_delay_seconds"`
	MaxConcurrent          int32  `db:"max_concurrent" json:"max_concurrent"`
	IsActive               bool   `db:"is_active" json:"is_active"`
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (int, error) {
	row := q.db.QueryRow(ctx, Create,
		arg.MarketID,
		arg.Description,
		arg.TypeConfig,
		arg.RequestsPerMinute,
		arg.RequireAuth,
		arg.MaxNumberAuth,
		arg.MaxNumberProxy,
		arg.PerRequestDelaySeconds,
		arg.TimeoutSeconds,
		arg.MaxRetries,
		arg.RetryDelaySeconds,
		arg.MaxConcurrent,
		arg.IsActive,
	)
	var id int
	err := row.Scan(&id)
	return id, err
}

type CreateManyParams struct {
	MarketID               int    `db:"market_id" json:"market_id"`
	Description            string `db:"description" json:"description"`
	TypeConfig             string `db:"type_config" json:"type_config"`
	RequestsPerMinute      int    `db:"requests_per_minute" json:"requests_per_minute"`
	RequireAuth            bool   `db:"require_auth" json:"require_auth"`
	MaxNumberAuth          int    `db:"max_number_auth" json:"max_number_auth"`
	MaxNumberProxy         int    `db:"max_number_proxy" json:"max_number_proxy"`
	PerRequestDelaySeconds int    `db:"per_request_delay_seconds" json:"per_request_delay_seconds"`
	TimeoutSeconds         int    `db:"timeout_seconds" json:"timeout_seconds"`
	MaxRetries             int    `db:"max_retries" json:"max_retries"`
	RetryDelaySeconds      int32  `db:"retry_delay_seconds" json:"retry_delay_seconds"`
	MaxConcurrent          int32  `db:"max_concurrent" json:"max_concurrent"`
	IsActive               bool   `db:"is_active" json:"is_active"`
}

const DeactivateConfig = `-- name: DeactivateConfig :one
UPDATE crawl_configs
SET is_active = false
WHERE id = $1
RETURNING id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at
`

func (q *Queries) DeactivateConfig(ctx context.Context, id int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, DeactivateConfig, id)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const DeleteCrawlConfig = `-- name: DeleteCrawlConfig :exec
DELETE FROM crawl_configs WHERE id = $1
`

func (q *Queries) DeleteCrawlConfig(ctx context.Context, id int) error {
	_, err := q.db.Exec(ctx, DeleteCrawlConfig, id)
	return err
}

const FilterCrawlConfigsQuery = `-- name: FilterCrawlConfigsQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs
WHERE
    ($1 = 0 OR market_id = $1)
    AND ($2 = '' OR type_config = $2)
    AND ($3::boolean IS NULL OR is_active = $3)
    AND ($4::boolean IS NULL OR require_auth = $4)
    AND ($5::timestamptz IS NULL OR created_at >= $5)
    AND ($6::timestamptz IS NULL OR created_at <= $6)
ORDER BY
    CASE WHEN $7 = 'description' THEN description END ASC,
    CASE WHEN $7 = 'description_desc' THEN description END DESC,
    CASE WHEN $7 = 'created_at' THEN created_at END ASC,
    CASE WHEN $7 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $7 = 'last_used_at' THEN last_used_at END ASC,
    CASE WHEN $7 = 'last_used_at_desc' THEN last_used_at END DESC,
    CASE WHEN $7 = '' OR $7 IS NULL THEN id END ASC
`

type FilterCrawlConfigsQueryParams struct {
	Column1 interface{}        `db:"column_1" json:"column_1"`
	Column2 interface{}        `db:"column_2" json:"column_2"`
	Column3 bool               `db:"column_3" json:"column_3"`
	Column4 bool               `db:"column_4" json:"column_4"`
	Column5 pgtype.Timestamptz `db:"column_5" json:"column_5"`
	Column6 pgtype.Timestamptz `db:"column_6" json:"column_6"`
	Column7 interface{}        `db:"column_7" json:"column_7"`
}

func (q *Queries) FilterCrawlConfigsQuery(ctx context.Context, arg FilterCrawlConfigsQueryParams) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, FilterCrawlConfigsQuery,
		arg.Column1,
		arg.Column2,
		arg.Column3,
		arg.Column4,
		arg.Column5,
		arg.Column6,
		arg.Column7,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const FindByID = `-- name: FindByID :one
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs WHERE id = $1 LIMIT 1
`

func (q *Queries) FindByID(ctx context.Context, id int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, FindByID, id)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetActiveCrawlConfigsByMarketAndTypeQuery = `-- name: GetActiveCrawlConfigsByMarketAndTypeQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE market_id = $1 AND type_config = $2 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST
`

type GetActiveCrawlConfigsByMarketAndTypeQueryParams struct {
	MarketID   int    `db:"market_id" json:"market_id"`
	TypeConfig string `db:"type_config" json:"type_config"`
}

func (q *Queries) GetActiveCrawlConfigsByMarketAndTypeQuery(ctx context.Context, arg GetActiveCrawlConfigsByMarketAndTypeQueryParams) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetActiveCrawlConfigsByMarketAndTypeQuery, arg.MarketID, arg.TypeConfig)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetActiveCrawlConfigsByMarketQuery = `-- name: GetActiveCrawlConfigsByMarketQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE market_id = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST
`

func (q *Queries) GetActiveCrawlConfigsByMarketQuery(ctx context.Context, marketID int) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetActiveCrawlConfigsByMarketQuery, marketID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetActiveCrawlConfigsByTypeQuery = `-- name: GetActiveCrawlConfigsByTypeQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE type_config = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST
`

func (q *Queries) GetActiveCrawlConfigsByTypeQuery(ctx context.Context, typeConfig string) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetActiveCrawlConfigsByTypeQuery, typeConfig)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetActiveCrawlConfigsQuery = `-- name: GetActiveCrawlConfigsQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs WHERE is_active = true ORDER BY last_used_at ASC NULLS FIRST
`

func (q *Queries) GetActiveCrawlConfigsQuery(ctx context.Context) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetActiveCrawlConfigsQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetAllCrawlConfigsWithPagination = `-- name: GetAllCrawlConfigsWithPagination :many

SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs
ORDER BY
    CASE WHEN $3 = 'description' THEN description END ASC,
    CASE WHEN $3 = 'description_desc' THEN description END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = 'last_used_at' THEN last_used_at END ASC,
    CASE WHEN $3 = 'last_used_at_desc' THEN last_used_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2
`

type GetAllCrawlConfigsWithPaginationParams struct {
	Limit   int32       `db:"limit" json:"limit"`
	Offset  int32       `db:"offset" json:"offset"`
	Column3 interface{} `db:"column_3" json:"column_3"`
}

// Query methods for CrawlConfigQueryRepository
func (q *Queries) GetAllCrawlConfigsWithPagination(ctx context.Context, arg GetAllCrawlConfigsWithPaginationParams) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetAllCrawlConfigsWithPagination, arg.Limit, arg.Offset, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetCrawlConfigByIdQuery = `-- name: GetCrawlConfigByIdQuery :one
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs WHERE id = $1 LIMIT 1
`

func (q *Queries) GetCrawlConfigByIdQuery(ctx context.Context, id int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, GetCrawlConfigByIdQuery, id)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetCrawlConfigsByMarketIdQuery = `-- name: GetCrawlConfigsByMarketIdQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs WHERE market_id = $1 ORDER BY created_at DESC
`

func (q *Queries) GetCrawlConfigsByMarketIdQuery(ctx context.Context, marketID int) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetCrawlConfigsByMarketIdQuery, marketID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetCrawlConfigsByTypeQuery = `-- name: GetCrawlConfigsByTypeQuery :many
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs WHERE type_config = $1 ORDER BY created_at DESC
`

func (q *Queries) GetCrawlConfigsByTypeQuery(ctx context.Context, typeConfig string) ([]*CrawlConfig, error) {
	rows, err := q.db.Query(ctx, GetCrawlConfigsByTypeQuery, typeConfig)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*CrawlConfig{}
	for rows.Next() {
		var i CrawlConfig
		if err := rows.Scan(
			&i.ID,
			&i.MarketID,
			&i.Description,
			&i.TypeConfig,
			&i.RequestsPerMinute,
			&i.RequireAuth,
			&i.MaxNumberAuth,
			&i.MaxNumberProxy,
			&i.PerRequestDelaySeconds,
			&i.TimeoutSeconds,
			&i.MaxRetries,
			&i.RetryDelaySeconds,
			&i.MaxConcurrent,
			&i.IsActive,
			&i.LastUsedAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetLeastUsedActiveConfigByMarketQuery = `-- name: GetLeastUsedActiveConfigByMarketQuery :one
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE market_id = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1
`

func (q *Queries) GetLeastUsedActiveConfigByMarketQuery(ctx context.Context, marketID int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, GetLeastUsedActiveConfigByMarketQuery, marketID)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetLeastUsedActiveConfigByTypeQuery = `-- name: GetLeastUsedActiveConfigByTypeQuery :one
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE type_config = $1 AND is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1
`

func (q *Queries) GetLeastUsedActiveConfigByTypeQuery(ctx context.Context, typeConfig string) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, GetLeastUsedActiveConfigByTypeQuery, typeConfig)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetLeastUsedActiveConfigQuery = `-- name: GetLeastUsedActiveConfigQuery :one
SELECT id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at FROM crawl_configs 
WHERE is_active = true 
ORDER BY last_used_at ASC NULLS FIRST 
LIMIT 1
`

func (q *Queries) GetLeastUsedActiveConfigQuery(ctx context.Context) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, GetLeastUsedActiveConfigQuery)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateCrawlConfig = `-- name: UpdateCrawlConfig :one
UPDATE crawl_configs
SET market_id = $2,
    description = $3,
    type_config = $4,
    requests_per_minute = $5,
    require_auth = $6,
    max_number_auth = $7,
    max_number_proxy = $8,
    per_request_delay_seconds = $9,
    timeout_seconds = $10,
    max_retries = $11,
    retry_delay_seconds = $12,
    max_concurrent = $13,
    is_active = $14,
    last_used_at = $15
WHERE id = $1
RETURNING id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at
`

type UpdateCrawlConfigParams struct {
	ID                     int                `db:"id" json:"id"`
	MarketID               int                `db:"market_id" json:"market_id"`
	Description            string             `db:"description" json:"description"`
	TypeConfig             string             `db:"type_config" json:"type_config"`
	RequestsPerMinute      int                `db:"requests_per_minute" json:"requests_per_minute"`
	RequireAuth            bool               `db:"require_auth" json:"require_auth"`
	MaxNumberAuth          int                `db:"max_number_auth" json:"max_number_auth"`
	MaxNumberProxy         int                `db:"max_number_proxy" json:"max_number_proxy"`
	PerRequestDelaySeconds int                `db:"per_request_delay_seconds" json:"per_request_delay_seconds"`
	TimeoutSeconds         int                `db:"timeout_seconds" json:"timeout_seconds"`
	MaxRetries             int                `db:"max_retries" json:"max_retries"`
	RetryDelaySeconds      int32              `db:"retry_delay_seconds" json:"retry_delay_seconds"`
	MaxConcurrent          int32              `db:"max_concurrent" json:"max_concurrent"`
	IsActive               bool               `db:"is_active" json:"is_active"`
	LastUsedAt             pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
}

func (q *Queries) UpdateCrawlConfig(ctx context.Context, arg UpdateCrawlConfigParams) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, UpdateCrawlConfig,
		arg.ID,
		arg.MarketID,
		arg.Description,
		arg.TypeConfig,
		arg.RequestsPerMinute,
		arg.RequireAuth,
		arg.MaxNumberAuth,
		arg.MaxNumberProxy,
		arg.PerRequestDelaySeconds,
		arg.TimeoutSeconds,
		arg.MaxRetries,
		arg.RetryDelaySeconds,
		arg.MaxConcurrent,
		arg.IsActive,
		arg.LastUsedAt,
	)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateLastUsedAt = `-- name: UpdateLastUsedAt :one
UPDATE crawl_configs
SET last_used_at = NOW()
WHERE id = $1
RETURNING id, market_id, description, type_config, requests_per_minute, require_auth, max_number_auth, max_number_proxy, per_request_delay_seconds, timeout_seconds, max_retries, retry_delay_seconds, max_concurrent, is_active, last_used_at, created_at, updated_at
`

func (q *Queries) UpdateLastUsedAt(ctx context.Context, id int) (*CrawlConfig, error) {
	row := q.db.QueryRow(ctx, UpdateLastUsedAt, id)
	var i CrawlConfig
	err := row.Scan(
		&i.ID,
		&i.MarketID,
		&i.Description,
		&i.TypeConfig,
		&i.RequestsPerMinute,
		&i.RequireAuth,
		&i.MaxNumberAuth,
		&i.MaxNumberProxy,
		&i.PerRequestDelaySeconds,
		&i.TimeoutSeconds,
		&i.MaxRetries,
		&i.RetryDelaySeconds,
		&i.MaxConcurrent,
		&i.IsActive,
		&i.LastUsedAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
