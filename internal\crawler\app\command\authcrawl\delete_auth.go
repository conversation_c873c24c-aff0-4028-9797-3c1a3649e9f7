package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeleteAuth struct {
	ID        int
	DeletedBy string
}

type Delete<PERSON>uth<PERSON><PERSON>ler decorator.CommandHandler[DeleteAuth]

type deleteA<PERSON><PERSON>andler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewDeleteAuthHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeleteAuthHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeleteAuth](
		deleteAuthHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deleteAuthHandler) Handle(ctx context.Context, cmd DeleteAuth) error {
	return h.repo.Delete(ctx, cmd.ID)
}
