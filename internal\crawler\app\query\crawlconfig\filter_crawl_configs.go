package querycrawlconfig

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"time"
)

type FilterCrawlConfigsQuery struct {
	MarketId    int
	TypeConfig  string
	IsActive    *bool
	RequireAuth *bool
	CreatedFrom *time.Time
	CreatedTo   *time.Time
	OrderBy     string
}

type FilterCrawlConfigsQueryHandler decorator.QueryHandler[FilterCrawlConfigsQuery, []*CrawlConfig]

type filterCrawlConfigsQueryHandler struct {
	repo CrawlConfigReadModel
}

func NewFilterCrawlConfigsQueryHandler(
	repo CrawlConfigReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) FilterCrawlConfigsQueryHandler {

	return decorator.ApplyQueryDecorators[FilterCrawlConfigsQuery, []*CrawlConfig](
		filterCrawlConfigsQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h filterCrawlConfigsQueryHandler) Handle(ctx context.Context, query FilterCrawlConfigsQuery) ([]*CrawlConfig, error) {
	return h.repo.FilterCrawlConfigs(ctx, query)
}
