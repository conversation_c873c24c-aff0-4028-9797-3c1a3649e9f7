package queryauthcral

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetAvailableAuthCrawlByMarketQuery struct {
	MarketId int
	Limit    int
}

type GetAvailableAuthCrawlByMarketQueryHandler decorator.QueryHandler[GetAvailableAuthCrawlByMarketQuery, []*AuthCrawl]

type getAvailableAuthCrawlByMarketQueryHandler struct {
	repo RepositoryQueryAuth
}

func NewGetAvailableAuthCrawlByMarketQueryHandler(
	repo RepositoryQueryAuth,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAvailableAuthCrawlByMarketQueryHandler {

	return decorator.ApplyQueryDecorators[GetAvailableAuthCrawlByMarketQuery, []*AuthCrawl](
		getAvailableAuthCrawlByMarketQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAvailableAuthCrawlByMarketQueryHandler) Handle(ctx context.Context, query GetAvailableAuthCrawlByMarketQuery) ([]*AuthCrawl, error) {
	return h.repo.GetAvailableAuthCrawlByMarket(ctx, query.MarketId, query.Limit)
}
