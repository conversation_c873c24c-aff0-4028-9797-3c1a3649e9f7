package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type SetMarketMaintenance struct {
	MarketId     int
	MaintainedBy string
}

type SetMarketMaintenanceHandler decorator.CommandHandler[SetMarketMaintenance]

type setMarketMaintenanceHandler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewSetMarketMaintenanceHandler(
	repo repository.RepositoryMarket,
	log logger.Logger,
	client decorator.MetricsClient,
	broker message.Broker,
) SetMarketMaintenanceHandler {
	return decorator.ApplyCommandDecorators[SetMarketMaintenance](
		setMarketMaintenanceHandler{
			repo:   repo,
			broker: broker,
		},
		log,
		client,
	)
}

func (h setMarketMaintenanceHandler) Handle(ctx context.Context, cmd SetMarketMaintenance) error {
	return h.repo.Update(ctx, cmd.MarketId, func(m *entity.Market) (*entity.Market, error) {
		err := m.SetMaintenance()
		if err != nil {
			return nil, err
		}
		return m, nil
	})
}
