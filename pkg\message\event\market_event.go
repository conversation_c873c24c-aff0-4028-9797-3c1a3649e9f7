package event

import (
	"time"
)

// ====== Event Types Constants ======
const (
	MarketCreatedEventType     = "market.created"
	MarketActivatedEventType   = "market.activated"
	MarketDeactivatedEventType = "market.deactivated"
	MarketMaintenanceEventType = "market.maintenance"
	MarketDeprecatedEventType  = "market.deprecated"
	MarketCrawledEventType     = "market.crawled"
	MarketUpdatedEventType     = "market.updated"
)

// ====== Market Created Event ======
type MarketCreatedData struct {
	MarketID         int       `json:"market_id"`
	Name             string    `json:"name"`
	DisplayName      string    `json:"display_name"`
	Type             string    `json:"type"`
	Status           string    `json:"status"`
	BaseURL          string    `json:"base_url"`
	Currency         string    `json:"currency"`
	BuyerFeePercent  float64   `json:"buyer_fee_percent"`
	SellerFeePercent float64   `json:"seller_fee_percent"`
	CountryCode      string    `json:"country_code,omitempty"`
	Language         string    `json:"language,omitempty"`
	Description      string    `json:"description,omitempty"`
	IsActive         bool      `json:"is_active"`
	CreatedAt        time.Time `json:"created_at"`
}

type MarketCreatedEvent struct {
	*BaseEvent[MarketCreatedData]
}

func NewMarketCreatedEvent(aggregateID string, data MarketCreatedData) *MarketCreatedEvent {
	return &MarketCreatedEvent{
		BaseEvent: NewBaseEvent(MarketCreatedEventType, aggregateID, data),
	}
}

// ====== Market Activated Event ======
type MarketActivatedData struct {
	MarketID    int       `json:"market_id"`
	Name        string    `json:"name"`
	ActivatedAt time.Time `json:"activated_at"`
	ActivatedBy string    `json:"activated_by,omitempty"` // Optional: who/what activated it
	Reason      string    `json:"reason,omitempty"`       // Optional: reason for activation
}

type MarketActivatedEvent struct {
	*BaseEvent[MarketActivatedData]
}

func NewMarketActivatedEvent(aggregateID string, data MarketActivatedData) *MarketActivatedEvent {
	return &MarketActivatedEvent{
		BaseEvent: NewBaseEvent(MarketActivatedEventType, aggregateID, data),
	}
}

// ====== Market Deactivated Event ======
type MarketDeactivatedData struct {
	MarketID      int       `json:"market_id"`
	Name          string    `json:"name"`
	DeactivatedAt time.Time `json:"deactivated_at"`
	DeactivatedBy string    `json:"deactivated_by,omitempty"` // Optional: who/what deactivated it
	Reason        string    `json:"reason,omitempty"`         // Optional: reason for deactivation
}

type MarketDeactivatedEvent struct {
	*BaseEvent[MarketDeactivatedData]
}

func NewMarketDeactivatedEvent(aggregateID string, data MarketDeactivatedData) *MarketDeactivatedEvent {
	return &MarketDeactivatedEvent{
		BaseEvent: NewBaseEvent(MarketDeactivatedEventType, aggregateID, data),
	}
}

// ====== Market Maintenance Event ======
type MarketMaintenanceData struct {
	MarketID      int       `json:"market_id"`
	Name          string    `json:"name"`
	MaintenanceAt time.Time `json:"maintenance_at"`
	MaintenanceBy string    `json:"maintenance_by,omitempty"` // Optional: who/what set maintenance
	Reason        string    `json:"reason,omitempty"`         // Optional: reason for maintenance
}

type MarketMaintenanceEvent struct {
	*BaseEvent[MarketMaintenanceData]
}

func NewMarketMaintenanceEvent(aggregateID string, data MarketMaintenanceData) *MarketMaintenanceEvent {
	return &MarketMaintenanceEvent{
		BaseEvent: NewBaseEvent(MarketMaintenanceEventType, aggregateID, data),
	}
}

// ====== Market Deprecated Event ======
type MarketDeprecatedData struct {
	MarketID     int       `json:"market_id"`
	Name         string    `json:"name"`
	DeprecatedAt time.Time `json:"deprecated_at"`
	DeprecatedBy string    `json:"deprecated_by,omitempty"` // Optional: who/what deprecated it
	Reason       string    `json:"reason,omitempty"`        // Optional: reason for deprecation
	Replacement  string    `json:"replacement,omitempty"`   // Optional: replacement market
}

type MarketDeprecatedEvent struct {
	*BaseEvent[MarketDeprecatedData]
}

func NewMarketDeprecatedEvent(aggregateID string, data MarketDeprecatedData) *MarketDeprecatedEvent {
	return &MarketDeprecatedEvent{
		BaseEvent: NewBaseEvent(MarketDeprecatedEventType, aggregateID, data),
	}
}

// ====== Market Crawled Event ======
type MarketCrawledData struct {
	MarketID  int        `json:"market_id"`
	Name      string     `json:"name"`
	CrawledAt *time.Time `json:"crawled_at"`
}

type MarketCrawledEvent struct {
	*BaseEvent[MarketCrawledData]
}

func NewMarketCrawledEvent(aggregateID string, data MarketCrawledData) *MarketCrawledEvent {
	return &MarketCrawledEvent{
		BaseEvent: NewBaseEvent(MarketCrawledEventType, aggregateID, data),
	}
}

// ====== Market Updated Event ======
type MarketUpdatedData struct {
	MarketID  int                    `json:"market_id"`
	Name      string                 `json:"name"`
	UpdatedAt time.Time              `json:"updated_at"`
	UpdatedBy string                 `json:"updated_by,omitempty"` // Optional: who/what updated it
	Changes   map[string]interface{} `json:"changes,omitempty"`    // Optional: what fields were changed
}

type MarketUpdatedEvent struct {
	*BaseEvent[MarketUpdatedData]
}

func NewMarketUpdatedEvent(aggregateID string, data MarketUpdatedData) *MarketUpdatedEvent {
	return &MarketUpdatedEvent{
		BaseEvent: NewBaseEvent(MarketUpdatedEventType, aggregateID, data),
	}
}
