package config

import (
	"fmt"
	"github.com/caarlos0/env/v10"
	"github.com/stretchr/testify/assert/yaml"
	"os"
	"time"
)

type DatabaseConfig struct {
	Driver          string        `json:"driver" yaml:"driver" env:"DB_DRIVER" `
	Host            string        `json:"host" yaml:"host" env:"DB_HOST" `
	Port            int           `json:"port" yaml:"port" env:"DB_PORT" `
	Database        string        `json:"database" yaml:"database" env:"DB_NAME" `
	Username        string        `json:"username" yaml:"username" env:"DB_USER"`
	Password        string        `json:"password" yaml:"password" env:"DB_PASSWORD" `
	MaxOpenConns    int           `json:"max_open_conns" yaml:"max_open_conns" env:"DB_MAX_OPEN_CONNS" `
	MaxIdleConns    int           `json:"max_idle_conns" yaml:"max_idle_conns" env:"DB_MAX_IDLE_CONNS" `
	ConnMaxLifetime time.Duration `json:"conn_max_lifetime" yaml:"conn_max_lifetime" env:"DB_CONN_MAX_LIFETIME"`
	SSLMode         string        `json:"ssl_mode" yaml:"ssl_mode" env:"DB_SSL_MODE" `
}

func (c *DatabaseConfig) GetDSN() string {
	return fmt.Sprintf("%s://%s:%s@%s:%d/%s?sslmode=%s",
		c.Driver, c.Username, c.Password, c.Host, c.Port, c.Database, c.SSLMode)
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Port   int    `json:"port" yaml:"port" env:"SERVER_PORT" `
	Host   string `json:"host" yaml:"host" env:"SERVER_HOST" `
	APIKey string `json:"api_key" yaml:"api_key" env:"API_KEY" `
}

// KafkaConfig holds Kafka configuration
type KafkaConfig struct {
	Brokers        []string      `json:"brokers" yaml:"brokers" env:"KAFKA_BROKERS" `
	GroupID        string        `json:"group_id" yaml:"group_id" env:"KAFKA_GROUP_ID" `
	BatchSize      int           `json:"batch_size" yaml:"batch_size" env:"KAFKA_BATCH_SIZE" `
	BatchTimeout   time.Duration `json:"batch_timeout" yaml:"batch_timeout" env:"KAFKA_BATCH_TIMEOUT" `
	ReadBatchSize  int           `json:"read_batch_size" yaml:"read_batch_size" env:"KAFKA_READ_BATCH_SIZE" `
	CommitInterval time.Duration `json:"commit_interval" yaml:"commit_interval" env:"KAFKA_COMMIT_INTERVAL"`
	StartOffset    int64         `json:"start_offset" yaml:"start_offset" env:"KAFKA_START_OFFSET" `
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level       string        `json:"level" yaml:"level" env:"LOG_LEVEL" `
	Format      string        `json:"format" yaml:"format" env:"LOG_FORMAT" `
	Output      []string      `json:"output" yaml:"output" env:"LOG_OUTPUT" `
	FileConfig  FileLogConfig `json:"file" yaml:"file"`
	ServiceName string        `json:"service_name" yaml:"service_name" env:"SERVICE_NAME" `
	Environment string        `json:"environment" yaml:"environment" env:"ENVIRONMENT"`
}

// FileLogConfig holds file logging configuration
type FileLogConfig struct {
	Enabled    bool   `json:"enabled" yaml:"enabled" env:"LOG_FILE_ENABLED" `
	Path       string `json:"path" yaml:"path" env:"LOG_FILE_PATH" `
	MaxSize    int    `json:"max_size" yaml:"max_size" env:"LOG_FILE_MAX_SIZE" `
	MaxBackups int    `json:"max_backups" yaml:"max_backups" env:"LOG_FILE_MAX_BACKUPS" `
	MaxAge     int    `json:"max_age" yaml:"max_age" env:"LOG_FILE_MAX_AGE" `
	Compress   bool   `json:"compress" yaml:"compress" env:"LOG_FILE_COMPRESS"`
}

// BaseServiceConfig là config chung cho tất cả các service
type BaseServiceConfig struct {
	Database DatabaseConfig `json:"database" yaml:"database"`
	Server   ServerConfig   `json:"server" yaml:"server"`
	Kafka    KafkaConfig    `json:"kafka" yaml:"kafka"`
	Logger   LoggerConfig   `json:"logger" yaml:"logger"`
}

func LoadCustomConfig[T any](path string) (*T, error) {
	data, err := os.ReadFile(path)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg T
	if err := yaml.Unmarshal(data, &cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal yaml: %w", err)
	}

	if err := env.Parse(&cfg); err != nil {
		return nil, fmt.Errorf("failed to parse env vars: %w", err)
	}

	return &cfg, nil
}
