package queryproxy

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/pagination"
)

type GetAllProxiesQuery struct {
	Page     int
	PageSize int
	OrderBy  string
}

type GetAllProxiesHandler decorator.QueryHandler[GetAllProxiesQuery, *pagination.PaginatedResponse[Proxy]]

type getAllProxiesHandler struct {
	repo ProxyReadModel
}

func NewGetAllProxiesHandler(
	repo ProxyReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
) GetAllProxiesHandler {
	if repo == nil {
		panic("nil ProxyRepository")
	}

	return decorator.ApplyQueryDecorators[GetAllProxiesQuery, *pagination.PaginatedResponse[Proxy]](
		getAllProxiesHandler{repo: repo},
		logger,
		metricsClient,
	)
}

func (h getAllProxiesHandler) Handle(ctx context.Context, query GetAllProxiesQuery) (*pagination.PaginatedResponse[Proxy], error) {
	// Validate query
	p := pagination.NewPagination(query.Page, query.PageSize)

	// Get total count for pagination
	total, err := h.repo.CountProxies(ctx)
	if err != nil {
		return nil, err
	}
	p.SetTotal(total)

	// Get paginated data
	proxies, err := h.repo.AllProxies(ctx, query)
	if err != nil {
		return nil, err
	}

	return pagination.CreateResponse[Proxy](p, proxies), nil
}
