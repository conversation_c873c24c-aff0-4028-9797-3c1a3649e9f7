syntax = "proto3";

package job.v1;

option go_package = "go_core_market/pkg/pb/job";

import "google/protobuf/timestamp.proto";

// Job message
message Job {
  int32 id = 1;
  string job_type = 2;
  string name = 3;
  string description = 4;
  string status = 5;
  optional int32 crawl_config_id = 6;
  optional google.protobuf.Timestamp scheduled_at = 7;
  optional google.protobuf.Timestamp started_at = 8;
  optional google.protobuf.Timestamp completed_at = 9;
  
  // Progress tracking
  optional int32 current_step = 10;
  optional int32 total_steps = 11;
  optional string progress_message = 12;
  optional int32 progress_percentage = 13;
  
  // Error handling
  int32 max_retries = 14;
  int32 retry_count = 15;
  optional string last_error = 16;
  int32 timeout_seconds = 17;
  
  // Metadata
  string created_by = 18;
  google.protobuf.Timestamp created_at = 19;
  google.protobuf.Timestamp updated_at = 20;
}

// JobProgress message
message JobProgress {
  int32 current_step = 1;
  int32 total_steps = 2;
  string message = 3;
  int32 percentage = 4;
}

// Request/Response messages for Commands
message CreateJobRequest {
  string job_type = 1;
  string name = 2;
  string description = 3;
  optional int32 crawl_config_id = 4;
  optional google.protobuf.Timestamp scheduled_at = 5;
  int32 max_retries = 6;
  int32 timeout_seconds = 7;
  string created_by = 8;
}

message CreateJobResponse {
  bool success = 1;
  string message = 2;
  optional int32 job_id = 3;
}

message UpdateJobRequest {
  int32 id = 1;
  string job_type = 2;
  string name = 3;
  string description = 4;
  optional int32 crawl_config_id = 5;
  optional google.protobuf.Timestamp scheduled_at = 6;
  int32 max_retries = 7;
  int32 timeout_seconds = 8;
  string updated_by = 9;
}

message UpdateJobResponse {
  bool success = 1;
  string message = 2;
}

message UpdateJobStatusRequest {
  int32 id = 1;
  string status = 2;
  string updated_by = 3;
}

message UpdateJobStatusResponse {
  bool success = 1;
  string message = 2;
}

message UpdateJobProgressRequest {
  int32 id = 1;
  JobProgress progress = 2;
  string updated_by = 3;
}

message UpdateJobProgressResponse {
  bool success = 1;
  string message = 2;
}

message StartJobRequest {
  int32 id = 1;
  string started_by = 2;
}

message StartJobResponse {
  bool success = 1;
  string message = 2;
}

message CompleteJobRequest {
  int32 id = 1;
  string completed_by = 2;
}

message CompleteJobResponse {
  bool success = 1;
  string message = 2;
}

message FailJobRequest {
  int32 id = 1;
  string error_message = 2;
  string failed_by = 3;
}

message FailJobResponse {
  bool success = 1;
  string message = 2;
}

message CancelJobRequest {
  int32 id = 1;
  string cancelled_by = 2;
}

message CancelJobResponse {
  bool success = 1;
  string message = 2;
}

message RetryJobRequest {
  int32 id = 1;
  string retried_by = 2;
}

message RetryJobResponse {
  bool success = 1;
  string message = 2;
}

message DeleteJobRequest {
  int32 id = 1;
  string deleted_by = 2;
}

message DeleteJobResponse {
  bool success = 1;
  string message = 2;
}

// Request/Response messages for Queries
message GetAllJobsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string order_by = 3;
}

message GetAllJobsResponse {
  repeated Job jobs = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
  int32 total_pages = 5;
}

message GetJobByIdRequest {
  int32 id = 1;
}

message GetJobByIdResponse {
  Job job = 1;
}

message GetJobsByStatusRequest {
  string status = 1;
}

message GetJobsByStatusResponse {
  repeated Job jobs = 1;
}

message GetJobsByTypeRequest {
  string job_type = 1;
}

message GetJobsByTypeResponse {
  repeated Job jobs = 1;
}

message GetPendingJobsRequest {
  // No parameters needed
}

message GetPendingJobsResponse {
  repeated Job jobs = 1;
}

message GetRunningJobsRequest {
  // No parameters needed
}

message GetRunningJobsResponse {
  repeated Job jobs = 1;
}

message GetJobsByConfigIdRequest {
  int32 crawl_config_id = 1;
}

message GetJobsByConfigIdResponse {
  repeated Job jobs = 1;
}

message FilterJobsRequest {
  optional string job_type = 1;
  optional string status = 2;
  optional int32 crawl_config_id = 3;
  optional string created_by = 4;
  optional google.protobuf.Timestamp created_from = 5;
  optional google.protobuf.Timestamp created_to = 6;
  string order_by = 7;
}

message FilterJobsResponse {
  repeated Job jobs = 1;
}

message GetJobsNeedingRetryRequest {
  // No parameters needed
}

message GetJobsNeedingRetryResponse {
  repeated Job jobs = 1;
}

message GetExpiredJobsRequest {
  // No parameters needed
}

message GetExpiredJobsResponse {
  repeated Job jobs = 1;
}

// gRPC Service
service JobService {
  // Commands
  rpc CreateJob(CreateJobRequest) returns (CreateJobResponse);
  rpc UpdateJob(UpdateJobRequest) returns (UpdateJobResponse);
  rpc UpdateJobStatus(UpdateJobStatusRequest) returns (UpdateJobStatusResponse);
  rpc UpdateJobProgress(UpdateJobProgressRequest) returns (UpdateJobProgressResponse);
  rpc StartJob(StartJobRequest) returns (StartJobResponse);
  rpc CompleteJob(CompleteJobRequest) returns (CompleteJobResponse);
  rpc FailJob(FailJobRequest) returns (FailJobResponse);
  rpc CancelJob(CancelJobRequest) returns (CancelJobResponse);
  rpc RetryJob(RetryJobRequest) returns (RetryJobResponse);
  rpc DeleteJob(DeleteJobRequest) returns (DeleteJobResponse);

  // Queries
  rpc GetAllJobs(GetAllJobsRequest) returns (GetAllJobsResponse);
  rpc GetJobById(GetJobByIdRequest) returns (GetJobByIdResponse);
  rpc GetJobsByStatus(GetJobsByStatusRequest) returns (GetJobsByStatusResponse);
  rpc GetJobsByType(GetJobsByTypeRequest) returns (GetJobsByTypeResponse);
  rpc GetPendingJobs(GetPendingJobsRequest) returns (GetPendingJobsResponse);
  rpc GetRunningJobs(GetRunningJobsRequest) returns (GetRunningJobsResponse);
  rpc GetJobsByConfigId(GetJobsByConfigIdRequest) returns (GetJobsByConfigIdResponse);
  rpc FilterJobs(FilterJobsRequest) returns (FilterJobsResponse);
  rpc GetJobsNeedingRetry(GetJobsNeedingRetryRequest) returns (GetJobsNeedingRetryResponse);
  rpc GetExpiredJobs(GetExpiredJobsRequest) returns (GetExpiredJobsResponse);
}
