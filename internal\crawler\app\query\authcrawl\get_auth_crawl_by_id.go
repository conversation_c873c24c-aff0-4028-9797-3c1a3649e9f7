package queryauthcral

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetAuthCrawlByIdQuery struct {
	Id int
}

type GetAuthCrawlByIdQueryHandler decorator.QueryHandler[GetAuthCrawlByIdQuery, *AuthCrawl]

type getAuthCrawlByIdQueryHandler struct {
	repo RepositoryQueryAuth
}

func NewGetAuthCrawlByIdQueryHandler(
	repo RepositoryQueryAuth,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAuthCrawlByIdQueryHandler {

	return decorator.ApplyQueryDecorators[GetAuthCrawlByIdQuery, *AuthCrawl](
		getAuthCrawlByIdQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAuthCrawlByIdQueryHandler) Handle(ctx context.Context, query GetAuthCrawlByIdQuery) (*AuthCrawl, error) {
	return h.repo.GetAuthCrawlById(ctx, query.Id)
}
