package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ActiveMarket struct {
	MarketId    int
	ActivatedBy string
}

type ActiveMarketHandler decorator.CommandHandler[ActiveMarket]

type activeMarketHandler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewActiveMarketHandler(
	repo repository.RepositoryMarket,
	log logger.Logger,
	client decorator.MetricsClient,
	broker message.Broker) ActiveMarketHandler {
	return decorator.ApplyCommandDecorators[ActiveMarket](
		activeMarketHandler{
			repo:   repo,
			broker: broker,
		},
		log,
		client,
	)
}

func (a activeMarketHandler) Handle(ctx context.Context, cmd ActiveMarket) error {
	return a.repo.Update(ctx, cmd.MarketId, func(m *entity.Market) (*entity.Market, error) {
		err := m.Activate()
		if err != nil {
			return nil, err
		}
		return m, nil
	})
}
