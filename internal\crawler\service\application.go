package service

import (
	"context"
	authrepo "go_core_market/internal/crawler/adapter/database/authcrawl"
	configcrawlrepo "go_core_market/internal/crawler/adapter/database/configcrawl"
	jobrepo "go_core_market/internal/crawler/adapter/database/job"
	marketrepo "go_core_market/internal/crawler/adapter/database/market"
	proxyrepo "go_core_market/internal/crawler/adapter/database/proxy"
	crawlerapp "go_core_market/internal/crawler/app"
	"go_core_market/internal/crawler/app/command/authcrawl"
	"go_core_market/internal/crawler/app/command/crawlconfig"
	"go_core_market/internal/crawler/app/command/job"
	"go_core_market/internal/crawler/app/command/market"
	"go_core_market/internal/crawler/app/command/proxy"
	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	queryjob "go_core_market/internal/crawler/app/query/job"
	querymarket "go_core_market/internal/crawler/app/query/market"
	queryproxy "go_core_market/internal/crawler/app/query/proxy"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/config"
	"go_core_market/pkg/database"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
	"go_core_market/pkg/message/kafka"
	"go_core_market/pkg/message/serializer"
	"go_core_market/pkg/metrics"
)

// CrawlerApplicationBuilder extends the base application builder
type CrawlerApplicationBuilder struct {
	Ctx          context.Context
	Cfg          config.BaseServiceConfig
	Logger       logger.Logger
	Db           database.DatabaseManager
	Broker       message.Broker
	Metrics      decorator.MetricsClient
	Repositories *CrawlerRepositories
}

// CrawlerRepositories holds all repository instances
type CrawlerRepositories struct {
	MarketRepo      repository.RepositoryMarket
	ProxyRepo       repository.RepositoryProxy
	AuthCrawlRepo   repository.RepositoryAuthCrawl
	JobRepo         repository.RepositoryJob
	CrawlConfigRepo repository.RepositoryConfigCrawl
	// Query repositories
	MarketQueryRepo      querymarket.MarketReadModel
	JobQueryRepo         queryjob.JobReadModel
	CrawlConfigQueryRepo querycrawlconfig.CrawlConfigReadModel
	ProxyQueryRepo       queryproxy.ProxyReadModel
	AuthCrawlQueryRepo   queryauthcral.RepositoryQueryAuth
}

// NewCrawlerApplicationBuilder creates a new crawler application builder
func NewCrawlerApplicationBuilder(ctx context.Context, cfg config.BaseServiceConfig, logger logger.Logger) *CrawlerApplicationBuilder {
	return &CrawlerApplicationBuilder{
		Ctx:          ctx,
		Cfg:          cfg,
		Logger:       logger,
		Repositories: &CrawlerRepositories{},
	}
}

// WithDatabase extends base WithDatabase and returns CrawlerApplicationBuilder
func (b *CrawlerApplicationBuilder) WithDatabase() *CrawlerApplicationBuilder {
	b.Logger.Info("Initializing database connection",
		"driver", b.Cfg.Database.Driver,
		"host", b.Cfg.Database.Host,
		"port", b.Cfg.Database.Port,
	)
	dsn := b.Cfg.Database.GetDSN()
	db, err := database.NewPostgresManager(b.Ctx, dsn)
	if err != nil {
		b.Logger.Fatal("Failed to initialize database", "error", err)
		panic(err)
	}

	b.Db = db
	b.Logger.Info("Database connection established successfully")
	return b
}

// WithBroker extends base WithBroker and returns CrawlerApplicationBuilder
func (b *CrawlerApplicationBuilder) WithBroker() *CrawlerApplicationBuilder {
	b.Logger.Info("Initializing Kafka message broker",
		"brokers", b.Cfg.Kafka.Brokers,
		"group_id", b.Cfg.Kafka.GroupID,
	)

	kafkaConfig := kafka.Config{
		Brokers:        b.Cfg.Kafka.Brokers,
		BatchSize:      b.Cfg.Kafka.BatchSize,
		BatchTimeout:   b.Cfg.Kafka.BatchTimeout,
		ReadBatchSize:  b.Cfg.Kafka.ReadBatchSize,
		CommitInterval: b.Cfg.Kafka.CommitInterval,
		StartOffset:    b.Cfg.Kafka.StartOffset,
		GroupID:        b.Cfg.Kafka.GroupID,
		Serializer:     serializer.NewTypedSerializer(serializer.NewEventRegistry()),
	}

	broker := kafka.NewKafkaBroker(kafkaConfig)
	if err := broker.Connect(b.Ctx); err != nil {
		b.Logger.Fatal("Failed to connect to Kafka", "error", err)
		panic(err)
	}

	b.Broker = broker
	b.Logger.Info("Kafka message broker connected successfully")
	return b
}

// WithMetrics extends base WithMetrics and returns CrawlerApplicationBuilder
func (b *CrawlerApplicationBuilder) WithMetrics() *CrawlerApplicationBuilder {
	b.Metrics = metrics.NewSimpleMetricsClient()
	b.Logger.Info("Metrics client initialized successfully")
	return b

}

// WithRepository initializes all crawler-specific repositories
func (b *CrawlerApplicationBuilder) WithRepository() *CrawlerApplicationBuilder {
	db := b.Db.GetDB()
	// Initialize command repositories
	b.Repositories.MarketRepo = marketrepo.NewMarketRepository(db)
	b.Repositories.ProxyRepo = proxyrepo.NewProxyRepository(db)
	b.Repositories.AuthCrawlRepo = authrepo.NewAuthRepository(db)
	b.Repositories.JobRepo = jobrepo.NewJobRepository(db)
	b.Repositories.CrawlConfigRepo = configcrawlrepo.NewConfigCrawlRepository(db)

	// Initialize query repositories
	b.Repositories.MarketQueryRepo = marketrepo.NewMarketQueryRepository(db)
	b.Repositories.JobQueryRepo = jobrepo.NewJobQueryRepository(db)
	b.Repositories.CrawlConfigQueryRepo = configcrawlrepo.NewConfigCrawlQueryRepository(db)
	b.Repositories.ProxyQueryRepo = proxyrepo.NewProxyReadModel(db)
	b.Repositories.AuthCrawlQueryRepo = authrepo.NewAuthCrawlReadModel(db)

	b.Logger.Info("Crawler repositories initialized successfully")
	return b
}

// Build creates the final crawler application with all dependencies
func (b *CrawlerApplicationBuilder) Build() *crawlerapp.Application {

	//handlerBuilder := app.NewHandlerBuilder(logger, metrics)
	commands := b.buildCommands()
	queries := b.buildQueries()
	crawlerApp := crawlerapp.Application{
		Commands: commands,
		Queries:  queries,
		Db:       b.Db,
		Broker:   b.Broker,
	}

	b.Logger.Info("Crawler application built successfully")
	return &crawlerApp
}

// buildCommands creates all command handlers
func (b *CrawlerApplicationBuilder) buildCommands() crawlerapp.CrawlerCommands {

	return crawlerapp.CrawlerCommands{
		Market:      *commandmarket.NewMarketCommands(b.Repositories.MarketRepo, b.Logger, b.Metrics, b.Broker),
		Proxy:       *commandproxy.NewProxyCommands(b.Repositories.ProxyRepo, b.Logger, b.Metrics, b.Broker),
		AuthCrawl:   *commandauthcrawl.NewAuthCrawlCommands(b.Repositories.AuthCrawlRepo, b.Logger, b.Metrics, b.Broker),
		Job:         *commandjob.NewJobCommands(b.Repositories.JobRepo, b.Logger, b.Metrics, b.Broker),
		CrawlConfig: *commandcrawlconfig.NewCrawlConfigCommands(b.Repositories.CrawlConfigRepo, b.Logger, b.Metrics, b.Broker),
	}
}

// buildQueries creates all query handlers
func (b *CrawlerApplicationBuilder) buildQueries() crawlerapp.CrawlerQueries {

	return crawlerapp.CrawlerQueries{
		Market:      *querymarket.NewMarketQueries(b.Repositories.MarketQueryRepo, b.Logger, b.Metrics),
		Proxy:       *queryproxy.NewProxyQueries(b.Repositories.ProxyQueryRepo, b.Logger, b.Metrics),
		AuthCrawl:   *queryauthcral.NewAuthCrawlQueries(b.Repositories.AuthCrawlQueryRepo, b.Logger, b.Metrics),
		Job:         queryjob.NewJobQueries(b.Repositories.JobQueryRepo, b.Logger, b.Metrics),
		CrawlConfig: querycrawlconfig.NewCrawlConfigQueries(b.Repositories.CrawlConfigQueryRepo, b.Logger, b.Metrics),
	}
}
