// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v4.25.1
// source: job.proto

package job

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	JobService_CreateJob_FullMethodName           = "/job.v1.JobService/CreateJob"
	JobService_UpdateJob_FullMethodName           = "/job.v1.JobService/UpdateJob"
	JobService_UpdateJobStatus_FullMethodName     = "/job.v1.JobService/UpdateJobStatus"
	JobService_UpdateJobProgress_FullMethodName   = "/job.v1.JobService/UpdateJobProgress"
	JobService_StartJob_FullMethodName            = "/job.v1.JobService/StartJob"
	JobService_CompleteJob_FullMethodName         = "/job.v1.JobService/CompleteJob"
	JobService_FailJob_FullMethodName             = "/job.v1.JobService/FailJob"
	JobService_CancelJob_FullMethodName           = "/job.v1.JobService/CancelJob"
	JobService_RetryJob_FullMethodName            = "/job.v1.JobService/RetryJob"
	JobService_DeleteJob_FullMethodName           = "/job.v1.JobService/DeleteJob"
	JobService_GetAllJobs_FullMethodName          = "/job.v1.JobService/GetAllJobs"
	JobService_GetJobById_FullMethodName          = "/job.v1.JobService/GetJobById"
	JobService_GetJobsByStatus_FullMethodName     = "/job.v1.JobService/GetJobsByStatus"
	JobService_GetJobsByType_FullMethodName       = "/job.v1.JobService/GetJobsByType"
	JobService_GetPendingJobs_FullMethodName      = "/job.v1.JobService/GetPendingJobs"
	JobService_GetRunningJobs_FullMethodName      = "/job.v1.JobService/GetRunningJobs"
	JobService_GetJobsByConfigId_FullMethodName   = "/job.v1.JobService/GetJobsByConfigId"
	JobService_FilterJobs_FullMethodName          = "/job.v1.JobService/FilterJobs"
	JobService_GetJobsNeedingRetry_FullMethodName = "/job.v1.JobService/GetJobsNeedingRetry"
	JobService_GetExpiredJobs_FullMethodName      = "/job.v1.JobService/GetExpiredJobs"
)

// JobServiceClient is the client API for JobService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// gRPC Service
type JobServiceClient interface {
	// Commands
	CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error)
	UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*UpdateJobResponse, error)
	UpdateJobStatus(ctx context.Context, in *UpdateJobStatusRequest, opts ...grpc.CallOption) (*UpdateJobStatusResponse, error)
	UpdateJobProgress(ctx context.Context, in *UpdateJobProgressRequest, opts ...grpc.CallOption) (*UpdateJobProgressResponse, error)
	StartJob(ctx context.Context, in *StartJobRequest, opts ...grpc.CallOption) (*StartJobResponse, error)
	CompleteJob(ctx context.Context, in *CompleteJobRequest, opts ...grpc.CallOption) (*CompleteJobResponse, error)
	FailJob(ctx context.Context, in *FailJobRequest, opts ...grpc.CallOption) (*FailJobResponse, error)
	CancelJob(ctx context.Context, in *CancelJobRequest, opts ...grpc.CallOption) (*CancelJobResponse, error)
	RetryJob(ctx context.Context, in *RetryJobRequest, opts ...grpc.CallOption) (*RetryJobResponse, error)
	DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*DeleteJobResponse, error)
	// Queries
	GetAllJobs(ctx context.Context, in *GetAllJobsRequest, opts ...grpc.CallOption) (*GetAllJobsResponse, error)
	GetJobById(ctx context.Context, in *GetJobByIdRequest, opts ...grpc.CallOption) (*GetJobByIdResponse, error)
	GetJobsByStatus(ctx context.Context, in *GetJobsByStatusRequest, opts ...grpc.CallOption) (*GetJobsByStatusResponse, error)
	GetJobsByType(ctx context.Context, in *GetJobsByTypeRequest, opts ...grpc.CallOption) (*GetJobsByTypeResponse, error)
	GetPendingJobs(ctx context.Context, in *GetPendingJobsRequest, opts ...grpc.CallOption) (*GetPendingJobsResponse, error)
	GetRunningJobs(ctx context.Context, in *GetRunningJobsRequest, opts ...grpc.CallOption) (*GetRunningJobsResponse, error)
	GetJobsByConfigId(ctx context.Context, in *GetJobsByConfigIdRequest, opts ...grpc.CallOption) (*GetJobsByConfigIdResponse, error)
	FilterJobs(ctx context.Context, in *FilterJobsRequest, opts ...grpc.CallOption) (*FilterJobsResponse, error)
	GetJobsNeedingRetry(ctx context.Context, in *GetJobsNeedingRetryRequest, opts ...grpc.CallOption) (*GetJobsNeedingRetryResponse, error)
	GetExpiredJobs(ctx context.Context, in *GetExpiredJobsRequest, opts ...grpc.CallOption) (*GetExpiredJobsResponse, error)
}

type jobServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewJobServiceClient(cc grpc.ClientConnInterface) JobServiceClient {
	return &jobServiceClient{cc}
}

func (c *jobServiceClient) CreateJob(ctx context.Context, in *CreateJobRequest, opts ...grpc.CallOption) (*CreateJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateJobResponse)
	err := c.cc.Invoke(ctx, JobService_CreateJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJob(ctx context.Context, in *UpdateJobRequest, opts ...grpc.CallOption) (*UpdateJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateJobResponse)
	err := c.cc.Invoke(ctx, JobService_UpdateJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJobStatus(ctx context.Context, in *UpdateJobStatusRequest, opts ...grpc.CallOption) (*UpdateJobStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateJobStatusResponse)
	err := c.cc.Invoke(ctx, JobService_UpdateJobStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) UpdateJobProgress(ctx context.Context, in *UpdateJobProgressRequest, opts ...grpc.CallOption) (*UpdateJobProgressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateJobProgressResponse)
	err := c.cc.Invoke(ctx, JobService_UpdateJobProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) StartJob(ctx context.Context, in *StartJobRequest, opts ...grpc.CallOption) (*StartJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartJobResponse)
	err := c.cc.Invoke(ctx, JobService_StartJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) CompleteJob(ctx context.Context, in *CompleteJobRequest, opts ...grpc.CallOption) (*CompleteJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CompleteJobResponse)
	err := c.cc.Invoke(ctx, JobService_CompleteJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) FailJob(ctx context.Context, in *FailJobRequest, opts ...grpc.CallOption) (*FailJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FailJobResponse)
	err := c.cc.Invoke(ctx, JobService_FailJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) CancelJob(ctx context.Context, in *CancelJobRequest, opts ...grpc.CallOption) (*CancelJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CancelJobResponse)
	err := c.cc.Invoke(ctx, JobService_CancelJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) RetryJob(ctx context.Context, in *RetryJobRequest, opts ...grpc.CallOption) (*RetryJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RetryJobResponse)
	err := c.cc.Invoke(ctx, JobService_RetryJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) DeleteJob(ctx context.Context, in *DeleteJobRequest, opts ...grpc.CallOption) (*DeleteJobResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteJobResponse)
	err := c.cc.Invoke(ctx, JobService_DeleteJob_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetAllJobs(ctx context.Context, in *GetAllJobsRequest, opts ...grpc.CallOption) (*GetAllJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetAllJobsResponse)
	err := c.cc.Invoke(ctx, JobService_GetAllJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobById(ctx context.Context, in *GetJobByIdRequest, opts ...grpc.CallOption) (*GetJobByIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobByIdResponse)
	err := c.cc.Invoke(ctx, JobService_GetJobById_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobsByStatus(ctx context.Context, in *GetJobsByStatusRequest, opts ...grpc.CallOption) (*GetJobsByStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobsByStatusResponse)
	err := c.cc.Invoke(ctx, JobService_GetJobsByStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobsByType(ctx context.Context, in *GetJobsByTypeRequest, opts ...grpc.CallOption) (*GetJobsByTypeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobsByTypeResponse)
	err := c.cc.Invoke(ctx, JobService_GetJobsByType_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetPendingJobs(ctx context.Context, in *GetPendingJobsRequest, opts ...grpc.CallOption) (*GetPendingJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPendingJobsResponse)
	err := c.cc.Invoke(ctx, JobService_GetPendingJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetRunningJobs(ctx context.Context, in *GetRunningJobsRequest, opts ...grpc.CallOption) (*GetRunningJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRunningJobsResponse)
	err := c.cc.Invoke(ctx, JobService_GetRunningJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobsByConfigId(ctx context.Context, in *GetJobsByConfigIdRequest, opts ...grpc.CallOption) (*GetJobsByConfigIdResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobsByConfigIdResponse)
	err := c.cc.Invoke(ctx, JobService_GetJobsByConfigId_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) FilterJobs(ctx context.Context, in *FilterJobsRequest, opts ...grpc.CallOption) (*FilterJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterJobsResponse)
	err := c.cc.Invoke(ctx, JobService_FilterJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetJobsNeedingRetry(ctx context.Context, in *GetJobsNeedingRetryRequest, opts ...grpc.CallOption) (*GetJobsNeedingRetryResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetJobsNeedingRetryResponse)
	err := c.cc.Invoke(ctx, JobService_GetJobsNeedingRetry_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *jobServiceClient) GetExpiredJobs(ctx context.Context, in *GetExpiredJobsRequest, opts ...grpc.CallOption) (*GetExpiredJobsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetExpiredJobsResponse)
	err := c.cc.Invoke(ctx, JobService_GetExpiredJobs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// JobServiceServer is the server API for JobService service.
// All implementations must embed UnimplementedJobServiceServer
// for forward compatibility.
//
// gRPC Service
type JobServiceServer interface {
	// Commands
	CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error)
	UpdateJob(context.Context, *UpdateJobRequest) (*UpdateJobResponse, error)
	UpdateJobStatus(context.Context, *UpdateJobStatusRequest) (*UpdateJobStatusResponse, error)
	UpdateJobProgress(context.Context, *UpdateJobProgressRequest) (*UpdateJobProgressResponse, error)
	StartJob(context.Context, *StartJobRequest) (*StartJobResponse, error)
	CompleteJob(context.Context, *CompleteJobRequest) (*CompleteJobResponse, error)
	FailJob(context.Context, *FailJobRequest) (*FailJobResponse, error)
	CancelJob(context.Context, *CancelJobRequest) (*CancelJobResponse, error)
	RetryJob(context.Context, *RetryJobRequest) (*RetryJobResponse, error)
	DeleteJob(context.Context, *DeleteJobRequest) (*DeleteJobResponse, error)
	// Queries
	GetAllJobs(context.Context, *GetAllJobsRequest) (*GetAllJobsResponse, error)
	GetJobById(context.Context, *GetJobByIdRequest) (*GetJobByIdResponse, error)
	GetJobsByStatus(context.Context, *GetJobsByStatusRequest) (*GetJobsByStatusResponse, error)
	GetJobsByType(context.Context, *GetJobsByTypeRequest) (*GetJobsByTypeResponse, error)
	GetPendingJobs(context.Context, *GetPendingJobsRequest) (*GetPendingJobsResponse, error)
	GetRunningJobs(context.Context, *GetRunningJobsRequest) (*GetRunningJobsResponse, error)
	GetJobsByConfigId(context.Context, *GetJobsByConfigIdRequest) (*GetJobsByConfigIdResponse, error)
	FilterJobs(context.Context, *FilterJobsRequest) (*FilterJobsResponse, error)
	GetJobsNeedingRetry(context.Context, *GetJobsNeedingRetryRequest) (*GetJobsNeedingRetryResponse, error)
	GetExpiredJobs(context.Context, *GetExpiredJobsRequest) (*GetExpiredJobsResponse, error)
	mustEmbedUnimplementedJobServiceServer()
}

// UnimplementedJobServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedJobServiceServer struct{}

func (UnimplementedJobServiceServer) CreateJob(context.Context, *CreateJobRequest) (*CreateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateJob not implemented")
}
func (UnimplementedJobServiceServer) UpdateJob(context.Context, *UpdateJobRequest) (*UpdateJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJob not implemented")
}
func (UnimplementedJobServiceServer) UpdateJobStatus(context.Context, *UpdateJobStatusRequest) (*UpdateJobStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobStatus not implemented")
}
func (UnimplementedJobServiceServer) UpdateJobProgress(context.Context, *UpdateJobProgressRequest) (*UpdateJobProgressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateJobProgress not implemented")
}
func (UnimplementedJobServiceServer) StartJob(context.Context, *StartJobRequest) (*StartJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartJob not implemented")
}
func (UnimplementedJobServiceServer) CompleteJob(context.Context, *CompleteJobRequest) (*CompleteJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteJob not implemented")
}
func (UnimplementedJobServiceServer) FailJob(context.Context, *FailJobRequest) (*FailJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FailJob not implemented")
}
func (UnimplementedJobServiceServer) CancelJob(context.Context, *CancelJobRequest) (*CancelJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelJob not implemented")
}
func (UnimplementedJobServiceServer) RetryJob(context.Context, *RetryJobRequest) (*RetryJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RetryJob not implemented")
}
func (UnimplementedJobServiceServer) DeleteJob(context.Context, *DeleteJobRequest) (*DeleteJobResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteJob not implemented")
}
func (UnimplementedJobServiceServer) GetAllJobs(context.Context, *GetAllJobsRequest) (*GetAllJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllJobs not implemented")
}
func (UnimplementedJobServiceServer) GetJobById(context.Context, *GetJobByIdRequest) (*GetJobByIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobById not implemented")
}
func (UnimplementedJobServiceServer) GetJobsByStatus(context.Context, *GetJobsByStatusRequest) (*GetJobsByStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobsByStatus not implemented")
}
func (UnimplementedJobServiceServer) GetJobsByType(context.Context, *GetJobsByTypeRequest) (*GetJobsByTypeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobsByType not implemented")
}
func (UnimplementedJobServiceServer) GetPendingJobs(context.Context, *GetPendingJobsRequest) (*GetPendingJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPendingJobs not implemented")
}
func (UnimplementedJobServiceServer) GetRunningJobs(context.Context, *GetRunningJobsRequest) (*GetRunningJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRunningJobs not implemented")
}
func (UnimplementedJobServiceServer) GetJobsByConfigId(context.Context, *GetJobsByConfigIdRequest) (*GetJobsByConfigIdResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobsByConfigId not implemented")
}
func (UnimplementedJobServiceServer) FilterJobs(context.Context, *FilterJobsRequest) (*FilterJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterJobs not implemented")
}
func (UnimplementedJobServiceServer) GetJobsNeedingRetry(context.Context, *GetJobsNeedingRetryRequest) (*GetJobsNeedingRetryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetJobsNeedingRetry not implemented")
}
func (UnimplementedJobServiceServer) GetExpiredJobs(context.Context, *GetExpiredJobsRequest) (*GetExpiredJobsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExpiredJobs not implemented")
}
func (UnimplementedJobServiceServer) mustEmbedUnimplementedJobServiceServer() {}
func (UnimplementedJobServiceServer) testEmbeddedByValue()                    {}

// UnsafeJobServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to JobServiceServer will
// result in compilation errors.
type UnsafeJobServiceServer interface {
	mustEmbedUnimplementedJobServiceServer()
}

func RegisterJobServiceServer(s grpc.ServiceRegistrar, srv JobServiceServer) {
	// If the following call pancis, it indicates UnimplementedJobServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&JobService_ServiceDesc, srv)
}

func _JobService_CreateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CreateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CreateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CreateJob(ctx, req.(*CreateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJob(ctx, req.(*UpdateJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJobStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJobStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJobStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJobStatus(ctx, req.(*UpdateJobStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_UpdateJobProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateJobProgressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).UpdateJobProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_UpdateJobProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).UpdateJobProgress(ctx, req.(*UpdateJobProgressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_StartJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).StartJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_StartJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).StartJob(ctx, req.(*StartJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_CompleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CompleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CompleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CompleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CompleteJob(ctx, req.(*CompleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_FailJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FailJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).FailJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_FailJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).FailJob(ctx, req.(*FailJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_CancelJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).CancelJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_CancelJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).CancelJob(ctx, req.(*CancelJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_RetryJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RetryJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).RetryJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_RetryJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).RetryJob(ctx, req.(*RetryJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_DeleteJob_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteJobRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).DeleteJob(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_DeleteJob_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).DeleteJob(ctx, req.(*DeleteJobRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetAllJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAllJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetAllJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetAllJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetAllJobs(ctx, req.(*GetAllJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobByIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobById_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobById(ctx, req.(*GetJobByIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobsByStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobsByStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobsByStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobsByStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobsByStatus(ctx, req.(*GetJobsByStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobsByType_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobsByTypeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobsByType(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobsByType_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobsByType(ctx, req.(*GetJobsByTypeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetPendingJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPendingJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetPendingJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetPendingJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetPendingJobs(ctx, req.(*GetPendingJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetRunningJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRunningJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetRunningJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetRunningJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetRunningJobs(ctx, req.(*GetRunningJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobsByConfigId_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobsByConfigIdRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobsByConfigId(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobsByConfigId_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobsByConfigId(ctx, req.(*GetJobsByConfigIdRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_FilterJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).FilterJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_FilterJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).FilterJobs(ctx, req.(*FilterJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetJobsNeedingRetry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetJobsNeedingRetryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetJobsNeedingRetry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetJobsNeedingRetry_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetJobsNeedingRetry(ctx, req.(*GetJobsNeedingRetryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _JobService_GetExpiredJobs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExpiredJobsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(JobServiceServer).GetExpiredJobs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: JobService_GetExpiredJobs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(JobServiceServer).GetExpiredJobs(ctx, req.(*GetExpiredJobsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// JobService_ServiceDesc is the grpc.ServiceDesc for JobService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var JobService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "job.v1.JobService",
	HandlerType: (*JobServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateJob",
			Handler:    _JobService_CreateJob_Handler,
		},
		{
			MethodName: "UpdateJob",
			Handler:    _JobService_UpdateJob_Handler,
		},
		{
			MethodName: "UpdateJobStatus",
			Handler:    _JobService_UpdateJobStatus_Handler,
		},
		{
			MethodName: "UpdateJobProgress",
			Handler:    _JobService_UpdateJobProgress_Handler,
		},
		{
			MethodName: "StartJob",
			Handler:    _JobService_StartJob_Handler,
		},
		{
			MethodName: "CompleteJob",
			Handler:    _JobService_CompleteJob_Handler,
		},
		{
			MethodName: "FailJob",
			Handler:    _JobService_FailJob_Handler,
		},
		{
			MethodName: "CancelJob",
			Handler:    _JobService_CancelJob_Handler,
		},
		{
			MethodName: "RetryJob",
			Handler:    _JobService_RetryJob_Handler,
		},
		{
			MethodName: "DeleteJob",
			Handler:    _JobService_DeleteJob_Handler,
		},
		{
			MethodName: "GetAllJobs",
			Handler:    _JobService_GetAllJobs_Handler,
		},
		{
			MethodName: "GetJobById",
			Handler:    _JobService_GetJobById_Handler,
		},
		{
			MethodName: "GetJobsByStatus",
			Handler:    _JobService_GetJobsByStatus_Handler,
		},
		{
			MethodName: "GetJobsByType",
			Handler:    _JobService_GetJobsByType_Handler,
		},
		{
			MethodName: "GetPendingJobs",
			Handler:    _JobService_GetPendingJobs_Handler,
		},
		{
			MethodName: "GetRunningJobs",
			Handler:    _JobService_GetRunningJobs_Handler,
		},
		{
			MethodName: "GetJobsByConfigId",
			Handler:    _JobService_GetJobsByConfigId_Handler,
		},
		{
			MethodName: "FilterJobs",
			Handler:    _JobService_FilterJobs_Handler,
		},
		{
			MethodName: "GetJobsNeedingRetry",
			Handler:    _JobService_GetJobsNeedingRetry_Handler,
		},
		{
			MethodName: "GetExpiredJobs",
			Handler:    _JobService_GetExpiredJobs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "job.proto",
}
