package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateResourcesCrawlParams struct {
	ID             int
	MaxNumberProxy int
	MaxNumberAuth  int
	UpdatedBy      string
}

type UpdateResourcesCrawlHandler decorator.CommandHandler[UpdateResourcesCrawlParams]

type updateResourcesCrawlHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewUpdateResourcesCrawlHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateResourcesCrawlHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateResourcesCrawlParams](
		updateResourcesCrawlHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateResourcesCrawlHandler) Handle(ctx context.Context, cmd UpdateResourcesCrawlParams) error {
	return h.repo.Update(ctx, cmd.ID, func(c *entity.CrawlConfig) (*entity.CrawlConfig, error) {
		// Update config information
		err := c.UpdateResourcesCrawl(entity.UpdateResourcesParams{
			MaxNumberProxy: cmd.MaxNumberProxy,
			MaxNumberAuth:  cmd.MaxNumberAuth,
		})
		if err != nil {
			return nil, err
		}
		return c, nil
	})
}
