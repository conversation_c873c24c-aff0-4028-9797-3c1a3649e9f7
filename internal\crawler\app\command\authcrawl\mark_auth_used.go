package commandauthcrawl

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type MarkAuthUsed struct {
	ID     int
	UsedBy string
}

type MarkAuthUsedHandler decorator.CommandHandler[MarkAuthUsed]

type markAuthUsedHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewMarkAuthUsedHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) MarkAuthUsedHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[MarkAuthUsed](
		markAuthUsedHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h markAuthUsedHandler) Handle(ctx context.Context, cmd MarkAuthUsed) error {
	return h.repo.Update(ctx, cmd.ID, func(a *entity.Auth) (*entity.Auth, error) {
		err := a.MarkUsed()
		if err != nil {
			return nil, err
		}
		return a, nil
	})
}

type MarkAuthsUsed struct {
	IDs    []int
	UsedBy string
}

type MarkAuthsUsedHandler decorator.CommandHandler[MarkAuthsUsed]

type markAuthsUsedHandler struct {
	repo   repository.RepositoryAuthCrawl
	broker message.Broker
}

func NewMarkAuthsUsedHandler(
	repo repository.RepositoryAuthCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) MarkAuthsUsedHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[MarkAuthsUsed](
		markAuthsUsedHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h markAuthsUsedHandler) Handle(ctx context.Context, cmd MarkAuthsUsed) error {
	return h.repo.UpdateMany(ctx, cmd.IDs, func(p []*entity.Auth) ([]*entity.Auth, error) {
		for _, v := range p {
			err := v.MarkUsed()
			if err != nil {
				return nil, err
			}
		}
		return p, nil
	})
}
