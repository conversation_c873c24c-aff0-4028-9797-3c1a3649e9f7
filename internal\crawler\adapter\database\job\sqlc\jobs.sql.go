// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: jobs.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountJobsByStatusQuery = `-- name: CountJobsByStatusQuery :one
SELECT COUNT(*) FROM jobs WHERE status = $1
`

func (q *Queries) CountJobsByStatusQuery(ctx context.Context, status string) (int64, error) {
	row := q.db.QueryRow(ctx, CountJobsByStatusQuery, status)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const CountJobsQuery = `-- name: CountJobsQuery :one
SELECT COUNT(*) FROM jobs
`

func (q *Queries) CountJobsQuery(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountJobsQuery)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const Create = `-- name: Create :one
INSERT INTO jobs (
    job_type,
    name,
    description,
    status,
    crawl_config_id,
    scheduled_at,
    max_retries,
    timeout_seconds,
    created_by
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id
`

type CreateParams struct {
	JobType        string             `db:"job_type" json:"job_type"`
	Name           string             `db:"name" json:"name"`
	Description    *string            `db:"description" json:"description"`
	Status         string             `db:"status" json:"status"`
	CrawlConfigID  *int32             `db:"crawl_config_id" json:"crawl_config_id"`
	ScheduledAt    pgtype.Timestamptz `db:"scheduled_at" json:"scheduled_at"`
	MaxRetries     int                `db:"max_retries" json:"max_retries"`
	TimeoutSeconds int                `db:"timeout_seconds" json:"timeout_seconds"`
	CreatedBy      string             `db:"created_by" json:"created_by"`
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (int, error) {
	row := q.db.QueryRow(ctx, Create,
		arg.JobType,
		arg.Name,
		arg.Description,
		arg.Status,
		arg.CrawlConfigID,
		arg.ScheduledAt,
		arg.MaxRetries,
		arg.TimeoutSeconds,
		arg.CreatedBy,
	)
	var id int
	err := row.Scan(&id)
	return id, err
}

type CreateManyParams struct {
	JobType        string             `db:"job_type" json:"job_type"`
	Name           string             `db:"name" json:"name"`
	Description    *string            `db:"description" json:"description"`
	Status         string             `db:"status" json:"status"`
	CrawlConfigID  *int32             `db:"crawl_config_id" json:"crawl_config_id"`
	ScheduledAt    pgtype.Timestamptz `db:"scheduled_at" json:"scheduled_at"`
	MaxRetries     int                `db:"max_retries" json:"max_retries"`
	TimeoutSeconds int                `db:"timeout_seconds" json:"timeout_seconds"`
	CreatedBy      string             `db:"created_by" json:"created_by"`
}

const DeleteJob = `-- name: DeleteJob :exec
DELETE FROM jobs WHERE id = $1
`

func (q *Queries) DeleteJob(ctx context.Context, id int) error {
	_, err := q.db.Exec(ctx, DeleteJob, id)
	return err
}

const FilterJobsQuery = `-- name: FilterJobsQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs
WHERE
    ($1 = '' OR job_type = $1)
    AND ($2 = '' OR status = $2)
    AND ($3 = 0 OR crawl_config_id = $3)
    AND ($4 = '' OR created_by = $4)
    AND ($5::timestamptz IS NULL OR created_at >= $5)
    AND ($6::timestamptz IS NULL OR created_at <= $6)
ORDER BY
    CASE WHEN $7 = 'name' THEN name END ASC,
    CASE WHEN $7 = 'name_desc' THEN name END DESC,
    CASE WHEN $7 = 'created_at' THEN created_at END ASC,
    CASE WHEN $7 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $7 = 'scheduled_at' THEN scheduled_at END ASC,
    CASE WHEN $7 = 'scheduled_at_desc' THEN scheduled_at END DESC,
    CASE WHEN $7 = '' OR $7 IS NULL THEN id END ASC
`

type FilterJobsQueryParams struct {
	Column1 interface{}        `db:"column_1" json:"column_1"`
	Column2 interface{}        `db:"column_2" json:"column_2"`
	Column3 interface{}        `db:"column_3" json:"column_3"`
	Column4 interface{}        `db:"column_4" json:"column_4"`
	Column5 pgtype.Timestamptz `db:"column_5" json:"column_5"`
	Column6 pgtype.Timestamptz `db:"column_6" json:"column_6"`
	Column7 interface{}        `db:"column_7" json:"column_7"`
}

func (q *Queries) FilterJobsQuery(ctx context.Context, arg FilterJobsQueryParams) ([]*Job, error) {
	rows, err := q.db.Query(ctx, FilterJobsQuery,
		arg.Column1,
		arg.Column2,
		arg.Column3,
		arg.Column4,
		arg.Column5,
		arg.Column6,
		arg.Column7,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const FindByID = `-- name: FindByID :one
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE id = $1 LIMIT 1
`

func (q *Queries) FindByID(ctx context.Context, id int) (*Job, error) {
	row := q.db.QueryRow(ctx, FindByID, id)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetAllJobsWithPagination = `-- name: GetAllJobsWithPagination :many

SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs
ORDER BY
    CASE WHEN $3 = 'name' THEN name END ASC,
    CASE WHEN $3 = 'name_desc' THEN name END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = 'scheduled_at' THEN scheduled_at END ASC,
    CASE WHEN $3 = 'scheduled_at_desc' THEN scheduled_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2
`

type GetAllJobsWithPaginationParams struct {
	Limit   int32       `db:"limit" json:"limit"`
	Offset  int32       `db:"offset" json:"offset"`
	Column3 interface{} `db:"column_3" json:"column_3"`
}

// Query methods for JobQueryRepository
func (q *Queries) GetAllJobsWithPagination(ctx context.Context, arg GetAllJobsWithPaginationParams) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetAllJobsWithPagination, arg.Limit, arg.Offset, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetExpiredJobsQuery = `-- name: GetExpiredJobsQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs 
WHERE status = 'running' 
  AND started_at IS NOT NULL 
  AND started_at + INTERVAL '1 second' * timeout_seconds < NOW()
ORDER BY started_at ASC
`

func (q *Queries) GetExpiredJobsQuery(ctx context.Context) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetExpiredJobsQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetJobByIdQuery = `-- name: GetJobByIdQuery :one
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE id = $1 LIMIT 1
`

func (q *Queries) GetJobByIdQuery(ctx context.Context, id int) (*Job, error) {
	row := q.db.QueryRow(ctx, GetJobByIdQuery, id)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetJobsByConfigIdQuery = `-- name: GetJobsByConfigIdQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE crawl_config_id = $1 ORDER BY created_at DESC
`

func (q *Queries) GetJobsByConfigIdQuery(ctx context.Context, crawlConfigID *int32) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetJobsByConfigIdQuery, crawlConfigID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetJobsByStatusQuery = `-- name: GetJobsByStatusQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE status = $1 ORDER BY created_at ASC
`

func (q *Queries) GetJobsByStatusQuery(ctx context.Context, status string) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetJobsByStatusQuery, status)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetJobsByTypeQuery = `-- name: GetJobsByTypeQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE job_type = $1 ORDER BY created_at ASC
`

func (q *Queries) GetJobsByTypeQuery(ctx context.Context, jobType string) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetJobsByTypeQuery, jobType)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetJobsNeedingRetryQuery = `-- name: GetJobsNeedingRetryQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs 
WHERE status = 'failed' 
  AND retry_count < max_retries 
ORDER BY created_at ASC
`

func (q *Queries) GetJobsNeedingRetryQuery(ctx context.Context) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetJobsNeedingRetryQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetPendingJobsQuery = `-- name: GetPendingJobsQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs 
WHERE status = 'pending' 
   OR (status = 'scheduled' AND scheduled_at <= NOW())
ORDER BY scheduled_at ASC NULLS FIRST, created_at ASC
`

func (q *Queries) GetPendingJobsQuery(ctx context.Context) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetPendingJobsQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetRunningJobsQuery = `-- name: GetRunningJobsQuery :many
SELECT id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at FROM jobs WHERE status = 'running' ORDER BY started_at ASC
`

func (q *Queries) GetRunningJobsQuery(ctx context.Context) ([]*Job, error) {
	rows, err := q.db.Query(ctx, GetRunningJobsQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Job{}
	for rows.Next() {
		var i Job
		if err := rows.Scan(
			&i.ID,
			&i.JobType,
			&i.Name,
			&i.Description,
			&i.Status,
			&i.CrawlConfigID,
			&i.ScheduledAt,
			&i.StartedAt,
			&i.CompletedAt,
			&i.CurrentStep,
			&i.TotalSteps,
			&i.ProgressMessage,
			&i.ProgressPercentage,
			&i.MaxRetries,
			&i.RetryCount,
			&i.LastError,
			&i.TimeoutSeconds,
			&i.CreatedBy,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const IncrementRetryCount = `-- name: IncrementRetryCount :one
UPDATE jobs
SET retry_count = retry_count + 1,
    last_error = $2
WHERE id = $1
RETURNING id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at
`

type IncrementRetryCountParams struct {
	ID        int     `db:"id" json:"id"`
	LastError *string `db:"last_error" json:"last_error"`
}

func (q *Queries) IncrementRetryCount(ctx context.Context, arg IncrementRetryCountParams) (*Job, error) {
	row := q.db.QueryRow(ctx, IncrementRetryCount, arg.ID, arg.LastError)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateJob = `-- name: UpdateJob :one
UPDATE jobs
SET job_type = $2,
    name = $3,
    description = $4,
    status = $5,
    crawl_config_id = $6,
    scheduled_at = $7,
    started_at = $8,
    completed_at = $9,
    current_step = $10,
    total_steps = $11,
    progress_message = $12,
    progress_percentage = $13,
    max_retries = $14,
    retry_count = $15,
    last_error = $16,
    timeout_seconds = $17
WHERE id = $1
RETURNING id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at
`

type UpdateJobParams struct {
	ID                 int                `db:"id" json:"id"`
	JobType            string             `db:"job_type" json:"job_type"`
	Name               string             `db:"name" json:"name"`
	Description        *string            `db:"description" json:"description"`
	Status             string             `db:"status" json:"status"`
	CrawlConfigID      *int32             `db:"crawl_config_id" json:"crawl_config_id"`
	ScheduledAt        pgtype.Timestamptz `db:"scheduled_at" json:"scheduled_at"`
	StartedAt          pgtype.Timestamptz `db:"started_at" json:"started_at"`
	CompletedAt        pgtype.Timestamptz `db:"completed_at" json:"completed_at"`
	CurrentStep        *int32             `db:"current_step" json:"current_step"`
	TotalSteps         *int32             `db:"total_steps" json:"total_steps"`
	ProgressMessage    *string            `db:"progress_message" json:"progress_message"`
	ProgressPercentage *int32             `db:"progress_percentage" json:"progress_percentage"`
	MaxRetries         int                `db:"max_retries" json:"max_retries"`
	RetryCount         int                `db:"retry_count" json:"retry_count"`
	LastError          *string            `db:"last_error" json:"last_error"`
	TimeoutSeconds     int                `db:"timeout_seconds" json:"timeout_seconds"`
}

func (q *Queries) UpdateJob(ctx context.Context, arg UpdateJobParams) (*Job, error) {
	row := q.db.QueryRow(ctx, UpdateJob,
		arg.ID,
		arg.JobType,
		arg.Name,
		arg.Description,
		arg.Status,
		arg.CrawlConfigID,
		arg.ScheduledAt,
		arg.StartedAt,
		arg.CompletedAt,
		arg.CurrentStep,
		arg.TotalSteps,
		arg.ProgressMessage,
		arg.ProgressPercentage,
		arg.MaxRetries,
		arg.RetryCount,
		arg.LastError,
		arg.TimeoutSeconds,
	)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateJobProgress = `-- name: UpdateJobProgress :one
UPDATE jobs
SET current_step = $2,
    total_steps = $3,
    progress_message = $4,
    progress_percentage = $5
WHERE id = $1
RETURNING id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at
`

type UpdateJobProgressParams struct {
	ID                 int     `db:"id" json:"id"`
	CurrentStep        *int32  `db:"current_step" json:"current_step"`
	TotalSteps         *int32  `db:"total_steps" json:"total_steps"`
	ProgressMessage    *string `db:"progress_message" json:"progress_message"`
	ProgressPercentage *int32  `db:"progress_percentage" json:"progress_percentage"`
}

func (q *Queries) UpdateJobProgress(ctx context.Context, arg UpdateJobProgressParams) (*Job, error) {
	row := q.db.QueryRow(ctx, UpdateJobProgress,
		arg.ID,
		arg.CurrentStep,
		arg.TotalSteps,
		arg.ProgressMessage,
		arg.ProgressPercentage,
	)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateJobStatus = `-- name: UpdateJobStatus :one
UPDATE jobs
SET status = $2,
    started_at = CASE WHEN $2 = 'running' AND started_at IS NULL THEN NOW() ELSE started_at END,
    completed_at = CASE WHEN $2 IN ('completed', 'failed', 'cancelled') AND completed_at IS NULL THEN NOW() ELSE completed_at END
WHERE id = $1
RETURNING id, job_type, name, description, status, crawl_config_id, scheduled_at, started_at, completed_at, current_step, total_steps, progress_message, progress_percentage, max_retries, retry_count, last_error, timeout_seconds, created_by, created_at, updated_at
`

type UpdateJobStatusParams struct {
	ID     int    `db:"id" json:"id"`
	Status string `db:"status" json:"status"`
}

func (q *Queries) UpdateJobStatus(ctx context.Context, arg UpdateJobStatusParams) (*Job, error) {
	row := q.db.QueryRow(ctx, UpdateJobStatus, arg.ID, arg.Status)
	var i Job
	err := row.Scan(
		&i.ID,
		&i.JobType,
		&i.Name,
		&i.Description,
		&i.Status,
		&i.CrawlConfigID,
		&i.ScheduledAt,
		&i.StartedAt,
		&i.CompletedAt,
		&i.CurrentStep,
		&i.TotalSteps,
		&i.ProgressMessage,
		&i.ProgressPercentage,
		&i.MaxRetries,
		&i.RetryCount,
		&i.LastError,
		&i.TimeoutSeconds,
		&i.CreatedBy,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
