package repository

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
)

type RepositoryAuthCrawl interface {
	// Command operations
	Create(ctx context.Context, auth *entity.Auth) error
	CreateMany(ctx context.Context, auths []*entity.Auth) error
	Update(ctx context.Context,
		id int,
		updateFn func(p *entity.Auth) (*entity.Auth, error),
	) error

	UpdateMany(ctx context.Context,
		ids []int,
		updateFn func(p []*entity.Auth) ([]*entity.Auth, error),
	) error

	Delete(ctx context.Context, id int) error
	DeleteMany(ctx context.Context, ids []int) error
}
