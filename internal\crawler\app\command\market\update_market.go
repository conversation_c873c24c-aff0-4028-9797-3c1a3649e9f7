package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateMarket struct {
	ID               int
	Name             string
	DisplayName      string
	Type             string
	BaseURL          string
	Currency         string
	BuyerFeePercent  float64
	SellerFeePercent float64
	CountryCode      string
	Language         string
	Description      string
	UpdatedBy        string
}

type UpdateMarketHandler decorator.CommandHandler[UpdateMarket]

type updateMarketHandler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewUpdateMarketHandler(
	repo repository.RepositoryMarket,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateMarketHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateMarket](
		updateMarketHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateMarketHandler) Handle(ctx context.Context, cmd UpdateMarket) error {
	return h.repo.Update(ctx, cmd.ID, func(m *entity.Market) (*entity.Market, error) {
		err := m.UpdateInfo(
			cmd.Name,
			cmd.DisplayName,
			cmd.Type,
			cmd.BaseURL,
			cmd.Currency,
			cmd.BuyerFeePercent,
			cmd.SellerFeePercent,
			cmd.CountryCode,
			cmd.Language,
			cmd.Description,
		)
		if err != nil {
			return nil, err
		}
		return m, nil
	})
}
