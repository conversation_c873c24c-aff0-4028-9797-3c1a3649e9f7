-- Create markets table for market domain
-- This table stores all market information including configuration and status


CREATE TABLE markets (
                         id serial PRIMARY KEY,
                         name VARCHAR(100) NOT NULL UNIQUE,
                         display_name VARCHAR(100) NOT NULL,
                         market_type VARCHAR(100) NOT NULL,
                         status VARCHAR(20) NOT NULL DEFAULT 'active',
                         base_url varchar(200) NOT NULL,
                         currency VARCHAR(10) NOT NULL,
                         buyer_fee_percent DECIMAL(3,2) NOT NULL DEFAULT 0.00,
                         seller_fee_percent DECIMAL(3,2) NOT NULL DEFAULT 0.00,
                         country_code VARCHAR(3) NOT NULL,
                         language VARCHAR(10) NOT NULL,
                         description TEXT NOT NULL,
                         is_active BOOLEAN NOT NULL DEFAULT true,
                         last_crawl_at TIMESTAMPTZ,
                         created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
                         updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

