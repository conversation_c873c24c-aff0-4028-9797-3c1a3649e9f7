-- name: Create :one
INSERT INTO markets (
    name,
    display_name,
    market_type,
    status,
    base_url,
    currency,
    buyer_fee_percent,
    seller_fee_percent,
    country_code,
    language,
    description,
    is_active,
    last_crawl_at
) VALUES (
             $1, $2, $3,
          $4, $5, $6, $7,
          $8, $9, $10, $11,
          $12, $13
         )
RETURNING id;

-- name: CreateMany :copyfrom
INSERT INTO markets (
    name,
    display_name,
    market_type,
    status,
    base_url,
    currency,
    buyer_fee_percent,
    seller_fee_percent,
    country_code,
    language,
    description,
    is_active,
    last_crawl_at
)VALUES
    /* repeat this for each product */
     ($1, $2, $3, $4, $5, $6,
      $7, $8, $9,
      $10, $11, $12, $13);

-- name: FindByID :one
SELECT * FROM markets WHERE id = $1 LIMIT 1;

-- name: FindByName :one
SELECT * FROM markets WHERE name = $1 LIMIT 1;


-- name: UpdateMarket :one
UPDATE markets
SET name = $2,
    display_name= $3,
    market_type= $4,
    status= $5,
    base_url= $6,
    currency= $7,
    buyer_fee_percent= $8,
    seller_fee_percent= $9,
    country_code= $10,
    language= $11,
    description= $12,
    is_active= $13,
    last_crawl_at= $14
WHERE id= $1
RETURNING *;

-- Query methods for MarketQueryRepository

-- name: GetAllMarketsWithPagination :many
SELECT * FROM markets
ORDER BY
    CASE WHEN $3 = 'name' THEN name END ASC,
    CASE WHEN $3 = 'name_desc' THEN name END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2;

-- name: GetMarketByIdQuery :one
SELECT * FROM markets WHERE id = $1 LIMIT 1;

-- name: GetActiveMarketsQuery :many
SELECT * FROM markets WHERE is_active = true ORDER BY name ASC;

-- name: CountMarketsQuery :one
SELECT COUNT(*) FROM markets;

-- name: FilterMarketsQuery :many
SELECT * FROM markets
WHERE
    ($1 = '' OR market_type = $1)
    AND ($2 = '' OR status = $2)
    AND ($3 = '' OR currency = $3)
    AND ($4 = false OR is_active = $4)
    AND ($5 = '' OR country_code = $5)
ORDER BY
    CASE WHEN $6 = 'name' THEN name END ASC,
    CASE WHEN $6 = 'name_desc' THEN name END DESC,
    CASE WHEN $6 = 'created_at' THEN created_at END ASC,
    CASE WHEN $6 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $6 = '' OR $6 IS NULL THEN id END ASC;
