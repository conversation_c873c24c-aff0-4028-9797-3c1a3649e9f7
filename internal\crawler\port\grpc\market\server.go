package grpc

import (
	"context"
	app2 "go_core_market/internal/crawler/app"
	commandmarket "go_core_market/internal/crawler/app/command/market"
	querymarket "go_core_market/internal/crawler/app/query/market"
	pb "go_core_market/pkg/pb/market"
)

type MarketGRPCServer struct {
	app app2.Application
	pb.UnimplementedMarketServiceServer
}

func NewMarketGRPCServer(app app2.Application) *MarketGRPCServer {
	return &MarketGRPCServer{
		app: app,
	}
}

// Command handlers
func (s *MarketGRPCServer) CreateMarket(ctx context.Context, req *pb.CreateMarketRequest) (*pb.CreateMarketResponse, error) {
	cmd := commandmarket.CreateMarket{
		Name:             req.Name,
		DisplayName:      req.DisplayName,
		Type:             req.Type,
		BaseURL:          req.BaseUrl,
		Currency:         req.Currency,
		BuyerFeePercent:  req.BuyerFeePercent,
		SellerFeePercent: req.SellerFeePercent,
		CountryCode:      req.CountryCode,
		Language:         req.Language,
		Description:      req.Description,
		CreatedBy:        req.CreatedBy,
	}

	err := s.app.Commands.Market.CreateMarket.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateMarketResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateMarketResponse{
		Success: true,
		Message: "Market created successfully",
	}, nil
}

func (s *MarketGRPCServer) UpdateMarket(ctx context.Context, req *pb.UpdateMarketRequest) (*pb.UpdateMarketResponse, error) {

	return &pb.UpdateMarketResponse{
		Success: true,
		Message: "Market updated successfully",
	}, nil
}

func (s *MarketGRPCServer) DeleteMarket(ctx context.Context, req *pb.DeleteMarketRequest) (*pb.DeleteMarketResponse, error) {
	cmd := commandmarket.DeleteMarket{
		ID:        int(req.Id),
		DeletedBy: req.DeletedBy,
	}

	err := s.app.Commands.Market.DeleteMarket.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeleteMarketResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteMarketResponse{
		Success: true,
		Message: "Market deleted successfully",
	}, nil
}

func (s *MarketGRPCServer) ActiveMarket(ctx context.Context, req *pb.ActiveMarketRequest) (*pb.ActiveMarketResponse, error) {
	cmd := commandmarket.ActiveMarket{
		MarketId:    int(req.MarketId),
		ActivatedBy: req.ActivatedBy,
	}

	err := s.app.Commands.Market.ActiveMarket.Handle(ctx, cmd)
	if err != nil {
		return &pb.ActiveMarketResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ActiveMarketResponse{
		Success: true,
		Message: "Market activated successfully",
	}, nil
}

func (s *MarketGRPCServer) DeactiveMarket(ctx context.Context, req *pb.DeactiveMarketRequest) (*pb.DeactiveMarketResponse, error) {
	cmd := commandmarket.DeactivateMarket{
		MarketId:      int(req.MarketId),
		DeactivatedBy: req.DeactivatedBy,
	}

	err := s.app.Commands.Market.DeactivateMarket.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeactiveMarketResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeactiveMarketResponse{
		Success: true,
		Message: "Market deactivated successfully",
	}, nil
}

func (s *MarketGRPCServer) SetMarketMaintenance(ctx context.Context, req *pb.SetMarketMaintenanceRequest) (*pb.SetMarketMaintenanceResponse, error) {
	cmd := commandmarket.SetMarketMaintenance{
		MarketId:     int(req.MarketId),
		MaintainedBy: req.MaintainedBy,
	}

	err := s.app.Commands.Market.SetMarketMaintenance.Handle(ctx, cmd)
	if err != nil {
		return &pb.SetMarketMaintenanceResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.SetMarketMaintenanceResponse{
		Success: true,
		Message: "Market set to maintenance successfully",
	}, nil
}

// Query handlers
func (s *MarketGRPCServer) GetAllMarkets(ctx context.Context, req *pb.GetAllMarketsRequest) (*pb.GetAllMarketsResponse, error) {
	q := querymarket.GetAllMarketsQuery{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		OrderBy:  req.OrderBy,
	}

	result, err := s.app.Queries.Market.GetAllMarkets.Handle(ctx, q)
	if err != nil {
		return nil, err
	}
	var pbMarkets []*pb.Market
	for _, market := range result.Data {
		pbMarkets = append(pbMarkets, MarketToProto(market))
	}

	return &pb.GetAllMarketsResponse{
		Markets:    pbMarkets,
		Total:      int32(result.Pagination.Total),
		Page:       int32(result.Pagination.Page),
		PageSize:   int32(result.Pagination.PageSize),
		TotalPages: int32(result.Pagination.Pages),
	}, nil
}

func (s *MarketGRPCServer) GetMarketById(ctx context.Context, req *pb.GetMarketByIdRequest) (*pb.GetMarketByIdResponse, error) {
	q := querymarket.GetMarketByIdQuery{
		Id: int(req.Id),
	}

	market, err := s.app.Queries.Market.GetMarketById.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetMarketByIdResponse{
		Market: MarketToProto(market),
	}, nil
}

func (s *MarketGRPCServer) GetActiveMarkets(ctx context.Context, req *pb.GetActiveMarketsRequest) (*pb.GetActiveMarketsResponse, error) {
	q := querymarket.GetActiveMarketsQuery{}

	markets, err := s.app.Queries.Market.GetActiveMarkets.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbMarkets []*pb.Market
	for _, market := range markets {
		pbMarkets = append(pbMarkets, MarketToProto(market))
	}

	return &pb.GetActiveMarketsResponse{
		Markets: pbMarkets,
	}, nil
}

func (s *MarketGRPCServer) FilterMarkets(ctx context.Context, req *pb.FilterMarketsRequest) (*pb.FilterMarketsResponse, error) {
	q := querymarket.FilterMarketsQuery{
		Types:       req.Types,
		Statuses:    req.Statuses,
		Currencies:  req.Currencies,
		CountryCode: req.CountryCode,
		OrderBy:     req.OrderBy,
	}

	if req.IsActive != nil {
		q.IsActive = req.IsActive
	}

	markets, err := s.app.Queries.Market.FilterMarkets.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbMarkets []*pb.Market
	for _, market := range markets {
		pbMarkets = append(pbMarkets, MarketToProto(market))
	}

	return &pb.FilterMarketsResponse{
		Markets: pbMarkets,
	}, nil
}
