// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package sqlc

import (
	"context"
)

// iteratorForCreateMany implements pgx.CopyFromSource.
type iteratorForCreateMany struct {
	rows                 []CreateManyParams
	skippedFirstNextCall bool
}

func (r *iteratorForCreateMany) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForCreateMany) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].MarketID,
		r.rows[0].Description,
		r.rows[0].TypeConfig,
		r.rows[0].RequestsPerMinute,
		r.rows[0].RequireAuth,
		r.rows[0].MaxNumberAuth,
		r.rows[0].MaxNumberProxy,
		r.rows[0].PerRequestDelaySeconds,
		r.rows[0].TimeoutSeconds,
		r.rows[0].MaxRetries,
		r.rows[0].RetryDelaySeconds,
		r.rows[0].MaxConcurrent,
		r.rows[0].IsActive,
	}, nil
}

func (r iteratorForCreateMany) Err() error {
	return nil
}

func (q *Queries) CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"crawl_configs"}, []string{"market_id", "description", "type_config", "requests_per_minute", "require_auth", "max_number_auth", "max_number_proxy", "per_request_delay_seconds", "timeout_seconds", "max_retries", "retry_delay_seconds", "max_concurrent", "is_active"}, &iteratorForCreateMany{rows: arg})
}
