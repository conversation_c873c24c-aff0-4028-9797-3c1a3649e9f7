// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: markets.sql

package sqlc

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const CountMarketsQuery = `-- name: CountMarketsQuery :one
SELECT COUNT(*) FROM markets
`

func (q *Queries) CountMarketsQuery(ctx context.Context) (int64, error) {
	row := q.db.QueryRow(ctx, CountMarketsQuery)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const Create = `-- name: Create :one
INSERT INTO markets (
    name,
    display_name,
    market_type,
    status,
    base_url,
    currency,
    buyer_fee_percent,
    seller_fee_percent,
    country_code,
    language,
    description,
    is_active,
    last_crawl_at
) VALUES (
             $1, $2, $3,
          $4, $5, $6, $7,
          $8, $9, $10, $11,
          $12, $13
         )
RETURNING id
`

type CreateParams struct {
	Name             string             `db:"name" json:"name"`
	DisplayName      string             `db:"display_name" json:"display_name"`
	MarketType       string             `db:"market_type" json:"market_type"`
	Status           string             `db:"status" json:"status"`
	BaseUrl          string             `db:"base_url" json:"base_url"`
	Currency         string             `db:"currency" json:"currency"`
	BuyerFeePercent  float64            `db:"buyer_fee_percent" json:"buyer_fee_percent"`
	SellerFeePercent float64            `db:"seller_fee_percent" json:"seller_fee_percent"`
	CountryCode      string             `db:"country_code" json:"country_code"`
	Language         string             `db:"language" json:"language"`
	Description      string             `db:"description" json:"description"`
	IsActive         bool               `db:"is_active" json:"is_active"`
	LastCrawlAt      pgtype.Timestamptz `db:"last_crawl_at" json:"last_crawl_at"`
}

func (q *Queries) Create(ctx context.Context, arg CreateParams) (int, error) {
	row := q.db.QueryRow(ctx, Create,
		arg.Name,
		arg.DisplayName,
		arg.MarketType,
		arg.Status,
		arg.BaseUrl,
		arg.Currency,
		arg.BuyerFeePercent,
		arg.SellerFeePercent,
		arg.CountryCode,
		arg.Language,
		arg.Description,
		arg.IsActive,
		arg.LastCrawlAt,
	)
	var id int
	err := row.Scan(&id)
	return id, err
}

type CreateManyParams struct {
	Name             string             `db:"name" json:"name"`
	DisplayName      string             `db:"display_name" json:"display_name"`
	MarketType       string             `db:"market_type" json:"market_type"`
	Status           string             `db:"status" json:"status"`
	BaseUrl          string             `db:"base_url" json:"base_url"`
	Currency         string             `db:"currency" json:"currency"`
	BuyerFeePercent  float64            `db:"buyer_fee_percent" json:"buyer_fee_percent"`
	SellerFeePercent float64            `db:"seller_fee_percent" json:"seller_fee_percent"`
	CountryCode      string             `db:"country_code" json:"country_code"`
	Language         string             `db:"language" json:"language"`
	Description      string             `db:"description" json:"description"`
	IsActive         bool               `db:"is_active" json:"is_active"`
	LastCrawlAt      pgtype.Timestamptz `db:"last_crawl_at" json:"last_crawl_at"`
}

const FilterMarketsQuery = `-- name: FilterMarketsQuery :many
SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets
WHERE
    ($1 = '' OR market_type = $1)
    AND ($2 = '' OR status = $2)
    AND ($3 = '' OR currency = $3)
    AND ($4 = false OR is_active = $4)
    AND ($5 = '' OR country_code = $5)
ORDER BY
    CASE WHEN $6 = 'name' THEN name END ASC,
    CASE WHEN $6 = 'name_desc' THEN name END DESC,
    CASE WHEN $6 = 'created_at' THEN created_at END ASC,
    CASE WHEN $6 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $6 = '' OR $6 IS NULL THEN id END ASC
`

type FilterMarketsQueryParams struct {
	Column1 interface{} `db:"column_1" json:"column_1"`
	Column2 interface{} `db:"column_2" json:"column_2"`
	Column3 interface{} `db:"column_3" json:"column_3"`
	Column4 interface{} `db:"column_4" json:"column_4"`
	Column5 interface{} `db:"column_5" json:"column_5"`
	Column6 interface{} `db:"column_6" json:"column_6"`
}

func (q *Queries) FilterMarketsQuery(ctx context.Context, arg FilterMarketsQueryParams) ([]*Market, error) {
	rows, err := q.db.Query(ctx, FilterMarketsQuery,
		arg.Column1,
		arg.Column2,
		arg.Column3,
		arg.Column4,
		arg.Column5,
		arg.Column6,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Market{}
	for rows.Next() {
		var i Market
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.DisplayName,
			&i.MarketType,
			&i.Status,
			&i.BaseUrl,
			&i.Currency,
			&i.BuyerFeePercent,
			&i.SellerFeePercent,
			&i.CountryCode,
			&i.Language,
			&i.Description,
			&i.IsActive,
			&i.LastCrawlAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const FindByID = `-- name: FindByID :one
SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets WHERE id = $1 LIMIT 1
`

func (q *Queries) FindByID(ctx context.Context, id int) (*Market, error) {
	row := q.db.QueryRow(ctx, FindByID, id)
	var i Market
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.DisplayName,
		&i.MarketType,
		&i.Status,
		&i.BaseUrl,
		&i.Currency,
		&i.BuyerFeePercent,
		&i.SellerFeePercent,
		&i.CountryCode,
		&i.Language,
		&i.Description,
		&i.IsActive,
		&i.LastCrawlAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const FindByName = `-- name: FindByName :one
SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets WHERE name = $1 LIMIT 1
`

func (q *Queries) FindByName(ctx context.Context, name string) (*Market, error) {
	row := q.db.QueryRow(ctx, FindByName, name)
	var i Market
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.DisplayName,
		&i.MarketType,
		&i.Status,
		&i.BaseUrl,
		&i.Currency,
		&i.BuyerFeePercent,
		&i.SellerFeePercent,
		&i.CountryCode,
		&i.Language,
		&i.Description,
		&i.IsActive,
		&i.LastCrawlAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const GetActiveMarketsQuery = `-- name: GetActiveMarketsQuery :many
SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets WHERE is_active = true ORDER BY name ASC
`

func (q *Queries) GetActiveMarketsQuery(ctx context.Context) ([]*Market, error) {
	rows, err := q.db.Query(ctx, GetActiveMarketsQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Market{}
	for rows.Next() {
		var i Market
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.DisplayName,
			&i.MarketType,
			&i.Status,
			&i.BaseUrl,
			&i.Currency,
			&i.BuyerFeePercent,
			&i.SellerFeePercent,
			&i.CountryCode,
			&i.Language,
			&i.Description,
			&i.IsActive,
			&i.LastCrawlAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetAllMarketsWithPagination = `-- name: GetAllMarketsWithPagination :many

SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets
ORDER BY
    CASE WHEN $3 = 'name' THEN name END ASC,
    CASE WHEN $3 = 'name_desc' THEN name END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2
`

type GetAllMarketsWithPaginationParams struct {
	Limit   int32       `db:"limit" json:"limit"`
	Offset  int32       `db:"offset" json:"offset"`
	Column3 interface{} `db:"column_3" json:"column_3"`
}

// Query methods for MarketQueryRepository
func (q *Queries) GetAllMarketsWithPagination(ctx context.Context, arg GetAllMarketsWithPaginationParams) ([]*Market, error) {
	rows, err := q.db.Query(ctx, GetAllMarketsWithPagination, arg.Limit, arg.Offset, arg.Column3)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []*Market{}
	for rows.Next() {
		var i Market
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.DisplayName,
			&i.MarketType,
			&i.Status,
			&i.BaseUrl,
			&i.Currency,
			&i.BuyerFeePercent,
			&i.SellerFeePercent,
			&i.CountryCode,
			&i.Language,
			&i.Description,
			&i.IsActive,
			&i.LastCrawlAt,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, &i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const GetMarketByIdQuery = `-- name: GetMarketByIdQuery :one
SELECT id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at FROM markets WHERE id = $1 LIMIT 1
`

func (q *Queries) GetMarketByIdQuery(ctx context.Context, id int) (*Market, error) {
	row := q.db.QueryRow(ctx, GetMarketByIdQuery, id)
	var i Market
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.DisplayName,
		&i.MarketType,
		&i.Status,
		&i.BaseUrl,
		&i.Currency,
		&i.BuyerFeePercent,
		&i.SellerFeePercent,
		&i.CountryCode,
		&i.Language,
		&i.Description,
		&i.IsActive,
		&i.LastCrawlAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}

const UpdateMarket = `-- name: UpdateMarket :one
UPDATE markets
SET name = $2,
    display_name= $3,
    market_type= $4,
    status= $5,
    base_url= $6,
    currency= $7,
    buyer_fee_percent= $8,
    seller_fee_percent= $9,
    country_code= $10,
    language= $11,
    description= $12,
    is_active= $13,
    last_crawl_at= $14
WHERE id= $1
RETURNING id, name, display_name, market_type, status, base_url, currency, buyer_fee_percent, seller_fee_percent, country_code, language, description, is_active, last_crawl_at, created_at, updated_at
`

type UpdateMarketParams struct {
	ID               int                `db:"id" json:"id"`
	Name             string             `db:"name" json:"name"`
	DisplayName      string             `db:"display_name" json:"display_name"`
	MarketType       string             `db:"market_type" json:"market_type"`
	Status           string             `db:"status" json:"status"`
	BaseUrl          string             `db:"base_url" json:"base_url"`
	Currency         string             `db:"currency" json:"currency"`
	BuyerFeePercent  float64            `db:"buyer_fee_percent" json:"buyer_fee_percent"`
	SellerFeePercent float64            `db:"seller_fee_percent" json:"seller_fee_percent"`
	CountryCode      string             `db:"country_code" json:"country_code"`
	Language         string             `db:"language" json:"language"`
	Description      string             `db:"description" json:"description"`
	IsActive         bool               `db:"is_active" json:"is_active"`
	LastCrawlAt      pgtype.Timestamptz `db:"last_crawl_at" json:"last_crawl_at"`
}

func (q *Queries) UpdateMarket(ctx context.Context, arg UpdateMarketParams) (*Market, error) {
	row := q.db.QueryRow(ctx, UpdateMarket,
		arg.ID,
		arg.Name,
		arg.DisplayName,
		arg.MarketType,
		arg.Status,
		arg.BaseUrl,
		arg.Currency,
		arg.BuyerFeePercent,
		arg.SellerFeePercent,
		arg.CountryCode,
		arg.Language,
		arg.Description,
		arg.IsActive,
		arg.LastCrawlAt,
	)
	var i Market
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.DisplayName,
		&i.MarketType,
		&i.Status,
		&i.BaseUrl,
		&i.Currency,
		&i.BuyerFeePercent,
		&i.SellerFeePercent,
		&i.CountryCode,
		&i.Language,
		&i.Description,
		&i.IsActive,
		&i.LastCrawlAt,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return &i, err
}
