package commandjob

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

// StartJob command
type StartJob struct {
	ID        int
	StartedBy string
}

type StartJobHandler decorator.CommandHandler[StartJob]

type startJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewStartJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) StartJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[StartJob](
		startJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h startJobHandler) Handle(ctx context.Context, cmd StartJob) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		err := j.Start()
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}

// CompleteJob command
type CompleteJob struct {
	ID          int
	CompletedBy string
}

type CompleteJobHandler decorator.CommandHandler[CompleteJob]

type completeJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewCompleteJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CompleteJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CompleteJob](
		completeJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h completeJobHandler) Handle(ctx context.Context, cmd CompleteJob) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		err := j.Complete()
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}

// FailJob command
type FailJob struct {
	ID           int
	ErrorMessage string
	FailedBy     string
}

type FailJobHandler decorator.CommandHandler[FailJob]

type failJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewFailJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) FailJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[FailJob](
		failJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h failJobHandler) Handle(ctx context.Context, cmd FailJob) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		err := j.Fail(cmd.ErrorMessage)
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}

// CancelJob command
type CancelJob struct {
	ID          int
	CancelledBy string
}

type CancelJobHandler decorator.CommandHandler[CancelJob]

type cancelJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewCancelJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CancelJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CancelJob](
		cancelJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h cancelJobHandler) Handle(ctx context.Context, cmd CancelJob) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		err := j.Cancel()
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}

// RetryJob command
type RetryJob struct {
	ID        int
	RetriedBy string
}

type RetryJobHandler decorator.CommandHandler[RetryJob]

type retryJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewRetryJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) RetryJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[RetryJob](
		retryJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h retryJobHandler) Handle(ctx context.Context, cmd RetryJob) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		err := j.Retry()
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}
