package commandjob

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateJobProgress struct {
	ID          int
	CurrentStep int
	TotalSteps  int
	Message     string
	Percentage  int
	UpdatedBy   string
}

type UpdateJobProgressHandler decorator.CommandHandler[UpdateJobProgress]

type updateJobProgressHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewUpdateJobProgressHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateJobProgressHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateJobProgress](
		updateJobProgressHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateJobProgressHandler) Handle(ctx context.Context, cmd UpdateJobProgress) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		progress := &entity.JobProgress{
			CurrentStep: cmd.CurrentStep,
			TotalSteps:  cmd.TotalSteps,
			Message:     cmd.Message,
			Percentage:  cmd.Percentage,
		}

		err := j.UpdateProgress(progress)
		if err != nil {
			return nil, err
		}
		return j, nil
	})
}
