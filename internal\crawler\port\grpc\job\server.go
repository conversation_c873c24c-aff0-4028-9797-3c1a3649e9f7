package grpc

import (
	"context"
	app2 "go_core_market/internal/crawler/app"
	commandjob "go_core_market/internal/crawler/app/command/job"
	queryjob "go_core_market/internal/crawler/app/query/job"
	pb "go_core_market/pkg/pb/job"
	"time"
)

type JobGRPCServer struct {
	app app2.Application
	pb.UnimplementedJobServiceServer
}

func NewJobGRPCServer(app app2.Application) *JobGRPCServer {
	return &JobGRPCServer{
		app: app,
	}
}

// Command handlers
func (s *JobGRPCServer) CreateJob(ctx context.Context, req *pb.CreateJobRequest) (*pb.CreateJobResponse, error) {
	var scheduledAt *time.Time
	if req.ScheduledAt != nil {
		t := req.ScheduledAt.AsTime()
		scheduledAt = &t
	}

	var crawlConfigId *int
	if req.CrawlConfigId != nil {
		id := int(*req.CrawlConfigId)
		crawlConfigId = &id
	}

	cmd := commandjob.CreateJob{
		JobType:        req.JobType,
		Name:           req.Name,
		Description:    nil,
		CrawlConfigId:  crawlConfigId,
		ScheduledAt:    scheduledAt,
		MaxRetries:     int(req.MaxRetries),
		TimeoutSeconds: int(req.TimeoutSeconds),
		CreatedBy:      req.CreatedBy,
	}

	err := s.app.Commands.Job.CreateJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateJobResponse{
		Success: true,
		Message: "Job created successfully",
	}, nil
}

func (s *JobGRPCServer) UpdateJob(ctx context.Context, req *pb.UpdateJobRequest) (*pb.UpdateJobResponse, error) {

	return &pb.UpdateJobResponse{
		Success: true,
		Message: "Job updated successfully",
	}, nil
}

func (s *JobGRPCServer) UpdateJobStatus(ctx context.Context, req *pb.UpdateJobStatusRequest) (*pb.UpdateJobStatusResponse, error) {
	cmd := commandjob.UpdateJobStatus{
		ID:        int(req.Id),
		Status:    req.Status,
		UpdatedBy: req.UpdatedBy,
	}

	err := s.app.Commands.Job.UpdateJobStatus.Handle(ctx, cmd)
	if err != nil {
		return &pb.UpdateJobStatusResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.UpdateJobStatusResponse{
		Success: true,
		Message: "Job status updated successfully",
	}, nil
}

func (s *JobGRPCServer) UpdateJobProgress(ctx context.Context, req *pb.UpdateJobProgressRequest) (*pb.UpdateJobProgressResponse, error) {
	cmd := commandjob.UpdateJobProgress{
		ID:          int(req.Id),
		CurrentStep: int(req.Progress.CurrentStep),
		TotalSteps:  int(req.Progress.TotalSteps),
		Message:     req.Progress.Message,
		Percentage:  int(req.Progress.Percentage),
		UpdatedBy:   req.UpdatedBy,
	}

	err := s.app.Commands.Job.UpdateJobProgress.Handle(ctx, cmd)
	if err != nil {
		return &pb.UpdateJobProgressResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.UpdateJobProgressResponse{
		Success: true,
		Message: "Job progress updated successfully",
	}, nil
}

func (s *JobGRPCServer) StartJob(ctx context.Context, req *pb.StartJobRequest) (*pb.StartJobResponse, error) {
	cmd := commandjob.StartJob{
		ID:        int(req.Id),
		StartedBy: req.StartedBy,
	}

	err := s.app.Commands.Job.StartJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.StartJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.StartJobResponse{
		Success: true,
		Message: "Job started successfully",
	}, nil
}

func (s *JobGRPCServer) CompleteJob(ctx context.Context, req *pb.CompleteJobRequest) (*pb.CompleteJobResponse, error) {
	cmd := commandjob.CompleteJob{
		ID:          int(req.Id),
		CompletedBy: req.CompletedBy,
	}

	err := s.app.Commands.Job.CompleteJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.CompleteJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CompleteJobResponse{
		Success: true,
		Message: "Job completed successfully",
	}, nil
}

func (s *JobGRPCServer) FailJob(ctx context.Context, req *pb.FailJobRequest) (*pb.FailJobResponse, error) {
	cmd := commandjob.FailJob{
		ID:           int(req.Id),
		ErrorMessage: req.ErrorMessage,
		FailedBy:     req.FailedBy,
	}

	err := s.app.Commands.Job.FailJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.FailJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.FailJobResponse{
		Success: true,
		Message: "Job failed successfully",
	}, nil
}

func (s *JobGRPCServer) CancelJob(ctx context.Context, req *pb.CancelJobRequest) (*pb.CancelJobResponse, error) {
	cmd := commandjob.CancelJob{
		ID:          int(req.Id),
		CancelledBy: req.CancelledBy,
	}

	err := s.app.Commands.Job.CancelJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.CancelJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CancelJobResponse{
		Success: true,
		Message: "Job cancelled successfully",
	}, nil
}

func (s *JobGRPCServer) RetryJob(ctx context.Context, req *pb.RetryJobRequest) (*pb.RetryJobResponse, error) {
	cmd := commandjob.RetryJob{
		ID:        int(req.Id),
		RetriedBy: req.RetriedBy,
	}

	err := s.app.Commands.Job.RetryJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.RetryJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.RetryJobResponse{
		Success: true,
		Message: "Job retried successfully",
	}, nil
}

func (s *JobGRPCServer) DeleteJob(ctx context.Context, req *pb.DeleteJobRequest) (*pb.DeleteJobResponse, error) {
	cmd := commandjob.DeleteJob{
		ID:        int(req.Id),
		DeletedBy: req.DeletedBy,
	}

	err := s.app.Commands.Job.DeleteJob.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeleteJobResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteJobResponse{
		Success: true,
		Message: "Job deleted successfully",
	}, nil
}

// Query handlers
func (s *JobGRPCServer) GetAllJobs(ctx context.Context, req *pb.GetAllJobsRequest) (*pb.GetAllJobsResponse, error) {
	q := queryjob.GetAllJobsQuery{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		OrderBy:  req.OrderBy,
	}

	result, err := s.app.Queries.Job.GetAllJobs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbJobs []*pb.Job
	for _, job := range result.Data {
		pbJobs = append(pbJobs, JobToProto(job))
	}

	return &pb.GetAllJobsResponse{
		Jobs:       pbJobs,
		Total:      int32(result.Pagination.Total),
		Page:       int32(result.Pagination.Page),
		PageSize:   int32(result.Pagination.PageSize),
		TotalPages: int32(result.Pagination.Pages),
	}, nil
}

func (s *JobGRPCServer) GetJobById(ctx context.Context, req *pb.GetJobByIdRequest) (*pb.GetJobByIdResponse, error) {
	q := queryjob.GetJobByIdQuery{
		Id: int(req.Id),
	}

	job, err := s.app.Queries.Job.GetJobById.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetJobByIdResponse{
		Job: JobToProto(job),
	}, nil
}

func (s *JobGRPCServer) GetJobsByStatus(ctx context.Context, req *pb.GetJobsByStatusRequest) (*pb.GetJobsByStatusResponse, error) {
	q := queryjob.GetJobsByStatusQuery{
		Status: req.Status,
	}

	jobs, err := s.app.Queries.Job.GetJobsByStatus.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetJobsByStatusResponse{
		Jobs: JobsToProto(jobs),
	}, nil
}

func (s *JobGRPCServer) GetJobsByType(ctx context.Context, req *pb.GetJobsByTypeRequest) (*pb.GetJobsByTypeResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetJobsByTypeResponse{
		Jobs: []*pb.Job{},
	}, nil
}

func (s *JobGRPCServer) GetPendingJobs(ctx context.Context, req *pb.GetPendingJobsRequest) (*pb.GetPendingJobsResponse, error) {
	q := queryjob.GetPendingJobsQuery{}

	jobs, err := s.app.Queries.Job.GetPendingJobs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetPendingJobsResponse{
		Jobs: JobsToProto(jobs),
	}, nil
}

func (s *JobGRPCServer) GetRunningJobs(ctx context.Context, req *pb.GetRunningJobsRequest) (*pb.GetRunningJobsResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetRunningJobsResponse{
		Jobs: []*pb.Job{},
	}, nil
}

func (s *JobGRPCServer) GetJobsByConfigId(ctx context.Context, req *pb.GetJobsByConfigIdRequest) (*pb.GetJobsByConfigIdResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetJobsByConfigIdResponse{
		Jobs: []*pb.Job{},
	}, nil
}

func (s *JobGRPCServer) FilterJobs(ctx context.Context, req *pb.FilterJobsRequest) (*pb.FilterJobsResponse, error) {
	q := queryjob.FilterJobsQuery{
		OrderBy: req.OrderBy,
	}

	// Handle optional fields
	if req.JobType != nil {
		q.JobType = *req.JobType
	}
	if req.Status != nil {
		q.Status = *req.Status
	}
	if req.CrawlConfigId != nil {
		q.CrawlConfigId = int(*req.CrawlConfigId)
	}
	if req.CreatedBy != nil {
		q.CreatedBy = *req.CreatedBy
	}
	if req.CreatedFrom != nil {
		t := req.CreatedFrom.AsTime()
		q.CreatedFrom = &t
	}
	if req.CreatedTo != nil {
		t := req.CreatedTo.AsTime()
		q.CreatedTo = &t
	}

	jobs, err := s.app.Queries.Job.FilterJobs.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.FilterJobsResponse{
		Jobs: JobsToProto(jobs),
	}, nil
}

func (s *JobGRPCServer) GetJobsNeedingRetry(ctx context.Context, req *pb.GetJobsNeedingRetryRequest) (*pb.GetJobsNeedingRetryResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetJobsNeedingRetryResponse{
		Jobs: []*pb.Job{},
	}, nil
}

func (s *JobGRPCServer) GetExpiredJobs(ctx context.Context, req *pb.GetExpiredJobsRequest) (*pb.GetExpiredJobsResponse, error) {
	// TODO: Implement when query handler is created
	return &pb.GetExpiredJobsResponse{
		Jobs: []*pb.Job{},
	}, nil
}
