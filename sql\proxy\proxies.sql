-- name: CreateProxy :one
INSERT INTO proxies (
    host,
    port,
    user_name,
    password,
    is_active,
    is_use,
    last_used_at
) VALUES (
             $1, $2, $3, $4, $5, $6, $7
         )
    RETURNING id;


-- name: CreateProxies :copyfrom
INSERT INTO proxies (host, port, user_name, password, is_active, is_use, last_used_at)
VALUES ($1, $2, $3, $4, $5, $6, $7);

-- name: UpdateProxy :one
UPDATE proxies SET
                   host = $2,
                   port = $3,
                   user_name = $4,
                   password = $5,
                   is_active = $6,
                   is_use = $7,
                   last_used_at = $8,
                   updated_at = CURRENT_TIMESTAMP
WHERE id = $1
    RETURNING id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at;



-- name: DeleteProxy :exec
DELETE FROM proxies WHERE id = $1;

-- name: DeleteManyProxies :exec
DELETE FROM proxies WHERE id = ANY($1::int[]);

-- name: FindProxyByID :one
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies WHERE id = $1;

-- name: FindProxiesByIDs :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies WHERE id = ANY($1::int[]);

-- name: GetAllProxies :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies
ORDER BY
    CASE WHEN $3 = 'id' THEN id END,
    CASE WHEN $3 = 'host' THEN host END,
    CASE WHEN $3 = 'created_at' THEN created_at END,
    CASE WHEN $3 = '' OR $3 IS NULL THEN created_at END DESC
LIMIT $1 OFFSET $2;

-- name: CountProxies :one
SELECT COUNT(*) FROM proxies;

-- name: GetAvailableProxies :many
SELECT id, host, port, user_name, password, is_active, is_use, last_used_at, created_at, updated_at
FROM proxies
WHERE is_active = true AND is_use = false
ORDER BY last_used_at ASC NULLS FIRST
LIMIT $1;