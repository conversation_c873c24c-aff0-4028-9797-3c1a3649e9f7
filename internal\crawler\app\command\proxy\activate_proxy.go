package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type ActivateProxy struct {
	ID          int
	ActivatedBy string
}

type ActivateProxyHandler decorator.CommandHandler[ActivateProxy]

type activateProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewActivateProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ActivateProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ActivateProxy](
		activateProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h activateProxyHandler) Handle(ctx context.Context, cmd ActivateProxy) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Proxy) (*entity.Proxy, error) {
		err := p.Activate()
		if err != nil {
			return nil, err
		}
		return p, nil
	})

}

type DeactivateProxy struct {
	ID            int
	DeactivatedBy string
}

type DeactivateProxyHandler decorator.CommandHandler[DeactivateProxy]

type deactivateProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewDeactivateProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeactivateProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeactivateProxy](
		deactivateProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deactivateProxyHandler) Handle(ctx context.Context, cmd DeactivateProxy) error {
	return h.repo.Update(ctx, cmd.ID, func(p *entity.Proxy) (*entity.Proxy, error) {
		err := p.Deactivate()
		if err != nil {
			return nil, err
		}
		return p, nil
	})
}
