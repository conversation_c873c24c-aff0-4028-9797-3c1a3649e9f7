// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: configcrawl.proto

package configcrawl

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CrawlConfig message
type CrawlConfig struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MarketId               int32                  `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Description            string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	TypeConfig             string                 `protobuf:"bytes,4,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	RequestsPerMinute      int32                  `protobuf:"varint,5,opt,name=requests_per_minute,json=requestsPerMinute,proto3" json:"requests_per_minute,omitempty"`
	RequireAuth            bool                   `protobuf:"varint,6,opt,name=require_auth,json=requireAuth,proto3" json:"require_auth,omitempty"`
	MaxNumberAuth          int32                  `protobuf:"varint,7,opt,name=max_number_auth,json=maxNumberAuth,proto3" json:"max_number_auth,omitempty"`
	MaxNumberProxy         int32                  `protobuf:"varint,8,opt,name=max_number_proxy,json=maxNumberProxy,proto3" json:"max_number_proxy,omitempty"`
	PerRequestDelaySeconds int32                  `protobuf:"varint,9,opt,name=per_request_delay_seconds,json=perRequestDelaySeconds,proto3" json:"per_request_delay_seconds,omitempty"`
	TimeoutSeconds         int32                  `protobuf:"varint,10,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	MaxRetries             int32                  `protobuf:"varint,11,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RetryDelaySeconds      int32                  `protobuf:"varint,12,opt,name=retry_delay_seconds,json=retryDelaySeconds,proto3" json:"retry_delay_seconds,omitempty"`
	MaxConcurrent          int32                  `protobuf:"varint,13,opt,name=max_concurrent,json=maxConcurrent,proto3" json:"max_concurrent,omitempty"`
	IsActive               bool                   `protobuf:"varint,14,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	LastUsedAt             *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=last_used_at,json=lastUsedAt,proto3,oneof" json:"last_used_at,omitempty"`
	CreatedAt              *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt              *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CrawlConfig) Reset() {
	*x = CrawlConfig{}
	mi := &file_configcrawl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrawlConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrawlConfig) ProtoMessage() {}

func (x *CrawlConfig) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrawlConfig.ProtoReflect.Descriptor instead.
func (*CrawlConfig) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{0}
}

func (x *CrawlConfig) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CrawlConfig) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *CrawlConfig) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CrawlConfig) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

func (x *CrawlConfig) GetRequestsPerMinute() int32 {
	if x != nil {
		return x.RequestsPerMinute
	}
	return 0
}

func (x *CrawlConfig) GetRequireAuth() bool {
	if x != nil {
		return x.RequireAuth
	}
	return false
}

func (x *CrawlConfig) GetMaxNumberAuth() int32 {
	if x != nil {
		return x.MaxNumberAuth
	}
	return 0
}

func (x *CrawlConfig) GetMaxNumberProxy() int32 {
	if x != nil {
		return x.MaxNumberProxy
	}
	return 0
}

func (x *CrawlConfig) GetPerRequestDelaySeconds() int32 {
	if x != nil {
		return x.PerRequestDelaySeconds
	}
	return 0
}

func (x *CrawlConfig) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *CrawlConfig) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *CrawlConfig) GetRetryDelaySeconds() int32 {
	if x != nil {
		return x.RetryDelaySeconds
	}
	return 0
}

func (x *CrawlConfig) GetMaxConcurrent() int32 {
	if x != nil {
		return x.MaxConcurrent
	}
	return 0
}

func (x *CrawlConfig) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CrawlConfig) GetLastUsedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUsedAt
	}
	return nil
}

func (x *CrawlConfig) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CrawlConfig) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response messages for Commands
type CreateCrawlConfigRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	MarketId               int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Description            string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	TypeConfig             string                 `protobuf:"bytes,3,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	RequestsPerMinute      int32                  `protobuf:"varint,4,opt,name=requests_per_minute,json=requestsPerMinute,proto3" json:"requests_per_minute,omitempty"`
	RequireAuth            bool                   `protobuf:"varint,5,opt,name=require_auth,json=requireAuth,proto3" json:"require_auth,omitempty"`
	MaxNumberAuth          int32                  `protobuf:"varint,6,opt,name=max_number_auth,json=maxNumberAuth,proto3" json:"max_number_auth,omitempty"`
	MaxNumberProxy         int32                  `protobuf:"varint,7,opt,name=max_number_proxy,json=maxNumberProxy,proto3" json:"max_number_proxy,omitempty"`
	PerRequestDelaySeconds int32                  `protobuf:"varint,8,opt,name=per_request_delay_seconds,json=perRequestDelaySeconds,proto3" json:"per_request_delay_seconds,omitempty"`
	TimeoutSeconds         int32                  `protobuf:"varint,9,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	MaxRetries             int32                  `protobuf:"varint,10,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RetryDelaySeconds      int32                  `protobuf:"varint,11,opt,name=retry_delay_seconds,json=retryDelaySeconds,proto3" json:"retry_delay_seconds,omitempty"`
	MaxConcurrent          int32                  `protobuf:"varint,12,opt,name=max_concurrent,json=maxConcurrent,proto3" json:"max_concurrent,omitempty"`
	CreatedBy              string                 `protobuf:"bytes,13,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CreateCrawlConfigRequest) Reset() {
	*x = CreateCrawlConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCrawlConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCrawlConfigRequest) ProtoMessage() {}

func (x *CreateCrawlConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCrawlConfigRequest.ProtoReflect.Descriptor instead.
func (*CreateCrawlConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCrawlConfigRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CreateCrawlConfigRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

func (x *CreateCrawlConfigRequest) GetRequestsPerMinute() int32 {
	if x != nil {
		return x.RequestsPerMinute
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetRequireAuth() bool {
	if x != nil {
		return x.RequireAuth
	}
	return false
}

func (x *CreateCrawlConfigRequest) GetMaxNumberAuth() int32 {
	if x != nil {
		return x.MaxNumberAuth
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetMaxNumberProxy() int32 {
	if x != nil {
		return x.MaxNumberProxy
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetPerRequestDelaySeconds() int32 {
	if x != nil {
		return x.PerRequestDelaySeconds
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetRetryDelaySeconds() int32 {
	if x != nil {
		return x.RetryDelaySeconds
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetMaxConcurrent() int32 {
	if x != nil {
		return x.MaxConcurrent
	}
	return 0
}

func (x *CreateCrawlConfigRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateCrawlConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ConfigId      *int32                 `protobuf:"varint,3,opt,name=config_id,json=configId,proto3,oneof" json:"config_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCrawlConfigResponse) Reset() {
	*x = CreateCrawlConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCrawlConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCrawlConfigResponse) ProtoMessage() {}

func (x *CreateCrawlConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCrawlConfigResponse.ProtoReflect.Descriptor instead.
func (*CreateCrawlConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{2}
}

func (x *CreateCrawlConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateCrawlConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CreateCrawlConfigResponse) GetConfigId() int32 {
	if x != nil && x.ConfigId != nil {
		return *x.ConfigId
	}
	return 0
}

type UpdateCrawlConfigRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MarketId               int32                  `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Description            string                 `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	TypeConfig             string                 `protobuf:"bytes,4,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	RequestsPerMinute      int32                  `protobuf:"varint,5,opt,name=requests_per_minute,json=requestsPerMinute,proto3" json:"requests_per_minute,omitempty"`
	RequireAuth            bool                   `protobuf:"varint,6,opt,name=require_auth,json=requireAuth,proto3" json:"require_auth,omitempty"`
	MaxNumberAuth          int32                  `protobuf:"varint,7,opt,name=max_number_auth,json=maxNumberAuth,proto3" json:"max_number_auth,omitempty"`
	MaxNumberProxy         int32                  `protobuf:"varint,8,opt,name=max_number_proxy,json=maxNumberProxy,proto3" json:"max_number_proxy,omitempty"`
	PerRequestDelaySeconds int32                  `protobuf:"varint,9,opt,name=per_request_delay_seconds,json=perRequestDelaySeconds,proto3" json:"per_request_delay_seconds,omitempty"`
	TimeoutSeconds         int32                  `protobuf:"varint,10,opt,name=timeout_seconds,json=timeoutSeconds,proto3" json:"timeout_seconds,omitempty"`
	MaxRetries             int32                  `protobuf:"varint,11,opt,name=max_retries,json=maxRetries,proto3" json:"max_retries,omitempty"`
	RetryDelaySeconds      int32                  `protobuf:"varint,12,opt,name=retry_delay_seconds,json=retryDelaySeconds,proto3" json:"retry_delay_seconds,omitempty"`
	MaxConcurrent          int32                  `protobuf:"varint,13,opt,name=max_concurrent,json=maxConcurrent,proto3" json:"max_concurrent,omitempty"`
	IsActive               bool                   `protobuf:"varint,14,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	UpdatedBy              string                 `protobuf:"bytes,15,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *UpdateCrawlConfigRequest) Reset() {
	*x = UpdateCrawlConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCrawlConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCrawlConfigRequest) ProtoMessage() {}

func (x *UpdateCrawlConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCrawlConfigRequest.ProtoReflect.Descriptor instead.
func (*UpdateCrawlConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCrawlConfigRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *UpdateCrawlConfigRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

func (x *UpdateCrawlConfigRequest) GetRequestsPerMinute() int32 {
	if x != nil {
		return x.RequestsPerMinute
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetRequireAuth() bool {
	if x != nil {
		return x.RequireAuth
	}
	return false
}

func (x *UpdateCrawlConfigRequest) GetMaxNumberAuth() int32 {
	if x != nil {
		return x.MaxNumberAuth
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetMaxNumberProxy() int32 {
	if x != nil {
		return x.MaxNumberProxy
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetPerRequestDelaySeconds() int32 {
	if x != nil {
		return x.PerRequestDelaySeconds
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetTimeoutSeconds() int32 {
	if x != nil {
		return x.TimeoutSeconds
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetMaxRetries() int32 {
	if x != nil {
		return x.MaxRetries
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetRetryDelaySeconds() int32 {
	if x != nil {
		return x.RetryDelaySeconds
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetMaxConcurrent() int32 {
	if x != nil {
		return x.MaxConcurrent
	}
	return 0
}

func (x *UpdateCrawlConfigRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *UpdateCrawlConfigRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateCrawlConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCrawlConfigResponse) Reset() {
	*x = UpdateCrawlConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCrawlConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCrawlConfigResponse) ProtoMessage() {}

func (x *UpdateCrawlConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCrawlConfigResponse.ProtoReflect.Descriptor instead.
func (*UpdateCrawlConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateCrawlConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateCrawlConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ActivateCrawlConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ActivatedBy   string                 `protobuf:"bytes,2,opt,name=activated_by,json=activatedBy,proto3" json:"activated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateCrawlConfigRequest) Reset() {
	*x = ActivateCrawlConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateCrawlConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateCrawlConfigRequest) ProtoMessage() {}

func (x *ActivateCrawlConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateCrawlConfigRequest.ProtoReflect.Descriptor instead.
func (*ActivateCrawlConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{5}
}

func (x *ActivateCrawlConfigRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivateCrawlConfigRequest) GetActivatedBy() string {
	if x != nil {
		return x.ActivatedBy
	}
	return ""
}

type ActivateCrawlConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateCrawlConfigResponse) Reset() {
	*x = ActivateCrawlConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateCrawlConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateCrawlConfigResponse) ProtoMessage() {}

func (x *ActivateCrawlConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateCrawlConfigResponse.ProtoReflect.Descriptor instead.
func (*ActivateCrawlConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{6}
}

func (x *ActivateCrawlConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ActivateCrawlConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeactivateCrawlConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeactivatedBy string                 `protobuf:"bytes,2,opt,name=deactivated_by,json=deactivatedBy,proto3" json:"deactivated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateCrawlConfigRequest) Reset() {
	*x = DeactivateCrawlConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateCrawlConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateCrawlConfigRequest) ProtoMessage() {}

func (x *DeactivateCrawlConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateCrawlConfigRequest.ProtoReflect.Descriptor instead.
func (*DeactivateCrawlConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{7}
}

func (x *DeactivateCrawlConfigRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeactivateCrawlConfigRequest) GetDeactivatedBy() string {
	if x != nil {
		return x.DeactivatedBy
	}
	return ""
}

type DeactivateCrawlConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateCrawlConfigResponse) Reset() {
	*x = DeactivateCrawlConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateCrawlConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateCrawlConfigResponse) ProtoMessage() {}

func (x *DeactivateCrawlConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateCrawlConfigResponse.ProtoReflect.Descriptor instead.
func (*DeactivateCrawlConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{8}
}

func (x *DeactivateCrawlConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeactivateCrawlConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateLastUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UsedBy        string                 `protobuf:"bytes,2,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLastUsedRequest) Reset() {
	*x = UpdateLastUsedRequest{}
	mi := &file_configcrawl_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLastUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLastUsedRequest) ProtoMessage() {}

func (x *UpdateLastUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLastUsedRequest.ProtoReflect.Descriptor instead.
func (*UpdateLastUsedRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{9}
}

func (x *UpdateLastUsedRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLastUsedRequest) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

type UpdateLastUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLastUsedResponse) Reset() {
	*x = UpdateLastUsedResponse{}
	mi := &file_configcrawl_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLastUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLastUsedResponse) ProtoMessage() {}

func (x *UpdateLastUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLastUsedResponse.ProtoReflect.Descriptor instead.
func (*UpdateLastUsedResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateLastUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateLastUsedResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteCrawlConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     string                 `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCrawlConfigRequest) Reset() {
	*x = DeleteCrawlConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCrawlConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCrawlConfigRequest) ProtoMessage() {}

func (x *DeleteCrawlConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCrawlConfigRequest.ProtoReflect.Descriptor instead.
func (*DeleteCrawlConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteCrawlConfigRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteCrawlConfigRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

type DeleteCrawlConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCrawlConfigResponse) Reset() {
	*x = DeleteCrawlConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCrawlConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCrawlConfigResponse) ProtoMessage() {}

func (x *DeleteCrawlConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCrawlConfigResponse.ProtoReflect.Descriptor instead.
func (*DeleteCrawlConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteCrawlConfigResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteCrawlConfigResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Request/Response messages for Queries
type GetAllCrawlConfigsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OrderBy       string                 `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCrawlConfigsRequest) Reset() {
	*x = GetAllCrawlConfigsRequest{}
	mi := &file_configcrawl_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCrawlConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCrawlConfigsRequest) ProtoMessage() {}

func (x *GetAllCrawlConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCrawlConfigsRequest.ProtoReflect.Descriptor instead.
func (*GetAllCrawlConfigsRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{13}
}

func (x *GetAllCrawlConfigsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllCrawlConfigsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllCrawlConfigsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type GetAllCrawlConfigsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalPages    int32                  `protobuf:"varint,5,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllCrawlConfigsResponse) Reset() {
	*x = GetAllCrawlConfigsResponse{}
	mi := &file_configcrawl_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllCrawlConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllCrawlConfigsResponse) ProtoMessage() {}

func (x *GetAllCrawlConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllCrawlConfigsResponse.ProtoReflect.Descriptor instead.
func (*GetAllCrawlConfigsResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{14}
}

func (x *GetAllCrawlConfigsResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

func (x *GetAllCrawlConfigsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAllCrawlConfigsResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllCrawlConfigsResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllCrawlConfigsResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

type GetCrawlConfigByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigByIdRequest) Reset() {
	*x = GetCrawlConfigByIdRequest{}
	mi := &file_configcrawl_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigByIdRequest) ProtoMessage() {}

func (x *GetCrawlConfigByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigByIdRequest.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigByIdRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{15}
}

func (x *GetCrawlConfigByIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetCrawlConfigByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        *CrawlConfig           `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigByIdResponse) Reset() {
	*x = GetCrawlConfigByIdResponse{}
	mi := &file_configcrawl_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigByIdResponse) ProtoMessage() {}

func (x *GetCrawlConfigByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigByIdResponse.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigByIdResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{16}
}

func (x *GetCrawlConfigByIdResponse) GetConfig() *CrawlConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetActiveCrawlConfigsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsRequest) Reset() {
	*x = GetActiveCrawlConfigsRequest{}
	mi := &file_configcrawl_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsRequest) ProtoMessage() {}

func (x *GetActiveCrawlConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsRequest.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{17}
}

type GetActiveCrawlConfigsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsResponse) Reset() {
	*x = GetActiveCrawlConfigsResponse{}
	mi := &file_configcrawl_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsResponse) ProtoMessage() {}

func (x *GetActiveCrawlConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsResponse.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{18}
}

func (x *GetActiveCrawlConfigsResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetCrawlConfigsByMarketIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigsByMarketIdRequest) Reset() {
	*x = GetCrawlConfigsByMarketIdRequest{}
	mi := &file_configcrawl_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigsByMarketIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigsByMarketIdRequest) ProtoMessage() {}

func (x *GetCrawlConfigsByMarketIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigsByMarketIdRequest.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigsByMarketIdRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{19}
}

func (x *GetCrawlConfigsByMarketIdRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

type GetCrawlConfigsByMarketIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigsByMarketIdResponse) Reset() {
	*x = GetCrawlConfigsByMarketIdResponse{}
	mi := &file_configcrawl_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigsByMarketIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigsByMarketIdResponse) ProtoMessage() {}

func (x *GetCrawlConfigsByMarketIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigsByMarketIdResponse.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigsByMarketIdResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{20}
}

func (x *GetCrawlConfigsByMarketIdResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetCrawlConfigsByTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TypeConfig    string                 `protobuf:"bytes,1,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigsByTypeRequest) Reset() {
	*x = GetCrawlConfigsByTypeRequest{}
	mi := &file_configcrawl_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigsByTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigsByTypeRequest) ProtoMessage() {}

func (x *GetCrawlConfigsByTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigsByTypeRequest.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigsByTypeRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{21}
}

func (x *GetCrawlConfigsByTypeRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

type GetCrawlConfigsByTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCrawlConfigsByTypeResponse) Reset() {
	*x = GetCrawlConfigsByTypeResponse{}
	mi := &file_configcrawl_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCrawlConfigsByTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCrawlConfigsByTypeResponse) ProtoMessage() {}

func (x *GetCrawlConfigsByTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCrawlConfigsByTypeResponse.ProtoReflect.Descriptor instead.
func (*GetCrawlConfigsByTypeResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{22}
}

func (x *GetCrawlConfigsByTypeResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetActiveCrawlConfigsByMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByMarketRequest) Reset() {
	*x = GetActiveCrawlConfigsByMarketRequest{}
	mi := &file_configcrawl_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByMarketRequest) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByMarketRequest.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByMarketRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{23}
}

func (x *GetActiveCrawlConfigsByMarketRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

type GetActiveCrawlConfigsByMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByMarketResponse) Reset() {
	*x = GetActiveCrawlConfigsByMarketResponse{}
	mi := &file_configcrawl_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByMarketResponse) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByMarketResponse.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByMarketResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{24}
}

func (x *GetActiveCrawlConfigsByMarketResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetActiveCrawlConfigsByTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TypeConfig    string                 `protobuf:"bytes,1,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByTypeRequest) Reset() {
	*x = GetActiveCrawlConfigsByTypeRequest{}
	mi := &file_configcrawl_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByTypeRequest) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByTypeRequest.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByTypeRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{25}
}

func (x *GetActiveCrawlConfigsByTypeRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

type GetActiveCrawlConfigsByTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByTypeResponse) Reset() {
	*x = GetActiveCrawlConfigsByTypeResponse{}
	mi := &file_configcrawl_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByTypeResponse) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByTypeResponse.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByTypeResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{26}
}

func (x *GetActiveCrawlConfigsByTypeResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetActiveCrawlConfigsByMarketAndTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	TypeConfig    string                 `protobuf:"bytes,2,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByMarketAndTypeRequest) Reset() {
	*x = GetActiveCrawlConfigsByMarketAndTypeRequest{}
	mi := &file_configcrawl_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByMarketAndTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByMarketAndTypeRequest) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByMarketAndTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByMarketAndTypeRequest.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByMarketAndTypeRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{27}
}

func (x *GetActiveCrawlConfigsByMarketAndTypeRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *GetActiveCrawlConfigsByMarketAndTypeRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

type GetActiveCrawlConfigsByMarketAndTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetActiveCrawlConfigsByMarketAndTypeResponse) Reset() {
	*x = GetActiveCrawlConfigsByMarketAndTypeResponse{}
	mi := &file_configcrawl_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetActiveCrawlConfigsByMarketAndTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetActiveCrawlConfigsByMarketAndTypeResponse) ProtoMessage() {}

func (x *GetActiveCrawlConfigsByMarketAndTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetActiveCrawlConfigsByMarketAndTypeResponse.ProtoReflect.Descriptor instead.
func (*GetActiveCrawlConfigsByMarketAndTypeResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{28}
}

func (x *GetActiveCrawlConfigsByMarketAndTypeResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type FilterCrawlConfigsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      *int32                 `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3,oneof" json:"market_id,omitempty"`
	TypeConfig    *string                `protobuf:"bytes,2,opt,name=type_config,json=typeConfig,proto3,oneof" json:"type_config,omitempty"`
	IsActive      *bool                  `protobuf:"varint,3,opt,name=is_active,json=isActive,proto3,oneof" json:"is_active,omitempty"`
	RequireAuth   *bool                  `protobuf:"varint,4,opt,name=require_auth,json=requireAuth,proto3,oneof" json:"require_auth,omitempty"`
	CreatedFrom   *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_from,json=createdFrom,proto3,oneof" json:"created_from,omitempty"`
	CreatedTo     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_to,json=createdTo,proto3,oneof" json:"created_to,omitempty"`
	OrderBy       string                 `protobuf:"bytes,7,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCrawlConfigsRequest) Reset() {
	*x = FilterCrawlConfigsRequest{}
	mi := &file_configcrawl_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCrawlConfigsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCrawlConfigsRequest) ProtoMessage() {}

func (x *FilterCrawlConfigsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCrawlConfigsRequest.ProtoReflect.Descriptor instead.
func (*FilterCrawlConfigsRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{29}
}

func (x *FilterCrawlConfigsRequest) GetMarketId() int32 {
	if x != nil && x.MarketId != nil {
		return *x.MarketId
	}
	return 0
}

func (x *FilterCrawlConfigsRequest) GetTypeConfig() string {
	if x != nil && x.TypeConfig != nil {
		return *x.TypeConfig
	}
	return ""
}

func (x *FilterCrawlConfigsRequest) GetIsActive() bool {
	if x != nil && x.IsActive != nil {
		return *x.IsActive
	}
	return false
}

func (x *FilterCrawlConfigsRequest) GetRequireAuth() bool {
	if x != nil && x.RequireAuth != nil {
		return *x.RequireAuth
	}
	return false
}

func (x *FilterCrawlConfigsRequest) GetCreatedFrom() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedFrom
	}
	return nil
}

func (x *FilterCrawlConfigsRequest) GetCreatedTo() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedTo
	}
	return nil
}

func (x *FilterCrawlConfigsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type FilterCrawlConfigsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Configs       []*CrawlConfig         `protobuf:"bytes,1,rep,name=configs,proto3" json:"configs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FilterCrawlConfigsResponse) Reset() {
	*x = FilterCrawlConfigsResponse{}
	mi := &file_configcrawl_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterCrawlConfigsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterCrawlConfigsResponse) ProtoMessage() {}

func (x *FilterCrawlConfigsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterCrawlConfigsResponse.ProtoReflect.Descriptor instead.
func (*FilterCrawlConfigsResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{30}
}

func (x *FilterCrawlConfigsResponse) GetConfigs() []*CrawlConfig {
	if x != nil {
		return x.Configs
	}
	return nil
}

type GetLeastUsedActiveConfigRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigRequest) Reset() {
	*x = GetLeastUsedActiveConfigRequest{}
	mi := &file_configcrawl_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigRequest) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigRequest.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{31}
}

type GetLeastUsedActiveConfigResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        *CrawlConfig           `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigResponse) Reset() {
	*x = GetLeastUsedActiveConfigResponse{}
	mi := &file_configcrawl_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigResponse) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigResponse.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{32}
}

func (x *GetLeastUsedActiveConfigResponse) GetConfig() *CrawlConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetLeastUsedActiveConfigByMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigByMarketRequest) Reset() {
	*x = GetLeastUsedActiveConfigByMarketRequest{}
	mi := &file_configcrawl_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigByMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigByMarketRequest) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigByMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigByMarketRequest.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigByMarketRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{33}
}

func (x *GetLeastUsedActiveConfigByMarketRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

type GetLeastUsedActiveConfigByMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        *CrawlConfig           `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigByMarketResponse) Reset() {
	*x = GetLeastUsedActiveConfigByMarketResponse{}
	mi := &file_configcrawl_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigByMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigByMarketResponse) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigByMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigByMarketResponse.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigByMarketResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{34}
}

func (x *GetLeastUsedActiveConfigByMarketResponse) GetConfig() *CrawlConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

type GetLeastUsedActiveConfigByTypeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TypeConfig    string                 `protobuf:"bytes,1,opt,name=type_config,json=typeConfig,proto3" json:"type_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigByTypeRequest) Reset() {
	*x = GetLeastUsedActiveConfigByTypeRequest{}
	mi := &file_configcrawl_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigByTypeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigByTypeRequest) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigByTypeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigByTypeRequest.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigByTypeRequest) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{35}
}

func (x *GetLeastUsedActiveConfigByTypeRequest) GetTypeConfig() string {
	if x != nil {
		return x.TypeConfig
	}
	return ""
}

type GetLeastUsedActiveConfigByTypeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Config        *CrawlConfig           `protobuf:"bytes,1,opt,name=config,proto3" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetLeastUsedActiveConfigByTypeResponse) Reset() {
	*x = GetLeastUsedActiveConfigByTypeResponse{}
	mi := &file_configcrawl_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetLeastUsedActiveConfigByTypeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetLeastUsedActiveConfigByTypeResponse) ProtoMessage() {}

func (x *GetLeastUsedActiveConfigByTypeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_configcrawl_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetLeastUsedActiveConfigByTypeResponse.ProtoReflect.Descriptor instead.
func (*GetLeastUsedActiveConfigByTypeResponse) Descriptor() ([]byte, []int) {
	return file_configcrawl_proto_rawDescGZIP(), []int{36}
}

func (x *GetLeastUsedActiveConfigByTypeResponse) GetConfig() *CrawlConfig {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_configcrawl_proto protoreflect.FileDescriptor

const file_configcrawl_proto_rawDesc = "" +
	"\n" +
	"\x11configcrawl.proto\x12\x0econfigcrawl.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe5\x05\n" +
	"\vCrawlConfig\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\x05R\bmarketId\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1f\n" +
	"\vtype_config\x18\x04 \x01(\tR\n" +
	"typeConfig\x12.\n" +
	"\x13requests_per_minute\x18\x05 \x01(\x05R\x11requestsPerMinute\x12!\n" +
	"\frequire_auth\x18\x06 \x01(\bR\vrequireAuth\x12&\n" +
	"\x0fmax_number_auth\x18\a \x01(\x05R\rmaxNumberAuth\x12(\n" +
	"\x10max_number_proxy\x18\b \x01(\x05R\x0emaxNumberProxy\x129\n" +
	"\x19per_request_delay_seconds\x18\t \x01(\x05R\x16perRequestDelaySeconds\x12'\n" +
	"\x0ftimeout_seconds\x18\n" +
	" \x01(\x05R\x0etimeoutSeconds\x12\x1f\n" +
	"\vmax_retries\x18\v \x01(\x05R\n" +
	"maxRetries\x12.\n" +
	"\x13retry_delay_seconds\x18\f \x01(\x05R\x11retryDelaySeconds\x12%\n" +
	"\x0emax_concurrent\x18\r \x01(\x05R\rmaxConcurrent\x12\x1b\n" +
	"\tis_active\x18\x0e \x01(\bR\bisActive\x12A\n" +
	"\flast_used_at\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampH\x00R\n" +
	"lastUsedAt\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAtB\x0f\n" +
	"\r_last_used_at\"\x9a\x04\n" +
	"\x18CreateCrawlConfigRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12\x1f\n" +
	"\vtype_config\x18\x03 \x01(\tR\n" +
	"typeConfig\x12.\n" +
	"\x13requests_per_minute\x18\x04 \x01(\x05R\x11requestsPerMinute\x12!\n" +
	"\frequire_auth\x18\x05 \x01(\bR\vrequireAuth\x12&\n" +
	"\x0fmax_number_auth\x18\x06 \x01(\x05R\rmaxNumberAuth\x12(\n" +
	"\x10max_number_proxy\x18\a \x01(\x05R\x0emaxNumberProxy\x129\n" +
	"\x19per_request_delay_seconds\x18\b \x01(\x05R\x16perRequestDelaySeconds\x12'\n" +
	"\x0ftimeout_seconds\x18\t \x01(\x05R\x0etimeoutSeconds\x12\x1f\n" +
	"\vmax_retries\x18\n" +
	" \x01(\x05R\n" +
	"maxRetries\x12.\n" +
	"\x13retry_delay_seconds\x18\v \x01(\x05R\x11retryDelaySeconds\x12%\n" +
	"\x0emax_concurrent\x18\f \x01(\x05R\rmaxConcurrent\x12\x1d\n" +
	"\n" +
	"created_by\x18\r \x01(\tR\tcreatedBy\"\x7f\n" +
	"\x19CreateCrawlConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12 \n" +
	"\tconfig_id\x18\x03 \x01(\x05H\x00R\bconfigId\x88\x01\x01B\f\n" +
	"\n" +
	"_config_id\"\xc7\x04\n" +
	"\x18UpdateCrawlConfigRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\x05R\bmarketId\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1f\n" +
	"\vtype_config\x18\x04 \x01(\tR\n" +
	"typeConfig\x12.\n" +
	"\x13requests_per_minute\x18\x05 \x01(\x05R\x11requestsPerMinute\x12!\n" +
	"\frequire_auth\x18\x06 \x01(\bR\vrequireAuth\x12&\n" +
	"\x0fmax_number_auth\x18\a \x01(\x05R\rmaxNumberAuth\x12(\n" +
	"\x10max_number_proxy\x18\b \x01(\x05R\x0emaxNumberProxy\x129\n" +
	"\x19per_request_delay_seconds\x18\t \x01(\x05R\x16perRequestDelaySeconds\x12'\n" +
	"\x0ftimeout_seconds\x18\n" +
	" \x01(\x05R\x0etimeoutSeconds\x12\x1f\n" +
	"\vmax_retries\x18\v \x01(\x05R\n" +
	"maxRetries\x12.\n" +
	"\x13retry_delay_seconds\x18\f \x01(\x05R\x11retryDelaySeconds\x12%\n" +
	"\x0emax_concurrent\x18\r \x01(\x05R\rmaxConcurrent\x12\x1b\n" +
	"\tis_active\x18\x0e \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x0f \x01(\tR\tupdatedBy\"O\n" +
	"\x19UpdateCrawlConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"O\n" +
	"\x1aActivateCrawlConfigRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\factivated_by\x18\x02 \x01(\tR\vactivatedBy\"Q\n" +
	"\x1bActivateCrawlConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"U\n" +
	"\x1cDeactivateCrawlConfigRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12%\n" +
	"\x0edeactivated_by\x18\x02 \x01(\tR\rdeactivatedBy\"S\n" +
	"\x1dDeactivateCrawlConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"@\n" +
	"\x15UpdateLastUsedRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x17\n" +
	"\aused_by\x18\x02 \x01(\tR\x06usedBy\"L\n" +
	"\x16UpdateLastUsedResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"I\n" +
	"\x18DeleteCrawlConfigRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\tR\tdeletedBy\"O\n" +
	"\x19DeleteCrawlConfigResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"g\n" +
	"\x19GetAllCrawlConfigsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x19\n" +
	"\border_by\x18\x03 \x01(\tR\aorderBy\"\xbb\x01\n" +
	"\x1aGetAllCrawlConfigsResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_pages\x18\x05 \x01(\x05R\n" +
	"totalPages\"+\n" +
	"\x19GetCrawlConfigByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"Q\n" +
	"\x1aGetCrawlConfigByIdResponse\x123\n" +
	"\x06config\x18\x01 \x01(\v2\x1b.configcrawl.v1.CrawlConfigR\x06config\"\x1e\n" +
	"\x1cGetActiveCrawlConfigsRequest\"V\n" +
	"\x1dGetActiveCrawlConfigsResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"?\n" +
	" GetCrawlConfigsByMarketIdRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\"Z\n" +
	"!GetCrawlConfigsByMarketIdResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"?\n" +
	"\x1cGetCrawlConfigsByTypeRequest\x12\x1f\n" +
	"\vtype_config\x18\x01 \x01(\tR\n" +
	"typeConfig\"V\n" +
	"\x1dGetCrawlConfigsByTypeResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"C\n" +
	"$GetActiveCrawlConfigsByMarketRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\"^\n" +
	"%GetActiveCrawlConfigsByMarketResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"E\n" +
	"\"GetActiveCrawlConfigsByTypeRequest\x12\x1f\n" +
	"\vtype_config\x18\x01 \x01(\tR\n" +
	"typeConfig\"\\\n" +
	"#GetActiveCrawlConfigsByTypeResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"k\n" +
	"+GetActiveCrawlConfigsByMarketAndTypeRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12\x1f\n" +
	"\vtype_config\x18\x02 \x01(\tR\n" +
	"typeConfig\"e\n" +
	",GetActiveCrawlConfigsByMarketAndTypeResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"\xa9\x03\n" +
	"\x19FilterCrawlConfigsRequest\x12 \n" +
	"\tmarket_id\x18\x01 \x01(\x05H\x00R\bmarketId\x88\x01\x01\x12$\n" +
	"\vtype_config\x18\x02 \x01(\tH\x01R\n" +
	"typeConfig\x88\x01\x01\x12 \n" +
	"\tis_active\x18\x03 \x01(\bH\x02R\bisActive\x88\x01\x01\x12&\n" +
	"\frequire_auth\x18\x04 \x01(\bH\x03R\vrequireAuth\x88\x01\x01\x12B\n" +
	"\fcreated_from\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampH\x04R\vcreatedFrom\x88\x01\x01\x12>\n" +
	"\n" +
	"created_to\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampH\x05R\tcreatedTo\x88\x01\x01\x12\x19\n" +
	"\border_by\x18\a \x01(\tR\aorderByB\f\n" +
	"\n" +
	"_market_idB\x0e\n" +
	"\f_type_configB\f\n" +
	"\n" +
	"_is_activeB\x0f\n" +
	"\r_require_authB\x0f\n" +
	"\r_created_fromB\r\n" +
	"\v_created_to\"S\n" +
	"\x1aFilterCrawlConfigsResponse\x125\n" +
	"\aconfigs\x18\x01 \x03(\v2\x1b.configcrawl.v1.CrawlConfigR\aconfigs\"!\n" +
	"\x1fGetLeastUsedActiveConfigRequest\"W\n" +
	" GetLeastUsedActiveConfigResponse\x123\n" +
	"\x06config\x18\x01 \x01(\v2\x1b.configcrawl.v1.CrawlConfigR\x06config\"F\n" +
	"'GetLeastUsedActiveConfigByMarketRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\"_\n" +
	"(GetLeastUsedActiveConfigByMarketResponse\x123\n" +
	"\x06config\x18\x01 \x01(\v2\x1b.configcrawl.v1.CrawlConfigR\x06config\"H\n" +
	"%GetLeastUsedActiveConfigByTypeRequest\x12\x1f\n" +
	"\vtype_config\x18\x01 \x01(\tR\n" +
	"typeConfig\"]\n" +
	"&GetLeastUsedActiveConfigByTypeResponse\x123\n" +
	"\x06config\x18\x01 \x01(\v2\x1b.configcrawl.v1.CrawlConfigR\x06config2\xb4\x11\n" +
	"\x12CrawlConfigService\x12h\n" +
	"\x11CreateCrawlConfig\x12(.configcrawl.v1.CreateCrawlConfigRequest\x1a).configcrawl.v1.CreateCrawlConfigResponse\x12h\n" +
	"\x11UpdateCrawlConfig\x12(.configcrawl.v1.UpdateCrawlConfigRequest\x1a).configcrawl.v1.UpdateCrawlConfigResponse\x12n\n" +
	"\x13ActivateCrawlConfig\x12*.configcrawl.v1.ActivateCrawlConfigRequest\x1a+.configcrawl.v1.ActivateCrawlConfigResponse\x12t\n" +
	"\x15DeactivateCrawlConfig\x12,.configcrawl.v1.DeactivateCrawlConfigRequest\x1a-.configcrawl.v1.DeactivateCrawlConfigResponse\x12_\n" +
	"\x0eUpdateLastUsed\x12%.configcrawl.v1.UpdateLastUsedRequest\x1a&.configcrawl.v1.UpdateLastUsedResponse\x12h\n" +
	"\x11DeleteCrawlConfig\x12(.configcrawl.v1.DeleteCrawlConfigRequest\x1a).configcrawl.v1.DeleteCrawlConfigResponse\x12k\n" +
	"\x12GetAllCrawlConfigs\x12).configcrawl.v1.GetAllCrawlConfigsRequest\x1a*.configcrawl.v1.GetAllCrawlConfigsResponse\x12k\n" +
	"\x12GetCrawlConfigById\x12).configcrawl.v1.GetCrawlConfigByIdRequest\x1a*.configcrawl.v1.GetCrawlConfigByIdResponse\x12t\n" +
	"\x15GetActiveCrawlConfigs\x12,.configcrawl.v1.GetActiveCrawlConfigsRequest\x1a-.configcrawl.v1.GetActiveCrawlConfigsResponse\x12\x80\x01\n" +
	"\x19GetCrawlConfigsByMarketId\x120.configcrawl.v1.GetCrawlConfigsByMarketIdRequest\x1a1.configcrawl.v1.GetCrawlConfigsByMarketIdResponse\x12t\n" +
	"\x15GetCrawlConfigsByType\x12,.configcrawl.v1.GetCrawlConfigsByTypeRequest\x1a-.configcrawl.v1.GetCrawlConfigsByTypeResponse\x12\x8c\x01\n" +
	"\x1dGetActiveCrawlConfigsByMarket\x124.configcrawl.v1.GetActiveCrawlConfigsByMarketRequest\x1a5.configcrawl.v1.GetActiveCrawlConfigsByMarketResponse\x12\x86\x01\n" +
	"\x1bGetActiveCrawlConfigsByType\x122.configcrawl.v1.GetActiveCrawlConfigsByTypeRequest\x1a3.configcrawl.v1.GetActiveCrawlConfigsByTypeResponse\x12\xa1\x01\n" +
	"$GetActiveCrawlConfigsByMarketAndType\x12;.configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeRequest\x1a<.configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeResponse\x12k\n" +
	"\x12FilterCrawlConfigs\x12).configcrawl.v1.FilterCrawlConfigsRequest\x1a*.configcrawl.v1.FilterCrawlConfigsResponse\x12}\n" +
	"\x18GetLeastUsedActiveConfig\x12/.configcrawl.v1.GetLeastUsedActiveConfigRequest\x1a0.configcrawl.v1.GetLeastUsedActiveConfigResponse\x12\x95\x01\n" +
	" GetLeastUsedActiveConfigByMarket\x127.configcrawl.v1.GetLeastUsedActiveConfigByMarketRequest\x1a8.configcrawl.v1.GetLeastUsedActiveConfigByMarketResponse\x12\x8f\x01\n" +
	"\x1eGetLeastUsedActiveConfigByType\x125.configcrawl.v1.GetLeastUsedActiveConfigByTypeRequest\x1a6.configcrawl.v1.GetLeastUsedActiveConfigByTypeResponseB#Z!go_core_market/pkg/pb/configcrawlb\x06proto3"

var (
	file_configcrawl_proto_rawDescOnce sync.Once
	file_configcrawl_proto_rawDescData []byte
)

func file_configcrawl_proto_rawDescGZIP() []byte {
	file_configcrawl_proto_rawDescOnce.Do(func() {
		file_configcrawl_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_configcrawl_proto_rawDesc), len(file_configcrawl_proto_rawDesc)))
	})
	return file_configcrawl_proto_rawDescData
}

var file_configcrawl_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_configcrawl_proto_goTypes = []any{
	(*CrawlConfig)(nil),                                  // 0: configcrawl.v1.CrawlConfig
	(*CreateCrawlConfigRequest)(nil),                     // 1: configcrawl.v1.CreateCrawlConfigRequest
	(*CreateCrawlConfigResponse)(nil),                    // 2: configcrawl.v1.CreateCrawlConfigResponse
	(*UpdateCrawlConfigRequest)(nil),                     // 3: configcrawl.v1.UpdateCrawlConfigRequest
	(*UpdateCrawlConfigResponse)(nil),                    // 4: configcrawl.v1.UpdateCrawlConfigResponse
	(*ActivateCrawlConfigRequest)(nil),                   // 5: configcrawl.v1.ActivateCrawlConfigRequest
	(*ActivateCrawlConfigResponse)(nil),                  // 6: configcrawl.v1.ActivateCrawlConfigResponse
	(*DeactivateCrawlConfigRequest)(nil),                 // 7: configcrawl.v1.DeactivateCrawlConfigRequest
	(*DeactivateCrawlConfigResponse)(nil),                // 8: configcrawl.v1.DeactivateCrawlConfigResponse
	(*UpdateLastUsedRequest)(nil),                        // 9: configcrawl.v1.UpdateLastUsedRequest
	(*UpdateLastUsedResponse)(nil),                       // 10: configcrawl.v1.UpdateLastUsedResponse
	(*DeleteCrawlConfigRequest)(nil),                     // 11: configcrawl.v1.DeleteCrawlConfigRequest
	(*DeleteCrawlConfigResponse)(nil),                    // 12: configcrawl.v1.DeleteCrawlConfigResponse
	(*GetAllCrawlConfigsRequest)(nil),                    // 13: configcrawl.v1.GetAllCrawlConfigsRequest
	(*GetAllCrawlConfigsResponse)(nil),                   // 14: configcrawl.v1.GetAllCrawlConfigsResponse
	(*GetCrawlConfigByIdRequest)(nil),                    // 15: configcrawl.v1.GetCrawlConfigByIdRequest
	(*GetCrawlConfigByIdResponse)(nil),                   // 16: configcrawl.v1.GetCrawlConfigByIdResponse
	(*GetActiveCrawlConfigsRequest)(nil),                 // 17: configcrawl.v1.GetActiveCrawlConfigsRequest
	(*GetActiveCrawlConfigsResponse)(nil),                // 18: configcrawl.v1.GetActiveCrawlConfigsResponse
	(*GetCrawlConfigsByMarketIdRequest)(nil),             // 19: configcrawl.v1.GetCrawlConfigsByMarketIdRequest
	(*GetCrawlConfigsByMarketIdResponse)(nil),            // 20: configcrawl.v1.GetCrawlConfigsByMarketIdResponse
	(*GetCrawlConfigsByTypeRequest)(nil),                 // 21: configcrawl.v1.GetCrawlConfigsByTypeRequest
	(*GetCrawlConfigsByTypeResponse)(nil),                // 22: configcrawl.v1.GetCrawlConfigsByTypeResponse
	(*GetActiveCrawlConfigsByMarketRequest)(nil),         // 23: configcrawl.v1.GetActiveCrawlConfigsByMarketRequest
	(*GetActiveCrawlConfigsByMarketResponse)(nil),        // 24: configcrawl.v1.GetActiveCrawlConfigsByMarketResponse
	(*GetActiveCrawlConfigsByTypeRequest)(nil),           // 25: configcrawl.v1.GetActiveCrawlConfigsByTypeRequest
	(*GetActiveCrawlConfigsByTypeResponse)(nil),          // 26: configcrawl.v1.GetActiveCrawlConfigsByTypeResponse
	(*GetActiveCrawlConfigsByMarketAndTypeRequest)(nil),  // 27: configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeRequest
	(*GetActiveCrawlConfigsByMarketAndTypeResponse)(nil), // 28: configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeResponse
	(*FilterCrawlConfigsRequest)(nil),                    // 29: configcrawl.v1.FilterCrawlConfigsRequest
	(*FilterCrawlConfigsResponse)(nil),                   // 30: configcrawl.v1.FilterCrawlConfigsResponse
	(*GetLeastUsedActiveConfigRequest)(nil),              // 31: configcrawl.v1.GetLeastUsedActiveConfigRequest
	(*GetLeastUsedActiveConfigResponse)(nil),             // 32: configcrawl.v1.GetLeastUsedActiveConfigResponse
	(*GetLeastUsedActiveConfigByMarketRequest)(nil),      // 33: configcrawl.v1.GetLeastUsedActiveConfigByMarketRequest
	(*GetLeastUsedActiveConfigByMarketResponse)(nil),     // 34: configcrawl.v1.GetLeastUsedActiveConfigByMarketResponse
	(*GetLeastUsedActiveConfigByTypeRequest)(nil),        // 35: configcrawl.v1.GetLeastUsedActiveConfigByTypeRequest
	(*GetLeastUsedActiveConfigByTypeResponse)(nil),       // 36: configcrawl.v1.GetLeastUsedActiveConfigByTypeResponse
	(*timestamppb.Timestamp)(nil),                        // 37: google.protobuf.Timestamp
}
var file_configcrawl_proto_depIdxs = []int32{
	37, // 0: configcrawl.v1.CrawlConfig.last_used_at:type_name -> google.protobuf.Timestamp
	37, // 1: configcrawl.v1.CrawlConfig.created_at:type_name -> google.protobuf.Timestamp
	37, // 2: configcrawl.v1.CrawlConfig.updated_at:type_name -> google.protobuf.Timestamp
	0,  // 3: configcrawl.v1.GetAllCrawlConfigsResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 4: configcrawl.v1.GetCrawlConfigByIdResponse.config:type_name -> configcrawl.v1.CrawlConfig
	0,  // 5: configcrawl.v1.GetActiveCrawlConfigsResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 6: configcrawl.v1.GetCrawlConfigsByMarketIdResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 7: configcrawl.v1.GetCrawlConfigsByTypeResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 8: configcrawl.v1.GetActiveCrawlConfigsByMarketResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 9: configcrawl.v1.GetActiveCrawlConfigsByTypeResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 10: configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	37, // 11: configcrawl.v1.FilterCrawlConfigsRequest.created_from:type_name -> google.protobuf.Timestamp
	37, // 12: configcrawl.v1.FilterCrawlConfigsRequest.created_to:type_name -> google.protobuf.Timestamp
	0,  // 13: configcrawl.v1.FilterCrawlConfigsResponse.configs:type_name -> configcrawl.v1.CrawlConfig
	0,  // 14: configcrawl.v1.GetLeastUsedActiveConfigResponse.config:type_name -> configcrawl.v1.CrawlConfig
	0,  // 15: configcrawl.v1.GetLeastUsedActiveConfigByMarketResponse.config:type_name -> configcrawl.v1.CrawlConfig
	0,  // 16: configcrawl.v1.GetLeastUsedActiveConfigByTypeResponse.config:type_name -> configcrawl.v1.CrawlConfig
	1,  // 17: configcrawl.v1.CrawlConfigService.CreateCrawlConfig:input_type -> configcrawl.v1.CreateCrawlConfigRequest
	3,  // 18: configcrawl.v1.CrawlConfigService.UpdateCrawlConfig:input_type -> configcrawl.v1.UpdateCrawlConfigRequest
	5,  // 19: configcrawl.v1.CrawlConfigService.ActivateCrawlConfig:input_type -> configcrawl.v1.ActivateCrawlConfigRequest
	7,  // 20: configcrawl.v1.CrawlConfigService.DeactivateCrawlConfig:input_type -> configcrawl.v1.DeactivateCrawlConfigRequest
	9,  // 21: configcrawl.v1.CrawlConfigService.UpdateLastUsed:input_type -> configcrawl.v1.UpdateLastUsedRequest
	11, // 22: configcrawl.v1.CrawlConfigService.DeleteCrawlConfig:input_type -> configcrawl.v1.DeleteCrawlConfigRequest
	13, // 23: configcrawl.v1.CrawlConfigService.GetAllCrawlConfigs:input_type -> configcrawl.v1.GetAllCrawlConfigsRequest
	15, // 24: configcrawl.v1.CrawlConfigService.GetCrawlConfigById:input_type -> configcrawl.v1.GetCrawlConfigByIdRequest
	17, // 25: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigs:input_type -> configcrawl.v1.GetActiveCrawlConfigsRequest
	19, // 26: configcrawl.v1.CrawlConfigService.GetCrawlConfigsByMarketId:input_type -> configcrawl.v1.GetCrawlConfigsByMarketIdRequest
	21, // 27: configcrawl.v1.CrawlConfigService.GetCrawlConfigsByType:input_type -> configcrawl.v1.GetCrawlConfigsByTypeRequest
	23, // 28: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByMarket:input_type -> configcrawl.v1.GetActiveCrawlConfigsByMarketRequest
	25, // 29: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByType:input_type -> configcrawl.v1.GetActiveCrawlConfigsByTypeRequest
	27, // 30: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByMarketAndType:input_type -> configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeRequest
	29, // 31: configcrawl.v1.CrawlConfigService.FilterCrawlConfigs:input_type -> configcrawl.v1.FilterCrawlConfigsRequest
	31, // 32: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfig:input_type -> configcrawl.v1.GetLeastUsedActiveConfigRequest
	33, // 33: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfigByMarket:input_type -> configcrawl.v1.GetLeastUsedActiveConfigByMarketRequest
	35, // 34: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfigByType:input_type -> configcrawl.v1.GetLeastUsedActiveConfigByTypeRequest
	2,  // 35: configcrawl.v1.CrawlConfigService.CreateCrawlConfig:output_type -> configcrawl.v1.CreateCrawlConfigResponse
	4,  // 36: configcrawl.v1.CrawlConfigService.UpdateCrawlConfig:output_type -> configcrawl.v1.UpdateCrawlConfigResponse
	6,  // 37: configcrawl.v1.CrawlConfigService.ActivateCrawlConfig:output_type -> configcrawl.v1.ActivateCrawlConfigResponse
	8,  // 38: configcrawl.v1.CrawlConfigService.DeactivateCrawlConfig:output_type -> configcrawl.v1.DeactivateCrawlConfigResponse
	10, // 39: configcrawl.v1.CrawlConfigService.UpdateLastUsed:output_type -> configcrawl.v1.UpdateLastUsedResponse
	12, // 40: configcrawl.v1.CrawlConfigService.DeleteCrawlConfig:output_type -> configcrawl.v1.DeleteCrawlConfigResponse
	14, // 41: configcrawl.v1.CrawlConfigService.GetAllCrawlConfigs:output_type -> configcrawl.v1.GetAllCrawlConfigsResponse
	16, // 42: configcrawl.v1.CrawlConfigService.GetCrawlConfigById:output_type -> configcrawl.v1.GetCrawlConfigByIdResponse
	18, // 43: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigs:output_type -> configcrawl.v1.GetActiveCrawlConfigsResponse
	20, // 44: configcrawl.v1.CrawlConfigService.GetCrawlConfigsByMarketId:output_type -> configcrawl.v1.GetCrawlConfigsByMarketIdResponse
	22, // 45: configcrawl.v1.CrawlConfigService.GetCrawlConfigsByType:output_type -> configcrawl.v1.GetCrawlConfigsByTypeResponse
	24, // 46: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByMarket:output_type -> configcrawl.v1.GetActiveCrawlConfigsByMarketResponse
	26, // 47: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByType:output_type -> configcrawl.v1.GetActiveCrawlConfigsByTypeResponse
	28, // 48: configcrawl.v1.CrawlConfigService.GetActiveCrawlConfigsByMarketAndType:output_type -> configcrawl.v1.GetActiveCrawlConfigsByMarketAndTypeResponse
	30, // 49: configcrawl.v1.CrawlConfigService.FilterCrawlConfigs:output_type -> configcrawl.v1.FilterCrawlConfigsResponse
	32, // 50: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfig:output_type -> configcrawl.v1.GetLeastUsedActiveConfigResponse
	34, // 51: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfigByMarket:output_type -> configcrawl.v1.GetLeastUsedActiveConfigByMarketResponse
	36, // 52: configcrawl.v1.CrawlConfigService.GetLeastUsedActiveConfigByType:output_type -> configcrawl.v1.GetLeastUsedActiveConfigByTypeResponse
	35, // [35:53] is the sub-list for method output_type
	17, // [17:35] is the sub-list for method input_type
	17, // [17:17] is the sub-list for extension type_name
	17, // [17:17] is the sub-list for extension extendee
	0,  // [0:17] is the sub-list for field type_name
}

func init() { file_configcrawl_proto_init() }
func file_configcrawl_proto_init() {
	if File_configcrawl_proto != nil {
		return
	}
	file_configcrawl_proto_msgTypes[0].OneofWrappers = []any{}
	file_configcrawl_proto_msgTypes[2].OneofWrappers = []any{}
	file_configcrawl_proto_msgTypes[29].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_configcrawl_proto_rawDesc), len(file_configcrawl_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_configcrawl_proto_goTypes,
		DependencyIndexes: file_configcrawl_proto_depIdxs,
		MessageInfos:      file_configcrawl_proto_msgTypes,
	}.Build()
	File_configcrawl_proto = out.File
	file_configcrawl_proto_goTypes = nil
	file_configcrawl_proto_depIdxs = nil
}
