// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	CountAuthCrawl(ctx context.Context) (int64, error)
	CreateAuth(ctx context.Context, arg CreateAuthParams) (int, error)
	CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error)
	DeleteAuth(ctx context.Context, id int) error
	FindAuthByID(ctx context.Context, id int) (*AuthCrawl, error)
	FindAuthByIDs(ctx context.Context, ids []int32) ([]*AuthCrawl, error)
	FindByValue(ctx context.Context, value string) (*AuthCrawl, error)
	GetAllAuthCrawl(ctx context.Context, arg GetAllAuthCrawlParams) ([]*AuthCrawl, error)
	GetAuthCrawlByMarketId(ctx context.Context, marketID int) (*AuthCrawl, error)
	GetAvailableAuthCrawlByMarket(ctx context.Context, arg GetAvailableAuthCrawlByMarketParams) ([]*AuthCrawl, error)
	UpdateAuth(ctx context.Context, arg UpdateAuthParams) (*AuthCrawl, error)
}

var _ Querier = (*Queries)(nil)
