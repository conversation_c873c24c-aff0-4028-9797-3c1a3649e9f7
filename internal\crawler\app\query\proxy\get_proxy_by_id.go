package queryproxy

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetProxyByID struct {
	ID int
}

type GetProxyByIDHandler decorator.QueryHandler[GetProxyByID, *Proxy]

type getProxyByIDHandler struct {
	repo ProxyReadModel
}

func NewGetProxyByIDHandler(
	repo ProxyReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
) GetProxyByIDHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyQueryDecorators[GetProxyByID, *Proxy](
		getProxyByIDHandler{repo: repo},
		logger,
		metricsClient,
	)
}

func (h getProxyByIDHandler) Handle(ctx context.Context, query GetProxyByID) (*Proxy, error) {
	proxy, err := h.repo.GetProxyById(ctx, query.ID)
	if err != nil {
		return nil, err
	}
	return proxy, nil
}
