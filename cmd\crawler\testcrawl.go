package main

import (
	"context"
	"go_core_market/internal/crawler/adapter/clienthttp"
	"go_core_market/internal/crawler/adapter/crawler/buff163"
	"go_core_market/internal/crawler/domain/value_object"
	"log"
)

// ExampleUsage demonstrates how to use the Buff163Crawler
func main() {
	// 1. Create HTTP client
	client := clienthttp.NewRestyClient()
	client.SetBaseURL("https://buff.163.com")
	// 2. Create Buff163Crawler
	crawler := buff163.NewBuff163Crawler(client)
	cookielocal := "session=1-y3mry3cNbPCw1uWN6RZ0I4WAFUejRoFp55D9y5lMzbni2024444474; HttpOnly; Path=/;Locale-Supported=en"
	cookieez := "session=1-VkGKrcmtvfNmVXx754psRcGUer6FjBkULHhr_ue3FGjm2043981107; HttpOnly; Path=/;Locale-Supported=en"
	cookiepepe := "session=1-iNb7K4_GKCU9Wri_18PgoW-Hg5UWAJboyk5hj4Es4e2r2024775951; HttpOnly; Path=/;Locale-Supported=en"
	cookieggez := "session=1-SyGuEglTWcxJ6lEIG7PLKT4wEWFFVlwpHBdCevw1dCUC2037725724; HttpOnly; Path=/;Locale-Supported=en"
	cookiefv := "session=1-LllJSq-UxR_NgD4jmFn3g_hH0O9VG-1zv8_akRw6G5uI2040748222; HttpOnly; Path=/;Locale-Supported=en"
	cookiecz02 := "session=1-qvUvaHjfLN3P7z93-hVw1j5hbARdJmrjJ4L4TPvjweG72018465661; HttpOnly; Path=/;Locale-Supported=en"
	cookierx470 := "session=1-KxgAOhEGofEmX002pezDoXgDqQWCCX7EiFfEFAIfG5Dv2018465704; HttpOnly; Path=/;Locale-Supported=en"
	proxies := make([]string, 0, 1)
	cookies := make([]string, 0, 1)
	cookies = append(cookies, cookieez)
	cookies = append(cookies, cookiepepe)
	cookies = append(cookies, cookieggez)
	cookies = append(cookies, cookiefv)
	cookies = append(cookies, cookielocal)
	cookies = append(cookies, cookiecz02)
	cookies = append(cookies, cookierx470)
	//proxies = append(proxies, "************************************************")
	//proxies = append(proxies, "*********************************************")
	proxies = append(proxies, "************************************************")
	//proxies = append(proxies, "*************************************************")
	proxies = append(proxies, "**************************************************")
	proxies = append(proxies, "*********************************************")
	//proxies = append(proxies, "*********************************************")
	//proxies = append(proxies, "********************************************")
	proxies = append(proxies, "*************************************************")
	//proxies = append(proxies, "********************************************")
	proxies = append(proxies, "*******************************************")
	proxies = append(proxies, "http://i27g7pjz:ZnQpPYQqCL2w@************:3139")
	proxies = append(proxies, "**********************************************")
	//cookies = append(cookies, cookieStr)
	config := value_object.Config{
		Cookies:           cookies,
		APIKeys:           nil,
		Proxies:           proxies,
		MarketID:          1,
		RequestsPerMinute: 60,
		PerRequestDelay:   1,
		Timeout:           3,
		MaxRetries:        1,
		RetryDelay:        1,
	}
	// Set the config to crawler
	err := crawler.SetConfig(&config)
	if err != nil {
		log.Fatalf("Failed to set config: %v", err)
	}
	ctx := context.Background()
	conditions := make([]value_object.ConditionDoppler, 0)
	conditions = append(conditions, value_object.ConditionDoppler{
		Name: "Phase1",
	})
	conditions = append(conditions, value_object.ConditionDoppler{
		Name: "Phase2",
	})
	conditions = append(conditions, value_object.ConditionDoppler{
		Name: "Phase3",
	})
	conditions = append(conditions, value_object.ConditionDoppler{
		Name: "Phase4",
	})
	type items struct {
		Name string
		Id   int
	}
	itemdopplers := make([]items, 0)
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Gamma Doppler (Factory New)",
		Id:   43104,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   871723,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Factory New)",
		Id:   42998,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Butterfly Knife | Gamma Doppler (Factory New)",
		Id:   871747,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Skeleton Knife | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   1115642,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Doppler (Factory New)",
		Id:   42486,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Nomad Knife | Doppler (Factory New)",
		Id:   1115738,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Gamma Doppler (Factory New)",
		Id:   871861,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   872051,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Paracord Knife | Doppler (Factory New)",
		Id:   1115922,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Minimal Wear)",
		Id:   42999,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Gamma Doppler (Factory New)",
		Id:   43011,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Navaja Knife | Doppler (Factory New)",
		Id:   769477,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Survival Knife | Doppler (Minimal Wear)",
		Id:   1116163,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Doppler (Factory New)",
		Id:   43186,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Falchion Knife | Doppler (Factory New)",
		Id:   42636,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Flip Knife | Doppler (Factory New)",
		Id:   43528,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Gamma Doppler (Factory New)",
		Id:   871722,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Talon Knife | Doppler (Minimal Wear)",
		Id:   769501,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ M9 Bayonet | Doppler (Factory New)",
		Id:   43803,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Paracord Knife | Doppler (Factory New)",
		Id:   1116226,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Gamma Doppler (Factory New)",
		Id:   43104,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   871723,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Factory New)",
		Id:   42998,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Butterfly Knife | Gamma Doppler (Factory New)",
		Id:   871747,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Skeleton Knife | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   1115642,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Doppler (Factory New)",
		Id:   42486,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Nomad Knife | Doppler (Factory New)",
		Id:   1115738,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Gamma Doppler (Factory New)",
		Id:   871861,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   872051,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Paracord Knife | Doppler (Factory New)",
		Id:   1115922,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Minimal Wear)",
		Id:   42999,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Gamma Doppler (Factory New)",
		Id:   43011,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Navaja Knife | Doppler (Factory New)",
		Id:   769477,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Survival Knife | Doppler (Minimal Wear)",
		Id:   1116163,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Doppler (Factory New)",
		Id:   43186,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Falchion Knife | Doppler (Factory New)",
		Id:   42636,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Flip Knife | Doppler (Factory New)",
		Id:   43528,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Gamma Doppler (Factory New)",
		Id:   871722,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Talon Knife | Doppler (Minimal Wear)",
		Id:   769501,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ M9 Bayonet | Doppler (Factory New)",
		Id:   43803,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Paracord Knife | Doppler (Factory New)",
		Id:   1116226,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Gamma Doppler (Factory New)",
		Id:   43104,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   871723,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Factory New)",
		Id:   42998,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Butterfly Knife | Gamma Doppler (Factory New)",
		Id:   871747,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Skeleton Knife | Doppler (Factory New)",
		Id:   43091,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ M9 Bayonet | Doppler (Factory New)",
		Id:   1115642,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Doppler (Factory New)",
		Id:   42486,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Nomad Knife | Doppler (Factory New)",
		Id:   1115738,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Bowie Knife | Gamma Doppler (Factory New)",
		Id:   871861,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Huntsman Knife | Gamma Doppler (Factory New)",
		Id:   872051,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Paracord Knife | Doppler (Factory New)",
		Id:   1115922,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Doppler (Minimal Wear)",
		Id:   42999,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Karambit | Gamma Doppler (Factory New)",
		Id:   43011,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Navaja Knife | Doppler (Factory New)",
		Id:   769477,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Survival Knife | Doppler (Minimal Wear)",
		Id:   1116163,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Doppler (Factory New)",
		Id:   43186,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Falchion Knife | Doppler (Factory New)",
		Id:   42636,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Flip Knife | Doppler (Factory New)",
		Id:   43528,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Shadow Daggers | Gamma Doppler (Factory New)",
		Id:   871722,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ Talon Knife | Doppler (Minimal Wear)",
		Id:   769501,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ M9 Bayonet | Doppler (Factory New)",
		Id:   43803,
	})
	itemdopplers = append(itemdopplers, items{
		Name: "★ StatTrak™ Paracord Knife | Doppler (Factory New)",
		Id:   1116226,
	})
	for _, item := range itemdopplers {
		price, err := crawler.CrawlDoppler(ctx, item.Id, item.Name, conditions)
		if err != nil {
			log.Println("Failed to crawl: %v", err)
			continue
		}
		if len(price) > 0 {
			log.Println(price[0].ItemName, price[0].BuyPrice, price[0].SellPrice, price[0].Condition.GetType())
		} else {
			log.Printf("No items on page %d", item.Name)
		}
	}
	/*for i := 1; i < 200; i++ {
		price, _, err := crawler.CrawlNormal(ctx, i)
		if err != nil {
			log.Println("Failed to crawl: %v", err)
			continue
		}
		if len(price) > 0 {
			log.Println(price[0].ItemName)
		} else {
			log.Printf("No items on page %d", i)
		}
	}*/

	/*typeFloat := make([]value_object.ConditionFloat, 0)
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.15-0.18",
		FloatMin: 0.15,
		FloatMax: 0.18,
	})
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.18-0.22",
		FloatMin: 0.18,
		FloatMax: 0.22,
	})
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.22-0.25",
		FloatMin: 0.22,
		FloatMax: 0.25,
	})
	prices, err := crawler.CrawlFloat(ctx, 45508, "★ Specialist Gloves | Fade (Field-Tested)", typeFloat)
	if err != nil {
		log.Fatalf("Failed to crawl: %v", err)
	}
	for _, price := range prices {
		log.Println(price.SellPrice, price.BuyPrice, price.Condition.GetCondition(), price.NumSell, price.NumBuy)
	}*/

}
