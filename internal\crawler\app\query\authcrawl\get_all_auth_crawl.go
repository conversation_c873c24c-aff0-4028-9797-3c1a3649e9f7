package queryauthcral

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/pagination"
)

type AllAuthCrawlQuery struct {
	Page     int
	PageSize int
	Order    string
}

type GetAllAuthCrawlQueryHandler decorator.QueryHandler[AllAuthCrawlQuery, *pagination.PaginatedResponse[AuthCrawl]]

type getAllAuthCrawlQueryHandler struct {
	repo RepositoryQueryAuth
}

func NewGetAllAuthCrawlQueryHandler(
	repo RepositoryQueryAuth,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetAllAuthCrawlQueryHandler {

	return decorator.ApplyQueryDecorators[AllAuthCrawlQuery, *pagination.PaginatedResponse[AuthCrawl]](
		getAllAuthCrawlQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getAllAuthCrawlQueryHandler) Handle(ctx context.Context, query AllAuthCrawlQuery) (*pagination.PaginatedResponse[AuthCrawl], error) {
	p := pagination.NewPagination(query.Page, query.PageSize)
	total, err := h.repo.CountAuthCrawl(ctx)
	if err != nil {
		return nil, err
	}
	p.SetTotal(total)
	auths, err := h.repo.AllAuth(ctx, query)
	if err != nil {
		return nil, err
	}
	return pagination.CreateResponse[AuthCrawl](p, auths), nil
}
