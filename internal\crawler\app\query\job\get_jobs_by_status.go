package queryjob

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetJobsByStatusQuery struct {
	Status string
}

type GetJobsByStatusQueryHandler decorator.QueryHandler[GetJobsByStatusQuery, []*Job]

type getJobsByStatusQueryHandler struct {
	repo JobReadModel
}

func NewGetJobsByStatusQueryHandler(
	repo JobReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetJobsByStatusQueryHandler {

	return decorator.ApplyQueryDecorators[GetJobsByStatusQuery, []*Job](
		getJobsByStatusQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getJobsByStatusQueryHandler) Handle(ctx context.Context, query GetJobsByStatusQuery) ([]*Job, error) {
	return h.repo.GetJobsByStatus(ctx, query.Status)
}
