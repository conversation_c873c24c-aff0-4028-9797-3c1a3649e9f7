-- name: Create :one
INSERT INTO jobs (
    job_type,
    name,
    description,
    status,
    crawl_config_id,
    scheduled_at,
    max_retries,
    timeout_seconds,
    created_by
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9
)
RETURNING id;

-- name: CreateMany :copyfrom
INSERT INTO jobs (
    job_type,
    name,
    description,
    status,
    crawl_config_id,
    scheduled_at,
    max_retries,
    timeout_seconds,
    created_by
) VALUES
    ($1, $2, $3, $4, $5, $6, $7, $8, $9);

-- name: FindByID :one
SELECT * FROM jobs WHERE id = $1 LIMIT 1;

-- name: UpdateJob :one
UPDATE jobs
SET job_type = $2,
    name = $3,
    description = $4,
    status = $5,
    crawl_config_id = $6,
    scheduled_at = $7,
    started_at = $8,
    completed_at = $9,
    current_step = $10,
    total_steps = $11,
    progress_message = $12,
    progress_percentage = $13,
    max_retries = $14,
    retry_count = $15,
    last_error = $16,
    timeout_seconds = $17
WHERE id = $1
RETURNING *;

-- name: UpdateJobStatus :one
UPDATE jobs
SET status = $2,
    started_at = CASE WHEN $2 = 'running' AND started_at IS NULL THEN NOW() ELSE started_at END,
    completed_at = CASE WHEN $2 IN ('completed', 'failed', 'cancelled') AND completed_at IS NULL THEN NOW() ELSE completed_at END
WHERE id = $1
RETURNING *;

-- name: UpdateJobProgress :one
UPDATE jobs
SET current_step = $2,
    total_steps = $3,
    progress_message = $4,
    progress_percentage = $5
WHERE id = $1
RETURNING *;

-- name: IncrementRetryCount :one
UPDATE jobs
SET retry_count = retry_count + 1,
    last_error = $2
WHERE id = $1
RETURNING *;

-- name: DeleteJob :exec
DELETE FROM jobs WHERE id = $1;

-- Query methods for JobQueryRepository

-- name: GetAllJobsWithPagination :many
SELECT * FROM jobs
ORDER BY
    CASE WHEN $3 = 'name' THEN name END ASC,
    CASE WHEN $3 = 'name_desc' THEN name END DESC,
    CASE WHEN $3 = 'created_at' THEN created_at END ASC,
    CASE WHEN $3 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $3 = 'scheduled_at' THEN scheduled_at END ASC,
    CASE WHEN $3 = 'scheduled_at_desc' THEN scheduled_at END DESC,
    CASE WHEN $3 = '' OR $3 IS NULL THEN id END ASC
LIMIT $1 OFFSET $2;

-- name: GetJobByIdQuery :one
SELECT * FROM jobs WHERE id = $1 LIMIT 1;

-- name: GetJobsByStatusQuery :many
SELECT * FROM jobs WHERE status = $1 ORDER BY created_at ASC;

-- name: GetJobsByTypeQuery :many
SELECT * FROM jobs WHERE job_type = $1 ORDER BY created_at ASC;

-- name: GetPendingJobsQuery :many
SELECT * FROM jobs 
WHERE status = 'pending' 
   OR (status = 'scheduled' AND scheduled_at <= NOW())
ORDER BY scheduled_at ASC NULLS FIRST, created_at ASC;

-- name: GetRunningJobsQuery :many
SELECT * FROM jobs WHERE status = 'running' ORDER BY started_at ASC;

-- name: GetJobsByConfigIdQuery :many
SELECT * FROM jobs WHERE crawl_config_id = $1 ORDER BY created_at DESC;

-- name: CountJobsQuery :one
SELECT COUNT(*) FROM jobs;

-- name: CountJobsByStatusQuery :one
SELECT COUNT(*) FROM jobs WHERE status = $1;

-- name: FilterJobsQuery :many
SELECT * FROM jobs
WHERE
    ($1 = '' OR job_type = $1)
    AND ($2 = '' OR status = $2)
    AND ($3 = 0 OR crawl_config_id = $3)
    AND ($4 = '' OR created_by = $4)
    AND ($5::timestamptz IS NULL OR created_at >= $5)
    AND ($6::timestamptz IS NULL OR created_at <= $6)
ORDER BY
    CASE WHEN $7 = 'name' THEN name END ASC,
    CASE WHEN $7 = 'name_desc' THEN name END DESC,
    CASE WHEN $7 = 'created_at' THEN created_at END ASC,
    CASE WHEN $7 = 'created_at_desc' THEN created_at END DESC,
    CASE WHEN $7 = 'scheduled_at' THEN scheduled_at END ASC,
    CASE WHEN $7 = 'scheduled_at_desc' THEN scheduled_at END DESC,
    CASE WHEN $7 = '' OR $7 IS NULL THEN id END ASC;

-- name: GetJobsNeedingRetryQuery :many
SELECT * FROM jobs 
WHERE status = 'failed' 
  AND retry_count < max_retries 
ORDER BY created_at ASC;

-- name: GetExpiredJobsQuery :many
SELECT * FROM jobs 
WHERE status = 'running' 
  AND started_at IS NOT NULL 
  AND started_at + INTERVAL '1 second' * timeout_seconds < NOW()
ORDER BY started_at ASC;
