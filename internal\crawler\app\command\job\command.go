package commandjob

import (
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type JobCommands struct {
	CreateJob         CreateJobHandler
	UpdateJobStatus   UpdateJobStatusHandler
	UpdateJobProgress UpdateJobProgressHandler
	StartJob          StartJobHandler
	CompleteJob       CompleteJobHandler
	FailJob           FailJobHandler
	CancelJob         CancelJobHandler
	RetryJob          RetryJobHandler
	DeleteJob         DeleteJobHandler
}

func NewJobCommands(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) *JobCommands {
	return &JobCommands{
		CreateJob:         NewCreateJobHandler(repo, logger, metricsClient, broker),
		UpdateJobStatus:   NewUpdateJobStatusHandler(repo, logger, metricsClient, broker),
		UpdateJobProgress: NewUpdateJobProgressHandler(repo, logger, metricsClient, broker),
		StartJob:          NewStart<PERSON><PERSON><PERSON><PERSON><PERSON>(repo, logger, metricsClient, broker),
		CompleteJob:       NewCompleteJobHandler(repo, logger, metricsClient, broker),
		FailJob:           NewFailJobHandler(repo, logger, metricsClient, broker),
		CancelJob:         NewCancelJobHandler(repo, logger, metricsClient, broker),
		RetryJob:          NewRetryJobHandler(repo, logger, metricsClient, broker),
		DeleteJob:         NewDeleteJobHandler(repo, logger, metricsClient, broker),
	}
}
