package repository

import (
	"context"
	"errors"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/job/sqlc"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/database"
)

// Error variables
var (
	ErrJobNotFound    = errors.New("job not found")
	ErrEmptyJobList   = errors.New("no jobs to create")
	ErrUpdateJobFail  = errors.New("failed to update job")
	ErrCreateJobFail  = errors.New("failed to save job")
	ErrCreateJobsFail = errors.New("failed to save jobs")
	ErrDeleteJobFail  = errors.New("failed to delete job")
)

type jobRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewJobRepository creates a new job repository
func NewJobRepository(db database.DBTX) repository.RepositoryJob {
	return &jobRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// Create inserts a single job into the database
func (r *jobRepository) Create(ctx context.Context, job *entity.Job) error {
	params, err := ToCreateParams(job)
	if err != nil {
		return err
	}

	if _, err := r.queries.Create(ctx, *params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateJobFail, err)
	}

	return nil
}

// CreateMany inserts multiple jobs into the database
func (r *jobRepository) CreateMany(ctx context.Context, jobs []*entity.Job) error {
	if len(jobs) == 0 {
		return ErrEmptyJobList
	}

	params, err := ToCreateManyParams(jobs)
	if err != nil {
		return err
	}

	if _, err := r.queries.CreateMany(ctx, params); err != nil {
		return fmt.Errorf("%w: %v", ErrCreateJobsFail, err)
	}

	return nil
}

// Update updates a single job by ID using a provided update function
func (r *jobRepository) Update(ctx context.Context, id int, updateFn func(j *entity.Job) (*entity.Job, error)) error {
	rawJob, err := r.queries.FindByID(ctx, id)
	if err != nil {
		return fmt.Errorf("%w: %v", ErrJobNotFound, err)
	}

	job, err := FromSQLCModel(rawJob)
	if err != nil {
		return err
	}

	updatedJob, err := updateFn(job)
	if err != nil {
		return err
	}

	updateParams, err := ToUpdateJobParams(updatedJob)
	if err != nil {
		return err
	}

	if _, err := r.queries.UpdateJob(ctx, *updateParams); err != nil {
		return fmt.Errorf("%w: %v", ErrUpdateJobFail, err)
	}

	return nil
}

// Delete removes a job by ID
func (r *jobRepository) Delete(ctx context.Context, id int) error {
	if err := r.queries.DeleteJob(ctx, id); err != nil {
		return fmt.Errorf("%w: %v", ErrDeleteJobFail, err)
	}
	return nil
}
