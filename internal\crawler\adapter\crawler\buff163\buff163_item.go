package buff163

type PriceItem struct {
	Appid                  int       `json:"appid"`
	Game                   string    `json:"game"`
	ID                     int       `json:"id"`
	Name                   string    `json:"name"`
	MarketHashName         string    `json:"market_hash_name"`
	SteamMarketURL         string    `json:"steam_market_url"`
	SellReferencePrice     string    `json:"sell_reference_price"`
	SellMinPrice           string    `json:"sell_min_price"`
	BuyMaxPrice            string    `json:"buy_max_price"`
	SellNum                int       `json:"sell_num"`
	BuyNum                 int       `json:"buy_num"`
	TransactedNum          int       `json:"transacted_num"`
	QuickPrice             string    `json:"quick_price"`
	MarketMinPrice         string    `json:"market_min_price"`
	Description            *string   `json:"description"`
	CanSearchByTournament  bool      `json:"can_search_by_tournament"`
	ShortName              string    `json:"short_name"`
	RentUnitReferencePrice string    `json:"rent_unit_reference_price"`
	RentNum                int       `json:"rent_num"`
	MinRentUnitPrice       string    `json:"min_rent_unit_price"`
	MinSecurityPrice       string    `json:"min_security_price"`
	AuctionNum             int       `json:"auction_num"`
	GoodsInfo              GoodsInfo `json:"goods_info"`
}

type GoodsInfo struct {
	IconURL         string  `json:"icon_url"`
	SteamPrice      string  `json:"steam_price"`
	ItemID          *string `json:"item_id"`
	SteamPriceCNY   string  `json:"steam_price_cny"`
	OriginalIconURL string  `json:"original_icon_url"`

	Info struct {
		Tags Tags `json:"tags"`
	} `json:"info"`
}

type Tags struct {
	Exterior TagDetail `json:"exterior"`
	Quality  TagDetail `json:"quality"`
	Rarity   TagDetail `json:"rarity"`
	Type     TagDetail `json:"type"`
	Weapon   TagDetail `json:"weapon"`
	Category TagDetail `json:"category"`
}

type TagDetail struct {
	ID            int    `json:"id"`
	Category      string `json:"category"`
	InternalName  string `json:"internal_name"`
	LocalizedName string `json:"localized_name"`
}

type SellItem struct {
	Appid     int       `json:"appid"`
	Game      string    `json:"game"`
	ID        string    `json:"id"`
	AssetInfo AssetInfo `json:"asset_info"`
	Price     string    `json:"price"`
	Fee       string    `json:"fee"`
	GoodsID   int       `json:"goods_id"`
}

type AssetInfo struct {
	Appid      int              `json:"appid"`
	ContextID  int              `json:"contextid"`
	AssetID    string           `json:"assetid"`
	ClassID    string           `json:"classid"`
	InstanceID string           `json:"instanceid"`
	GoodsID    int              `json:"goods_id"`
	PaintWear  string           `json:"paintwear"`
	ActionLink string           `json:"action_link"`
	ID         string           `json:"id"`
	Info       AssetInfoDetails `json:"info"`
}

type AssetInfoDetails struct {
	IconURL         string      `json:"icon_url"`
	OriginalIconURL string      `json:"original_icon_url"`
	PaintSeed       int         `json:"paintseed"`
	PaintIndex      int         `json:"paintindex"`
	PhaseData       *PhaseData  `json:"phase_data"`
	MetaPhysic      *MetaPhysic `json:"metaphysic"`
}

type OrderItem struct {
	AppID        int             `json:"appid"`
	Game         string          `json:"game"`
	ID           string          `json:"id"`
	Num          int             `json:"num"`
	RealNum      int             `json:"real_num"`
	FrozenNum    int             `json:"frozen_num"`
	State        string          `json:"state"`
	GoodsID      int             `json:"goods_id"`
	Price        string          `json:"price"`
	Fee          string          `json:"fee"`
	FrozenAmount string          `json:"frozen_amount"`
	Specific     []SpecificField `json:"specific"`
}

type SpecificField struct {
	Color      string        `json:"color"`
	Type       string        `json:"type"`
	Text       string        `json:"text"`
	SimpleText string        `json:"simple_text"`
	Values     []interface{} `json:"values"` // Có thể là int hoặc string, dùng interface{}
}

type PhaseData struct {
	Name  string `json:"name"`
	Color string `json:"color"`
}

type MetaPhysic struct {
	Data  PhaseData `json:"data"`
	Title string    `json:"title"`
}
