syntax = "proto3";

package market.v1;

option go_package = "go_core_market/pkg/pb/market";

import "google/protobuf/timestamp.proto";

// Market message
message Market {
  int32 id = 1;
  string name = 2;
  string display_name = 3;
  string type = 4;
  string status = 5;
  string base_url = 6;
  string currency = 7;
  double buyer_fee_percent = 8;
  double seller_fee_percent = 9;
  string country_code = 10;
  string language = 11;
  string description = 12;
  bool is_active = 13;
  google.protobuf.Timestamp last_crawl_at = 14;
  google.protobuf.Timestamp created_at = 15;
  google.protobuf.Timestamp updated_at = 16;
}

// Request/Response messages for Commands
message CreateMarketRequest {
  string name = 1;
  string display_name = 2;
  string type = 3;
  string base_url = 4;
  string currency = 5;
  double buyer_fee_percent = 6;
  double seller_fee_percent = 7;
  string country_code = 8;
  string language = 9;
  string description = 10;
  string created_by = 11;
}

message CreateMarketResponse {
  bool success = 1;
  string message = 2;
}

message UpdateMarketRequest {
  int32 id = 1;
  string name = 2;
  string display_name = 3;
  string type = 4;
  string base_url = 5;
  string currency = 6;
  double buyer_fee_percent = 7;
  double seller_fee_percent = 8;
  string country_code = 9;
  string language = 10;
  string description = 11;
  string updated_by = 12;
}

message UpdateMarketResponse {
  bool success = 1;
  string message = 2;
}

message DeleteMarketRequest {
  int32 id = 1;
  string deleted_by = 2;
}

message DeleteMarketResponse {
  bool success = 1;
  string message = 2;
}

message ActiveMarketRequest {
  int32 market_id = 1;
  string activated_by = 2;
}

message ActiveMarketResponse {
  bool success = 1;
  string message = 2;
}

message DeactiveMarketRequest {
  int32 market_id = 1;
  string deactivated_by = 2;
}

message DeactiveMarketResponse {
  bool success = 1;
  string message = 2;
}

message SetMarketMaintenanceRequest {
  int32 market_id = 1;
  string maintained_by = 2;
}

message SetMarketMaintenanceResponse {
  bool success = 1;
  string message = 2;
}

// Request/Response messages for Queries
message GetAllMarketsRequest {
  int32 page = 1;
  int32 page_size = 2;
  string order_by = 3;
}

message GetAllMarketsResponse {
  repeated Market markets = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
  int32 total_pages = 5;
}

message GetMarketByIdRequest {
  int32 id = 1;
}

message GetMarketByIdResponse {
  Market market = 1;
}

message GetActiveMarketsRequest {
  // No parameters needed
}

message GetActiveMarketsResponse {
  repeated Market markets = 1;
}

message FilterMarketsRequest {
  repeated string types = 1;
  repeated string statuses = 2;
  repeated string currencies = 3;
  optional bool is_active = 4;
  string country_code = 5;
  string order_by = 6;
}

message FilterMarketsResponse {
  repeated Market markets = 1;
}

// gRPC Service
service MarketService {
  // Commands
  rpc CreateMarket(CreateMarketRequest) returns (CreateMarketResponse);
  rpc UpdateMarket(UpdateMarketRequest) returns (UpdateMarketResponse);
  rpc DeleteMarket(DeleteMarketRequest) returns (DeleteMarketResponse);
  rpc ActiveMarket(ActiveMarketRequest) returns (ActiveMarketResponse);
  rpc DeactiveMarket(DeactiveMarketRequest) returns (DeactiveMarketResponse);
  rpc SetMarketMaintenance(SetMarketMaintenanceRequest) returns (SetMarketMaintenanceResponse);

  // Queries
  rpc GetAllMarkets(GetAllMarketsRequest) returns (GetAllMarketsResponse);
  rpc GetMarketById(GetMarketByIdRequest) returns (GetMarketByIdResponse);
  rpc GetActiveMarkets(GetActiveMarketsRequest) returns (GetActiveMarketsResponse);
  rpc FilterMarkets(FilterMarketsRequest) returns (FilterMarketsResponse);
}
