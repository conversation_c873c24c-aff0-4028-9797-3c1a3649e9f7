package entity

import (
	"errors"
	"time"
)

var (
	ErrInvalidTypeConfig = errors.New("invalid type config")
	ErrInvalidRateLimit  = errors.New("requests per minute must be greater than 0")
	ErrInvalidTimeout    = errors.New("timeout must be greater than 0")
	ErrInvalidConcurrent = errors.New("max concurrent must be greater than 0")
)

type TypeConfig string

const (
	TypeConfigNormal  TypeConfig = "normal"
	TypeConfigDoppler TypeConfig = "doppler"
	TypeConfigFloat   TypeConfig = "float"
	TypeConfigFade    TypeConfig = "fade"
	TypeConfigBlueGem TypeConfig = "blue_gem"
	TypeConfigItem    TypeConfig = "item"
)

func (tp TypeConfig) IsValid() bool {
	switch tp {
	case TypeConfigNormal, TypeConfigDoppler, TypeConfigFloat, TypeConfigFade, TypeConfigBlueGem, TypeConfigItem:
		return true
	default:
		return false
	}
}

type CrawlConfigParams struct {
	MarketID          int
	Description       string // Mô tả cấu hình (ví dụ: "Config chính", "Config dự phòng")
	TypeConfig        string
	RequestsPerMinute int  // tối đa bao request mỗi phút
	RequireAuth       bool // có cần dùng auth không
	MaxNumberAuth     int  // cần lấy max bao nhiêu auth
	MaxNumberProxy    int  // cần lấy max bao nhiêu proxy cho cấu hình này
	PerRequestDelay   int  //tính bằng giây
	Timeout           int  // Timeout cho mỗi request/ tính bằng giây
	MaxRetries        int  // Số lần thử lại tối đa
	RetryDelay        int  // Thời gian chờ giữa các lần thử lại

}

type CrawlConfig struct {
	id                int
	marketID          int
	description       string // Mô tả cấu hình (ví dụ: "Config chính", "Config dự phòng")
	typeConfig        TypeConfig
	requestsPerMinute int
	requireAuth       bool // có cần dùng auth không
	maxNumberAuth     int  // cần lấy max bao nhiêu auth
	maxNumberProxy    int  // cần lấy max bao nhiêu proxy cho cấu hình này
	perRequestDelay   time.Duration
	timeout           time.Duration // Timeout cho mỗi request
	maxRetries        int           // Số lần thử lại tối đa
	retryDelay        time.Duration // Thời gian chờ giữa các lần thử lại

	isActive          bool          // Config có đang kích hoạt không
	lastUsedAt        *time.Time    // Lần cuối sử dụng
}

func NewCrawlConfig(params CrawlConfigParams) (*CrawlConfig, error) {
	typeConfig := TypeConfig(params.TypeConfig)
	if !typeConfig.IsValid() {
		return nil, ErrInvalidTypeConfig
	}
	if params.RequestsPerMinute <= 0 {
		return nil, ErrInvalidRateLimit
	}

	if params.Timeout <= 0 {
		return nil, ErrInvalidTimeout
	}


	cfg := &CrawlConfig{
		marketID:          params.MarketID,
		description:       params.Description,
		typeConfig:        typeConfig,
		requestsPerMinute: params.RequestsPerMinute,
		requireAuth:       params.RequireAuth,
		maxNumberAuth:     params.MaxNumberAuth,
		maxNumberProxy:    params.MaxNumberProxy,
		perRequestDelay:   time.Duration(params.PerRequestDelay) * time.Second,
		timeout:           time.Duration(params.Timeout) * time.Second,
		maxRetries:        params.MaxRetries,
		retryDelay:        time.Duration(params.RetryDelay) * time.Second,

		isActive:          true,
		lastUsedAt:        nil, // chưa sử dụng
	}
	return cfg, nil
}

type ReBuildConfigParams struct {
	ID                int
	MarketID          int
	Description       string // Mô tả cấu hình (ví dụ: "Config chính", "Config dự phòng")
	TypeConfig        string
	RequestsPerMinute int  // tối đa bao request mỗi phút
	RequireAuth       bool // có cần dùng auth không
	MaxNumberAuth     int  // cần lấy max bao nhiêu auth
	MaxNumberProxy    int  // cần lấy max bao nhiêu proxy cho cấu hình này
	PerRequestDelay   int  //tính bằng giây
	Timeout           int  // Timeout cho mỗi request/ tính bằng giây
	MaxRetries        int  // Số lần thử lại tối đa
	RetryDelay        int  // Thời gian chờ giữa các lần thử lại

	IsActive          bool
	LastUsedAt        *time.Time
}

func RebuildConfig(params ReBuildConfigParams) (*CrawlConfig, error) {
	cfg := &CrawlConfig{
		marketID:          params.MarketID,
		description:       params.Description,
		typeConfig:        TypeConfig(params.TypeConfig),
		requestsPerMinute: params.RequestsPerMinute,
		requireAuth:       params.RequireAuth,
		maxNumberAuth:     params.MaxNumberAuth,
		maxNumberProxy:    params.MaxNumberProxy,
		perRequestDelay:   time.Duration(params.PerRequestDelay) * time.Second,
		timeout:           time.Duration(params.Timeout) * time.Second,
		maxRetries:        params.MaxRetries,
		retryDelay:        time.Duration(params.RetryDelay) * time.Second,

		isActive:          params.IsActive,
		lastUsedAt:        params.LastUsedAt,
	}
	return cfg, nil
}
func (c *CrawlConfig) ID() int                        { return c.id }
func (c *CrawlConfig) MarketID() int                  { return c.marketID }
func (c *CrawlConfig) Description() string            { return c.description }
func (c *CrawlConfig) TypeConfig() TypeConfig         { return c.typeConfig }
func (c *CrawlConfig) RequestsPerMinute() int         { return c.requestsPerMinute }
func (c *CrawlConfig) RequireAuth() bool              { return c.requireAuth }
func (c *CrawlConfig) MaxNumberAuth() int             { return c.maxNumberAuth }
func (c *CrawlConfig) MaxNumberProxy() int            { return c.maxNumberProxy }
func (c *CrawlConfig) PerRequestDelay() time.Duration { return c.perRequestDelay }
func (c *CrawlConfig) Timeout() time.Duration         { return c.timeout }
func (c *CrawlConfig) MaxRetries() int                { return c.maxRetries }
func (c *CrawlConfig) RetryDelay() time.Duration      { return c.retryDelay }

func (c *CrawlConfig) IsActive() bool                 { return c.isActive }
func (c *CrawlConfig) LastUsedAt() *time.Time         { return c.lastUsedAt }

func (c *CrawlConfig) Activate() error {
	if c.isActive {
		return errors.New("already activated")
	}
	c.isActive = true
	return nil
}

func (c *CrawlConfig) Deactivate() error {
	if !c.isActive {
		return errors.New("already deactivated")
	}
	c.isActive = false
	return nil
}

func (c *CrawlConfig) UpdateLastUsedAt() {
	now := time.Now()
	c.lastUsedAt = &now
}

type UpdateRequestParams struct {
	RequestsPerMinute int
	PerRequestDelay   int
	MaxRetries        int
	RetryDelay        int

	Timeout           int
}

func (c *CrawlConfig) UpdateRequests(params UpdateRequestParams) error {
	if params.RequestsPerMinute <= 0 {
		return ErrInvalidRateLimit
	}

	if params.Timeout <= 0 {
		return ErrInvalidTimeout
	}


	if params.PerRequestDelay <= 0 {
		return ErrInvalidConcurrent
	}
	c.requestsPerMinute = params.RequestsPerMinute
	c.perRequestDelay = time.Duration(params.PerRequestDelay) * time.Second

	c.timeout = time.Duration(params.Timeout) * time.Second
	c.maxRetries = params.MaxRetries
	c.retryDelay = time.Duration(params.RetryDelay) * time.Second
	return nil
}

type UpdateResourcesParams struct {
	MaxNumberProxy int
	MaxNumberAuth  int
}

func (c *CrawlConfig) UpdateResourcesCrawl(params UpdateResourcesParams) error {
	if params.MaxNumberProxy <= 0 {
		return ErrInvalidRateLimit
	}
	if params.MaxNumberAuth <= 0 {
		return ErrInvalidTimeout
	}
	c.maxNumberProxy = params.MaxNumberProxy
	c.maxNumberAuth = params.MaxNumberAuth
	return nil
}

func (c *CrawlConfig) ToParams() ReBuildConfigParams {
	return ReBuildConfigParams{
		ID:                c.id,
		MarketID:          c.marketID,
		Description:       c.description,
		TypeConfig:        string(c.typeConfig),
		RequestsPerMinute: c.requestsPerMinute,
		RequireAuth:       c.requireAuth,
		MaxNumberAuth:     c.maxNumberAuth,
		MaxNumberProxy:    c.maxNumberProxy,
		PerRequestDelay:   int(c.perRequestDelay.Seconds()),
		Timeout:           int(c.timeout.Seconds()),
		MaxRetries:        c.maxRetries,
		RetryDelay:        int(c.retryDelay.Seconds()),

		IsActive:          c.isActive,
		LastUsedAt:        c.lastUsedAt,
	}
}
