package commandmarket

import (
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type MarketCommands struct {
	CreateMarket         CreateMarketHandler
	UpdateMarket         UpdateMarketHandler
	DeleteMarket         DeleteMarketHandler
	ActiveMarket         ActiveMarketHandler
	DeactivateMarket     DeactivateMarketHandler
	SetMarketMaintenance SetMarketMaintenanceHandler
}

func NewMarketCommands(repo repository.RepositoryMarket,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker) *MarketCommands {
	return &MarketCommands{
		CreateMarket:         NewCreateMarketHandler(repo, logger, metricsClient, broker),
		UpdateMarket:         NewUpdateMarketHandler(repo, logger, metricsClient, broker),
		DeleteMarket:         NewDeleteMarketHandler(repo, logger, metricsClient, broker),
		ActiveMarket:         NewActiveMarketHandler(repo, logger, metricsClient, broker),
		DeactivateMarket:     NewDeactivateMarketHandler(repo, logger, metricsClient, broker),
		SetMarketMaintenance: NewSetMarketMaintenanceHandler(repo, logger, metricsClient, broker),
	}
}
