package commandjob

import (
	"context"
	"errors"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type UpdateJobStatus struct {
	ID        int
	Status    string
	UpdatedBy string
}

type UpdateJobStatusHandler decorator.CommandHandler[UpdateJobStatus]

type updateJobStatusHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewUpdateJobStatusHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) UpdateJobStatusHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[UpdateJobStatus](
		updateJobStatusHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h updateJobStatusHandler) Handle(ctx context.Context, cmd UpdateJobStatus) error {
	return h.repo.Update(ctx, cmd.ID, func(j *entity.Job) (*entity.Job, error) {
		switch cmd.Status {
		case "running":
			return j, j.Start()
		case "paused":
			return j, j.Pause()
		case "completed":
			return j, j.Complete()
		case "cancelled":
			return j, j.Cancel()
		default:
			return nil, errors.New("invalid job status")
		}
	})
}
