package entity

import (
	"errors"
	"fmt"
	"strings"
	"time"
)

type ProxyParams struct {
	Host     string
	Port     int
	UserName string
	Password string
	IsActive bool
}

type ProxyRebuildParams struct {
	ID         int
	Host       string
	Port       int
	UserName   string
	Password   string
	IsActive   bool
	IsUse      bool
	LastUsedAt *time.Time
}

type Proxy struct {
	id         int
	host       string
	port       int
	userName   string
	password   string
	isActive   bool
	isUse      bool
	lastUsedAt *time.Time
}

// Constructor
func NewProxy(params ProxyParams) (*Proxy, error) {
	if params.Host == "" {
		return nil, errors.New("proxy host cannot be empty")
	}
	if params.Port <= 0 || params.Port > 65535 {
		return nil, errors.New("proxy port must be between 1 and 65535")
	}

	// Clean and validate host
	cleanHost := strings.TrimSpace(params.Host)
	if cleanHost == "" {
		return nil, errors.New("proxy host cannot be empty after cleaning")
	}

	return &Proxy{
		host:     cleanHost,
		port:     params.Port,
		userName: params.UserName,
		password: params.Password,
		isActive: params.IsActive,
		isUse:    false,
	}, nil
}

func NewProxyFromString(s string) (*Proxy, error) {
	var userName, password, host string
	var port int

	// Tách phần username:password (nếu có)
	authHost := s
	if atIdx := strings.LastIndex(s, "@"); atIdx != -1 {
		auth := s[:atIdx]
		authHost = s[atIdx+1:]
		authParts := strings.SplitN(auth, ":", 2)
		if len(authParts) != 2 {
			return nil, errors.New("invalid auth format, expected username:password")
		}
		userName = authParts[0]
		password = authParts[1]
	}

	// Tách host:port
	hostParts := strings.Split(authHost, ":")
	if len(hostParts) != 2 {
		return nil, errors.New("invalid host format, expected host:port")
	}
	host = hostParts[0]
	_, err := fmt.Sscanf(hostParts[1], "%d", &port)
	if err != nil {
		return nil, fmt.Errorf("invalid port: %w", err)
	}

	return &Proxy{
		host:     host,
		port:     port,
		userName: userName,
		password: password,
		isActive: true,
		isUse:    false,
	}, nil
}

func RebuildProxy(p ProxyRebuildParams) *Proxy {
	return &Proxy{
		id:         p.ID,
		host:       p.Host,
		port:       p.Port,
		userName:   p.UserName,
		password:   p.Password,
		isActive:   p.IsActive,
		isUse:      p.IsUse,
		lastUsedAt: p.LastUsedAt,
	}
}

func (p *Proxy) ID() int                { return p.id }
func (p *Proxy) Host() string           { return p.host }
func (p *Proxy) Port() int              { return p.port }
func (p *Proxy) UserName() string       { return p.userName }
func (p *Proxy) Password() string       { return p.password }
func (p *Proxy) IsActive() bool         { return p.isActive }
func (p *Proxy) IsUse() bool            { return p.isUse }
func (p *Proxy) LastUsedAt() *time.Time { return p.lastUsedAt }

// Status management
func (p *Proxy) Activate() error {
	if p.isActive {
		return fmt.Errorf("proxy is already active")
	}
	p.isActive = true
	p.isUse = false
	return nil
}

func (p *Proxy) Deactivate() error {
	if !p.isActive {
		return fmt.Errorf("proxy is not active")
	}
	p.isActive = false
	p.isUse = false // Khi deactivate thì cũng release luôn
	return nil
}

// Usage management
func (p *Proxy) MarkUsed() error {
	if !p.isActive {
		return fmt.Errorf("proxy is not active")
	}
	p.isUse = true
	now := time.Now()
	p.lastUsedAt = &now
	return nil
}

func (p *Proxy) Release() error {
	if !p.isActive {
		return fmt.Errorf("proxy is not active")
	}
	if !p.isUse {
		return fmt.Errorf("proxy is not use")
	}
	p.isUse = false
	return nil
}

func (p *Proxy) UpdateInfo(host string, port int, userName, password string) error {
	if host == "" {
		return errors.New("proxy host cannot be empty")
	}
	if port <= 0 || port > 65535 {
		return errors.New("proxy port must be between 1 and 65535")
	}

	cleanHost := strings.TrimSpace(host)
	if cleanHost == "" {
		return errors.New("proxy host cannot be empty after cleaning")
	}

	p.host = cleanHost
	p.port = port
	p.userName = userName
	p.password = password
	return nil
}

// Validation methods
func (p *Proxy) IsAvailable() bool {
	return p.isActive && !p.isUse
}

func (p *Proxy) CanBeUsed() error {
	if !p.isActive {
		return fmt.Errorf("proxy %s:%d is not active", p.host, p.port)
	}
	if p.isUse {
		return fmt.Errorf("proxy %s:%d is already in use", p.host, p.port)
	}
	return nil
}

func (p *Proxy) GetConnectionString() string {
	if p.userName != "" && p.password != "" {
		return fmt.Sprintf("%s:%s@%s:%d", p.userName, p.password, p.host, p.port)
	}
	return fmt.Sprintf("%s:%d", p.host, p.port)
}

func (p *Proxy) GetUsageInfo() string {
	if p.lastUsedAt == nil {
		return "Never used"
	}
	return fmt.Sprintf("Last used: %s", p.lastUsedAt.Format("2006-01-02 15:04:05"))
}

func (p *Proxy) String() string {
	status := "inactive"
	if p.isActive {
		status = "active"
	}
	usage := "available"
	if p.isUse {
		usage = "in use"
	}
	return fmt.Sprintf("Proxy[%d] %s:%d (%s, %s)", p.id, p.host, p.port, status, usage)
}
