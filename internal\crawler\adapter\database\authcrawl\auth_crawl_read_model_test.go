package repository

import (
	"context"
	"testing"
	"time"

	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	sqlc2 "go_core_market/internal/crawler/adapter/database/authcrawl/sqlc"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockQuerier is a mock implementation of sqlc2.Querier
type MockQuerier struct {
	mock.Mock
}

func (m *<PERSON>ck<PERSON>uerier) CountAuthCrawl(ctx context.Context) (int64, error) {
	args := m.Called(ctx)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockQuerier) CreateAuth(ctx context.Context, arg sqlc2.CreateAuthParams) (int, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(int), args.Error(1)
}

func (m *<PERSON>ck<PERSON>uerier) CreateMany(ctx context.Context, arg []sqlc2.CreateManyParams) (int64, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockQuerier) DeleteAuth(ctx context.Context, id int) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockQuerier) FindAuthByID(ctx context.Context, id int) (*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) FindAuthByIDs(ctx context.Context, ids []int32) ([]*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, ids)
	return args.Get(0).([]*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) FindByValue(ctx context.Context, value string) (*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, value)
	return args.Get(0).(*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) GetAllAuthCrawl(ctx context.Context, arg sqlc2.GetAllAuthCrawlParams) ([]*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).([]*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) GetAuthCrawlByMarketId(ctx context.Context, marketID int) (*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, marketID)
	return args.Get(0).(*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) GetAvailableAuthCrawlByMarket(ctx context.Context, arg sqlc2.GetAvailableAuthCrawlByMarketParams) ([]*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).([]*sqlc2.AuthCrawl), args.Error(1)
}

func (m *MockQuerier) UpdateAuth(ctx context.Context, arg sqlc2.UpdateAuthParams) (*sqlc2.AuthCrawl, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(*sqlc2.AuthCrawl), args.Error(1)
}

func TestAuthCrawlReadModel_GetAuthCrawlById(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &authCrawlReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	authID := 1
	expectedSQLCAuth := &sqlc2.AuthCrawl{
		ID:       1,
		MarketID: 1,
		AuthType: "api_key",
		Value:    "test-api-key",
		IsActive: true,
		IsUse:    false,
		LastUsedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
		ExpiredAt: pgtype.Timestamptz{
			Time:  time.Now().Add(24 * time.Hour),
			Valid: true,
		},
		CreatedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
		UpdatedAt: pgtype.Timestamptz{
			Time:  time.Now(),
			Valid: true,
		},
	}

	mockQuerier.On("FindAuthByID", ctx, authID).Return(expectedSQLCAuth, nil)

	// Act
	result, err := readModel.GetAuthCrawlById(ctx, authID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, authID, result.Id)
	assert.Equal(t, 1, result.MarketID)
	assert.Equal(t, "api_key", result.AuthType)
	assert.Equal(t, "test-api-key", result.Value)
	assert.True(t, result.IsActive)
	assert.False(t, result.IsUse)

	mockQuerier.AssertExpectations(t)
}

func TestAuthCrawlReadModel_GetAuthCrawlByMarketId(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &authCrawlReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	marketID := 1
	expectedSQLCAuth := &sqlc2.AuthCrawl{
		ID:       1,
		MarketID: marketID,
		AuthType: "cookie",
		Value:    "session=abc123",
		IsActive: true,
		IsUse:    false,
		CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}

	mockQuerier.On("GetAuthCrawlByMarketId", ctx, marketID).Return(expectedSQLCAuth, nil)

	// Act
	result, err := readModel.GetAuthCrawlByMarketId(ctx, marketID)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, 1, result.Id)
	assert.Equal(t, marketID, result.MarketID)
	assert.Equal(t, "cookie", result.AuthType)
	assert.Equal(t, "session=abc123", result.Value)

	mockQuerier.AssertExpectations(t)
}

func TestAuthCrawlReadModel_CountAuthCrawl(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &authCrawlReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	expectedCount := int64(15)

	mockQuerier.On("CountAuthCrawl", ctx).Return(expectedCount, nil)

	// Act
	result, err := readModel.CountAuthCrawl(ctx)

	// Assert
	assert.NoError(t, err)
	assert.Equal(t, expectedCount, result)

	mockQuerier.AssertExpectations(t)
}

func TestAuthCrawlReadModel_GetAvailableAuthCrawlByMarket(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &authCrawlReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	marketID := 1
	limit := 5
	expectedSQLCAuths := []*sqlc2.AuthCrawl{
		{
			ID:       1,
			MarketID: marketID,
			AuthType: "api_key",
			Value:    "key1",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
		{
			ID:       2,
			MarketID: marketID,
			AuthType: "cookie",
			Value:    "session=xyz789",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}

	expectedParams := sqlc2.GetAvailableAuthCrawlByMarketParams{
		MarketID: marketID,
		Limit:    int32(limit),
	}

	mockQuerier.On("GetAvailableAuthCrawlByMarket", ctx, expectedParams).Return(expectedSQLCAuths, nil)

	// Act
	result, err := readModel.GetAvailableAuthCrawlByMarket(ctx, marketID, limit)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, 1, result[0].Id)
	assert.Equal(t, "api_key", result[0].AuthType)
	assert.Equal(t, 2, result[1].Id)
	assert.Equal(t, "cookie", result[1].AuthType)

	mockQuerier.AssertExpectations(t)
}

func TestAuthCrawlReadModel_AllAuth(t *testing.T) {
	// Arrange
	mockQuerier := new(MockQuerier)
	readModel := &authCrawlReadModel{
		queries: mockQuerier,
	}

	ctx := context.Background()
	query := queryauthcral.AllAuthCrawlQuery{
		Page:     1,
		PageSize: 10,
		Order:    "id",
	}

	expectedSQLCAuths := []*sqlc2.AuthCrawl{
		{
			ID:       1,
			MarketID: 1,
			AuthType: "api_key",
			Value:    "test-key",
			IsActive: true,
			IsUse:    false,
			CreatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
			UpdatedAt: pgtype.Timestamptz{Time: time.Now(), Valid: true},
		},
	}

	expectedParams := sqlc2.GetAllAuthCrawlParams{
		Limit:   10,
		Offset:  0,
		Column3: "id",
	}

	mockQuerier.On("GetAllAuthCrawl", ctx, expectedParams).Return(expectedSQLCAuths, nil)

	// Act
	result, err := readModel.AllAuth(ctx, query)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 1)
	assert.Equal(t, 1, result[0].Id)
	assert.Equal(t, "api_key", result[0].AuthType)

	mockQuerier.AssertExpectations(t)
}
