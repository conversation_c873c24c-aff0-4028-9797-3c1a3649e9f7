package commandcrawlconfig

import (
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CrawlConfigCommands struct {
	CreateCrawlConfig         CreateCrawlConfigHandler
	UpdateRequestCrawConfig   UpdateRequestCrawlHandler
	UpdateResourcesCrawConfig UpdateResourcesCrawlHandler
	ActivateCrawlConfig       ActivateCrawlConfigHandler
	DeactivateCrawlConfig     DeactivateCrawlConfigHandler
	UpdateLastUsed            UpdateLastUsedHandler
	DeleteCrawlConfig         DeleteCrawlConfigHandler
}

func NewCrawlConfigCommands(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) *CrawlConfigCommands {
	return &CrawlConfigCommands{
		CreateCrawlConfig:         NewCreateCrawlConfigHandler(repo, logger, metricsClient, broker),
		UpdateResourcesCrawConfig: NewUpdateResourcesCrawlHandler(repo, logger, metricsClient, broker),
		UpdateRequestCrawConfig:   NewUpdateRequestCrawlHandler(repo, logger, metricsClient, broker),
		ActivateCrawlConfig:       NewActivateCrawlConfigHandler(repo, logger, metricsClient, broker),
		DeactivateCrawlConfig:     NewDeactivateCrawlConfigHandler(repo, logger, metricsClient, broker),
		UpdateLastUsed:            NewUpdateLastUsedHandler(repo, logger, metricsClient, broker),
		DeleteCrawlConfig:         NewDeleteCrawlConfigHandler(repo, logger, metricsClient, broker),
	}
}
