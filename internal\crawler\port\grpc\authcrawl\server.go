package grpc

import (
	"context"
	app2 "go_core_market/internal/crawler/app"
	commandauthcrawl "go_core_market/internal/crawler/app/command/authcrawl"
	queryauthcral "go_core_market/internal/crawler/app/query/authcrawl"
	"time"

	pb "go_core_market/pkg/pb/auth_crawl"
)

type AuthCrawlGRPCServer struct {
	app app2.Application
	pb.UnimplementedAuthCrawlServiceServer
}

func NewAuthCrawlGRPCServer(app app2.Application) *AuthCrawlGRPCServer {
	return &AuthCrawlGRPCServer{
		app: app,
	}
}

// Command handlers
func (s *AuthCrawlGRPCServer) CreateAuth(ctx context.Context, req *pb.CreateAuthRequest) (*pb.CreateAuthResponse, error) {
	var expiredAt *time.Time
	if req.ExpiredAt != nil {
		t := req.ExpiredAt.AsTime()
		expiredAt = &t
	}

	cmd := commandauthcrawl.CreateAuth{
		MarketID:  int(req.MarketId),
		AuthType:  req.AuthType,
		Value:     req.Value,
		IsActive:  req.IsActive,
		ExpiredAt: expiredAt,
		CreatedBy: req.CreatedBy,
	}

	err := s.app.Commands.AuthCrawl.CreateAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateAuthResponse{
		Success: true,
		Message: "Auth created successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) CreateAuthBatch(ctx context.Context, req *pb.CreateAuthBatchRequest) (*pb.CreateAuthBatchResponse, error) {
	var auths []commandauthcrawl.CreateAuth
	for _, authReq := range req.Auths {
		var expiredAt *time.Time
		if authReq.ExpiredAt != nil {
			t := authReq.ExpiredAt.AsTime()
			expiredAt = &t
		}

		auths = append(auths, commandauthcrawl.CreateAuth{
			MarketID:  int(authReq.MarketId),
			AuthType:  authReq.AuthType,
			Value:     authReq.Value,
			IsActive:  authReq.IsActive,
			ExpiredAt: expiredAt,
			CreatedBy: authReq.CreatedBy,
		})
	}

	cmd := commandauthcrawl.CreateAuthBatch{
		Auths:     auths,
		CreatedBy: req.CreatedBy,
	}

	err := s.app.Commands.AuthCrawl.CreateAuthBatch.Handle(ctx, cmd)
	if err != nil {
		return &pb.CreateAuthBatchResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.CreateAuthBatchResponse{
		Success: true,
		Message: "Auth batch created successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) UpdateAuth(ctx context.Context, req *pb.UpdateAuthRequest) (*pb.UpdateAuthResponse, error) {
	var expiredAt *time.Time
	if req.ExpiredAt != nil {
		t := req.ExpiredAt.AsTime()
		expiredAt = &t
	}

	cmd := commandauthcrawl.UpdateAuth{
		ID:        int(req.Id),
		MarketID:  int(req.MarketId),
		AuthType:  req.AuthType,
		Value:     req.Value,
		ExpiredAt: expiredAt,
		UpdatedBy: req.UpdatedBy,
	}

	err := s.app.Commands.AuthCrawl.UpdateAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.UpdateAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.UpdateAuthResponse{
		Success: true,
		Message: "Auth updated successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) DeleteAuth(ctx context.Context, req *pb.DeleteAuthRequest) (*pb.DeleteAuthResponse, error) {
	cmd := commandauthcrawl.DeleteAuth{
		ID:        int(req.Id),
		DeletedBy: req.DeletedBy,
	}

	err := s.app.Commands.AuthCrawl.DeleteAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeleteAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeleteAuthResponse{
		Success: true,
		Message: "Auth deleted successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) ActivateAuth(ctx context.Context, req *pb.ActivateAuthRequest) (*pb.ActivateAuthResponse, error) {
	cmd := commandauthcrawl.ActivateAuth{
		ID:          int(req.Id),
		ActivatedBy: req.ActivatedBy,
	}

	err := s.app.Commands.AuthCrawl.ActivateAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.ActivateAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ActivateAuthResponse{
		Success: true,
		Message: "Auth activated successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) DeactivateAuth(ctx context.Context, req *pb.DeactivateAuthRequest) (*pb.DeactivateAuthResponse, error) {
	cmd := commandauthcrawl.DeactivateAuth{
		ID:            int(req.Id),
		DeactivatedBy: req.DeactivatedBy,
	}

	err := s.app.Commands.AuthCrawl.DeactivateAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.DeactivateAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.DeactivateAuthResponse{
		Success: true,
		Message: "Auth deactivated successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) MarkAuthUsed(ctx context.Context, req *pb.MarkAuthUsedRequest) (*pb.MarkAuthUsedResponse, error) {
	cmd := commandauthcrawl.MarkAuthUsed{
		ID:     int(req.Id),
		UsedBy: req.UsedBy,
	}

	err := s.app.Commands.AuthCrawl.MarkAuthUsed.Handle(ctx, cmd)
	if err != nil {
		return &pb.MarkAuthUsedResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.MarkAuthUsedResponse{
		Success: true,
		Message: "Auth marked as used successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) MarkAuthsUsed(ctx context.Context, req *pb.MarkAuthsUsedRequest) (*pb.MarkAuthsUsedResponse, error) {
	var ids []int
	for _, id := range req.Ids {
		ids = append(ids, int(id))
	}

	cmd := commandauthcrawl.MarkAuthsUsed{
		IDs:    ids,
		UsedBy: req.UsedBy,
	}

	err := s.app.Commands.AuthCrawl.MarkAuthsUsed.Handle(ctx, cmd)
	if err != nil {
		return &pb.MarkAuthsUsedResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.MarkAuthsUsedResponse{
		Success: true,
		Message: "Auths marked as used successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) ReleaseAuth(ctx context.Context, req *pb.ReleaseAuthRequest) (*pb.ReleaseAuthResponse, error) {
	cmd := commandauthcrawl.ReleaseAuth{
		ID:         int(req.Id),
		ReleasedBy: req.ReleasedBy,
	}

	err := s.app.Commands.AuthCrawl.ReleaseAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.ReleaseAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ReleaseAuthResponse{
		Success: true,
		Message: "Auth released successfully",
	}, nil
}

func (s *AuthCrawlGRPCServer) ReleaseManyAuth(ctx context.Context, req *pb.ReleaseManyAuthRequest) (*pb.ReleaseManyAuthResponse, error) {
	var ids []int
	for _, id := range req.Ids {
		ids = append(ids, int(id))
	}

	cmd := commandauthcrawl.ReleaseManyAuth{
		IDs:        ids,
		ReleasedBy: req.ReleasedBy,
	}

	err := s.app.Commands.AuthCrawl.ReleaseManyAuth.Handle(ctx, cmd)
	if err != nil {
		return &pb.ReleaseManyAuthResponse{
			Success: false,
			Message: err.Error(),
		}, nil
	}

	return &pb.ReleaseManyAuthResponse{
		Success: true,
		Message: "Auths released successfully",
	}, nil
}

// Query handlers
func (s *AuthCrawlGRPCServer) GetAllAuthCrawl(ctx context.Context, req *pb.GetAllAuthCrawlRequest) (*pb.GetAllAuthCrawlResponse, error) {
	q := queryauthcral.AllAuthCrawlQuery{
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		Order:    req.Order,
	}

	result, err := s.app.Queries.AuthCrawl.GetAllAuthCrawl.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbAuths []*pb.AuthCrawl
	for _, auth := range result.Data {
		pbAuths = append(pbAuths, AuthCrawlToProto(auth))
	}

	return &pb.GetAllAuthCrawlResponse{
		Auths:      pbAuths,
		Total:      int32(result.Pagination.Total),
		Page:       int32(result.Pagination.Page),
		PageSize:   int32(result.Pagination.PageSize),
		TotalPages: int32(result.Pagination.Pages),
	}, nil
}

func (s *AuthCrawlGRPCServer) GetAuthCrawlById(ctx context.Context, req *pb.GetAuthCrawlByIdRequest) (*pb.GetAuthCrawlByIdResponse, error) {
	q := queryauthcral.GetAuthCrawlByIdQuery{
		Id: int(req.Id),
	}

	auth, err := s.app.Queries.AuthCrawl.GetAuthCrawlById.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetAuthCrawlByIdResponse{
		Auth: AuthCrawlToProto(auth),
	}, nil
}

func (s *AuthCrawlGRPCServer) GetAuthCrawlByMarketId(ctx context.Context, req *pb.GetAuthCrawlByMarketIdRequest) (*pb.GetAuthCrawlByMarketIdResponse, error) {
	q := queryauthcral.GetAuthCrawlByMarketIdQuery{
		MarketId: int(req.MarketId),
	}

	auth, err := s.app.Queries.AuthCrawl.GetAuthCrawlByMarketId.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	return &pb.GetAuthCrawlByMarketIdResponse{
		Auth: AuthCrawlToProto(auth),
	}, nil
}

func (s *AuthCrawlGRPCServer) GetAvailableAuthCrawlByMarket(ctx context.Context, req *pb.GetAvailableAuthCrawlByMarketRequest) (*pb.GetAvailableAuthCrawlByMarketResponse, error) {
	q := queryauthcral.GetAvailableAuthCrawlByMarketQuery{
		MarketId: int(req.MarketId),
		Limit:    int(req.Limit),
	}

	auths, err := s.app.Queries.AuthCrawl.GetAvailableAuthCrawlByMarket.Handle(ctx, q)
	if err != nil {
		return nil, err
	}

	var pbAuths []*pb.AuthCrawl
	for _, auth := range auths {
		pbAuths = append(pbAuths, AuthCrawlToProto(auth))
	}

	return &pb.GetAvailableAuthCrawlByMarketResponse{
		Auths: pbAuths,
	}, nil
}
