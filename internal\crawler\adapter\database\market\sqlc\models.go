// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type Market struct {
	ID               int                `db:"id" json:"id"`
	Name             string             `db:"name" json:"name"`
	DisplayName      string             `db:"display_name" json:"display_name"`
	MarketType       string             `db:"market_type" json:"market_type"`
	Status           string             `db:"status" json:"status"`
	BaseUrl          string             `db:"base_url" json:"base_url"`
	Currency         string             `db:"currency" json:"currency"`
	BuyerFeePercent  float64            `db:"buyer_fee_percent" json:"buyer_fee_percent"`
	SellerFeePercent float64            `db:"seller_fee_percent" json:"seller_fee_percent"`
	CountryCode      string             `db:"country_code" json:"country_code"`
	Language         string             `db:"language" json:"language"`
	Description      string             `db:"description" json:"description"`
	IsActive         bool               `db:"is_active" json:"is_active"`
	LastCrawlAt      pgtype.Timestamptz `db:"last_crawl_at" json:"last_crawl_at"`
	CreatedAt        pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt        pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}
