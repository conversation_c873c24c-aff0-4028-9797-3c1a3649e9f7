package buff163

import (
	"context"
	"encoding/json"
	"fmt"
	"go_core_market/internal/crawler/adapter/clienthttp"
	"go_core_market/internal/crawler/domain/value_object"
	"strconv"
	"sync"
	"time"
)

const defaultPageSize = "80"

type Buff163Crawler struct {
	client          clienthttp.IClient
	config          *value_object.Config
	lastRequestTime time.Time
	requestMutex    sync.Mutex
	cookieIndex     int
	apiKeyIndex     int
	proxyIndex      int
	headerIndex     int
	rotationMutex   sync.RWMutex
}

func NewBuff163Crawler(client clienthttp.IClient) *Buff163Crawler {
	crawler := &Buff163Crawler{client: client}
	crawler.setDefaultHeaders()
	return crawler
}

func (c *Buff163Crawler) setDefaultHeaders() {
	// Get current header set based on headerIndex
	currentHeaders := defaultHeadersSets[c.headerIndex%len(defaultHeadersSets)]
	for k, v := range currentHeaders {
		c.client.SetHeader(k, v)
	}
}

func (c *Buff163Crawler) SetConfig(config *value_object.Config) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}
	c.config = config
	return nil
}

func (c *Buff163Crawler) baseParams(page int) map[string]string {
	return map[string]string{
		"page_num":  strconv.Itoa(page),
		"page_size": defaultPageSize,
		"game":      "csgo",
		"appid":     "730",
		"sort_by":   "price.desc",
	}
}

// CrawlNormal implements the Crawler interface for normal price crawling
func (c *Buff163Crawler) CrawlNormal(ctx context.Context, page int) ([]*value_object.CrawledPrice, int, error) {
	params := c.baseParams(page)
	data, err := c.makeRequest(ctx, "/api/market/goods", params)
	if err != nil {
		return nil, 0, err
	}
	var response BuffItemResponse[PriceItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, 0, err
	}

	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, 0, err
	}
	if response.Data == nil {
		return nil, 0, fmt.Errorf("no data")
	}
	prices := make([]*value_object.CrawledPrice, 0, len(response.Data.Items))
	for _, item := range response.Data.Items {
		prices = append(prices, ItemToCrawledPrice(item, c.config.MarketID))
	}
	return prices, response.Data.TotalPage, nil
}

func (c *Buff163Crawler) CrawlItems(ctx context.Context, page int) ([]*value_object.CrawledItem, error) {
	params := c.baseParams(page)
	data, err := c.makeRequest(ctx, "/api/market/goods", params)
	if err != nil {
		return nil, err
	}
	var response BuffItemResponse[PriceItem]
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, err
	}
	if err := c.validateAPIResponse(response.Code, response.Message); err != nil {
		return nil, err
	}
	if response.Data == nil {
		return nil, fmt.Errorf("no data")
	}
	items := make([]*value_object.CrawledItem, 0, len(response.Data.Items))
	for _, item := range response.Data.Items {
		items = append(items, BuffItemToCrawledItem(item, c.config.MarketID))
	}
	return items, nil
}

// CrawlFloat implements the Crawler interface for Float-specific crawling
