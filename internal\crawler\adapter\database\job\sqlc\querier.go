// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"context"
)

type Querier interface {
	CountJobsByStatusQuery(ctx context.Context, status string) (int64, error)
	CountJobsQuery(ctx context.Context) (int64, error)
	Create(ctx context.Context, arg CreateParams) (int, error)
	CreateMany(ctx context.Context, arg []CreateManyParams) (int64, error)
	DeleteJob(ctx context.Context, id int) error
	FilterJobsQuery(ctx context.Context, arg FilterJobsQueryParams) ([]*Job, error)
	FindByID(ctx context.Context, id int) (*Job, error)
	// Query methods for JobQueryRepository
	GetAllJobsWithPagination(ctx context.Context, arg GetAllJobsWithPaginationParams) ([]*Job, error)
	GetExpiredJobsQuery(ctx context.Context) ([]*Job, error)
	GetJobByIdQuery(ctx context.Context, id int) (*Job, error)
	GetJobsByConfigIdQuery(ctx context.Context, crawlConfigID *int32) ([]*Job, error)
	GetJobsByStatusQuery(ctx context.Context, status string) ([]*Job, error)
	GetJobsByTypeQuery(ctx context.Context, jobType string) ([]*Job, error)
	GetJobsNeedingRetryQuery(ctx context.Context) ([]*Job, error)
	GetPendingJobsQuery(ctx context.Context) ([]*Job, error)
	GetRunningJobsQuery(ctx context.Context) ([]*Job, error)
	IncrementRetryCount(ctx context.Context, arg IncrementRetryCountParams) (*Job, error)
	UpdateJob(ctx context.Context, arg UpdateJobParams) (*Job, error)
	UpdateJobProgress(ctx context.Context, arg UpdateJobProgressParams) (*Job, error)
	UpdateJobStatus(ctx context.Context, arg UpdateJobStatusParams) (*Job, error)
}

var _ Querier = (*Queries)(nil)
