package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type MarkProxyUsed struct {
	ID     int
	UsedBy string
}

type MarkProxyUsedHandler decorator.CommandHandler[MarkProxyUsed]

type markProxyUsedHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewMarkProxyUsedHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) MarkProxyUsedHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[MarkProxyUsed](
		markProxyUsedHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h markProxyUsedHandler) Handle(ctx context.Context, cmd MarkProxyUsed) error {
	return h.repo.Update(ctx, cmd.ID,
		func(p *entity.Proxy) (*entity.Proxy, error) {
			err := p.MarkUsed()
			if err != nil {
				return nil, err
			}
			return p, nil
		})
}

type MarkProxiesUsed struct {
	IDs    []int
	UsedBy string
}

type MarkProxiesUsedHandler decorator.CommandHandler[MarkProxiesUsed]

type markProxiesUsedHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewMarkProxiesUsedHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) MarkProxiesUsedHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[MarkProxiesUsed](
		markProxiesUsedHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h markProxiesUsedHandler) Handle(ctx context.Context, cmd MarkProxiesUsed) error {
	return h.repo.UpdateMany(ctx, cmd.IDs,
		func(p []*entity.Proxy) ([]*entity.Proxy, error) {
			for _, v := range p {
				err := v.MarkUsed()
				if err != nil {
					return nil, err
				}
			}
			return p, nil
		})
}
