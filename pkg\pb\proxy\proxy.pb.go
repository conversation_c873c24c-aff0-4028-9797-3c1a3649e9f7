// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: api/protobuf/proxy.proto

package proxy

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Proxy message
type Proxy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Host          string                 `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Port          int32                  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	UserName      string                 `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password      string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	IsActive      bool                   `protobuf:"varint,6,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsUse         bool                   `protobuf:"varint,7,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	LastUsedAt    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=last_used_at,json=lastUsedAt,proto3" json:"last_used_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Proxy) Reset() {
	*x = Proxy{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Proxy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Proxy) ProtoMessage() {}

func (x *Proxy) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Proxy.ProtoReflect.Descriptor instead.
func (*Proxy) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{0}
}

func (x *Proxy) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Proxy) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *Proxy) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Proxy) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *Proxy) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Proxy) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Proxy) GetIsUse() bool {
	if x != nil {
		return x.IsUse
	}
	return false
}

func (x *Proxy) GetLastUsedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUsedAt
	}
	return nil
}

func (x *Proxy) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Proxy) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response messages for Commands
type CreateProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	UserName      string                 `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateProxyRequest) Reset() {
	*x = CreateProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateProxyRequest) ProtoMessage() {}

func (x *CreateProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateProxyRequest.ProtoReflect.Descriptor instead.
func (*CreateProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{1}
}

func (x *CreateProxyRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *CreateProxyRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *CreateProxyRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *CreateProxyRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *CreateProxyRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreateProxyRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateProxyResponse) Reset() {
	*x = CreateProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateProxyResponse) ProtoMessage() {}

func (x *CreateProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateProxyResponse.ProtoReflect.Descriptor instead.
func (*CreateProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{2}
}

func (x *CreateProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateProxyBatchRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxies       []*CreateProxyRequest  `protobuf:"bytes,1,rep,name=proxies,proto3" json:"proxies,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,2,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateProxyBatchRequest) Reset() {
	*x = CreateProxyBatchRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateProxyBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateProxyBatchRequest) ProtoMessage() {}

func (x *CreateProxyBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateProxyBatchRequest.ProtoReflect.Descriptor instead.
func (*CreateProxyBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{3}
}

func (x *CreateProxyBatchRequest) GetProxies() []*CreateProxyRequest {
	if x != nil {
		return x.Proxies
	}
	return nil
}

func (x *CreateProxyBatchRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateProxyBatchResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateProxyBatchResponse) Reset() {
	*x = CreateProxyBatchResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateProxyBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateProxyBatchResponse) ProtoMessage() {}

func (x *CreateProxyBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateProxyBatchResponse.ProtoReflect.Descriptor instead.
func (*CreateProxyBatchResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{4}
}

func (x *CreateProxyBatchResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateProxyBatchResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Host          string                 `protobuf:"bytes,2,opt,name=host,proto3" json:"host,omitempty"`
	Port          int32                  `protobuf:"varint,3,opt,name=port,proto3" json:"port,omitempty"`
	UserName      string                 `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name,omitempty"`
	Password      string                 `protobuf:"bytes,5,opt,name=password,proto3" json:"password,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,6,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProxyRequest) Reset() {
	*x = UpdateProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProxyRequest) ProtoMessage() {}

func (x *UpdateProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProxyRequest.ProtoReflect.Descriptor instead.
func (*UpdateProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateProxyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateProxyRequest) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *UpdateProxyRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *UpdateProxyRequest) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UpdateProxyRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UpdateProxyRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateProxyResponse) Reset() {
	*x = UpdateProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateProxyResponse) ProtoMessage() {}

func (x *UpdateProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateProxyResponse.ProtoReflect.Descriptor instead.
func (*UpdateProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     string                 `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteProxyRequest) Reset() {
	*x = DeleteProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProxyRequest) ProtoMessage() {}

func (x *DeleteProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProxyRequest.ProtoReflect.Descriptor instead.
func (*DeleteProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteProxyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteProxyRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

type DeleteProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteProxyResponse) Reset() {
	*x = DeleteProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteProxyResponse) ProtoMessage() {}

func (x *DeleteProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteProxyResponse.ProtoReflect.Descriptor instead.
func (*DeleteProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ActivateProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ActivatedBy   string                 `protobuf:"bytes,2,opt,name=activated_by,json=activatedBy,proto3" json:"activated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateProxyRequest) Reset() {
	*x = ActivateProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateProxyRequest) ProtoMessage() {}

func (x *ActivateProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateProxyRequest.ProtoReflect.Descriptor instead.
func (*ActivateProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{9}
}

func (x *ActivateProxyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivateProxyRequest) GetActivatedBy() string {
	if x != nil {
		return x.ActivatedBy
	}
	return ""
}

type ActivateProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateProxyResponse) Reset() {
	*x = ActivateProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateProxyResponse) ProtoMessage() {}

func (x *ActivateProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateProxyResponse.ProtoReflect.Descriptor instead.
func (*ActivateProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{10}
}

func (x *ActivateProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ActivateProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeactivateProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeactivatedBy string                 `protobuf:"bytes,2,opt,name=deactivated_by,json=deactivatedBy,proto3" json:"deactivated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateProxyRequest) Reset() {
	*x = DeactivateProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateProxyRequest) ProtoMessage() {}

func (x *DeactivateProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateProxyRequest.ProtoReflect.Descriptor instead.
func (*DeactivateProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{11}
}

func (x *DeactivateProxyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeactivateProxyRequest) GetDeactivatedBy() string {
	if x != nil {
		return x.DeactivatedBy
	}
	return ""
}

type DeactivateProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateProxyResponse) Reset() {
	*x = DeactivateProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateProxyResponse) ProtoMessage() {}

func (x *DeactivateProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateProxyResponse.ProtoReflect.Descriptor instead.
func (*DeactivateProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{12}
}

func (x *DeactivateProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeactivateProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MarkProxyUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UsedBy        string                 `protobuf:"bytes,2,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkProxyUsedRequest) Reset() {
	*x = MarkProxyUsedRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkProxyUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkProxyUsedRequest) ProtoMessage() {}

func (x *MarkProxyUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkProxyUsedRequest.ProtoReflect.Descriptor instead.
func (*MarkProxyUsedRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{13}
}

func (x *MarkProxyUsedRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarkProxyUsedRequest) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

type MarkProxyUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkProxyUsedResponse) Reset() {
	*x = MarkProxyUsedResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkProxyUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkProxyUsedResponse) ProtoMessage() {}

func (x *MarkProxyUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkProxyUsedResponse.ProtoReflect.Descriptor instead.
func (*MarkProxyUsedResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{14}
}

func (x *MarkProxyUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkProxyUsedResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MarkProxiesUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []int32                `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	UsedBy        string                 `protobuf:"bytes,2,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkProxiesUsedRequest) Reset() {
	*x = MarkProxiesUsedRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkProxiesUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkProxiesUsedRequest) ProtoMessage() {}

func (x *MarkProxiesUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkProxiesUsedRequest.ProtoReflect.Descriptor instead.
func (*MarkProxiesUsedRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{15}
}

func (x *MarkProxiesUsedRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MarkProxiesUsedRequest) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

type MarkProxiesUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkProxiesUsedResponse) Reset() {
	*x = MarkProxiesUsedResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkProxiesUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkProxiesUsedResponse) ProtoMessage() {}

func (x *MarkProxiesUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkProxiesUsedResponse.ProtoReflect.Descriptor instead.
func (*MarkProxiesUsedResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{16}
}

func (x *MarkProxiesUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkProxiesUsedResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReleaseProxyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ReleasedBy    string                 `protobuf:"bytes,2,opt,name=released_by,json=releasedBy,proto3" json:"released_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseProxyRequest) Reset() {
	*x = ReleaseProxyRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseProxyRequest) ProtoMessage() {}

func (x *ReleaseProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseProxyRequest.ProtoReflect.Descriptor instead.
func (*ReleaseProxyRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{17}
}

func (x *ReleaseProxyRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReleaseProxyRequest) GetReleasedBy() string {
	if x != nil {
		return x.ReleasedBy
	}
	return ""
}

type ReleaseProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseProxyResponse) Reset() {
	*x = ReleaseProxyResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseProxyResponse) ProtoMessage() {}

func (x *ReleaseProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseProxyResponse.ProtoReflect.Descriptor instead.
func (*ReleaseProxyResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{18}
}

func (x *ReleaseProxyResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReleaseProxyResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReleaseManyProxiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []int32                `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	ReleasedBy    string                 `protobuf:"bytes,2,opt,name=released_by,json=releasedBy,proto3" json:"released_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseManyProxiesRequest) Reset() {
	*x = ReleaseManyProxiesRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseManyProxiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseManyProxiesRequest) ProtoMessage() {}

func (x *ReleaseManyProxiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseManyProxiesRequest.ProtoReflect.Descriptor instead.
func (*ReleaseManyProxiesRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{19}
}

func (x *ReleaseManyProxiesRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReleaseManyProxiesRequest) GetReleasedBy() string {
	if x != nil {
		return x.ReleasedBy
	}
	return ""
}

type ReleaseManyProxiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseManyProxiesResponse) Reset() {
	*x = ReleaseManyProxiesResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseManyProxiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseManyProxiesResponse) ProtoMessage() {}

func (x *ReleaseManyProxiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseManyProxiesResponse.ProtoReflect.Descriptor instead.
func (*ReleaseManyProxiesResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{20}
}

func (x *ReleaseManyProxiesResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReleaseManyProxiesResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Request/Response messages for Queries
type GetAllProxiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	OrderBy       string                 `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllProxiesRequest) Reset() {
	*x = GetAllProxiesRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllProxiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllProxiesRequest) ProtoMessage() {}

func (x *GetAllProxiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllProxiesRequest.ProtoReflect.Descriptor instead.
func (*GetAllProxiesRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{21}
}

func (x *GetAllProxiesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllProxiesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllProxiesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

type GetAllProxiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxies       []*Proxy               `protobuf:"bytes,1,rep,name=proxies,proto3" json:"proxies,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalPages    int32                  `protobuf:"varint,5,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllProxiesResponse) Reset() {
	*x = GetAllProxiesResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllProxiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllProxiesResponse) ProtoMessage() {}

func (x *GetAllProxiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllProxiesResponse.ProtoReflect.Descriptor instead.
func (*GetAllProxiesResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{22}
}

func (x *GetAllProxiesResponse) GetProxies() []*Proxy {
	if x != nil {
		return x.Proxies
	}
	return nil
}

func (x *GetAllProxiesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAllProxiesResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllProxiesResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllProxiesResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

type GetProxyByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProxyByIdRequest) Reset() {
	*x = GetProxyByIdRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProxyByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProxyByIdRequest) ProtoMessage() {}

func (x *GetProxyByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProxyByIdRequest.ProtoReflect.Descriptor instead.
func (*GetProxyByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{23}
}

func (x *GetProxyByIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetProxyByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxy         *Proxy                 `protobuf:"bytes,1,opt,name=proxy,proto3" json:"proxy,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetProxyByIdResponse) Reset() {
	*x = GetProxyByIdResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetProxyByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetProxyByIdResponse) ProtoMessage() {}

func (x *GetProxyByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetProxyByIdResponse.ProtoReflect.Descriptor instead.
func (*GetProxyByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{24}
}

func (x *GetProxyByIdResponse) GetProxy() *Proxy {
	if x != nil {
		return x.Proxy
	}
	return nil
}

type GetAvailableProxiesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Limit         int32                  `protobuf:"varint,1,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableProxiesRequest) Reset() {
	*x = GetAvailableProxiesRequest{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableProxiesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableProxiesRequest) ProtoMessage() {}

func (x *GetAvailableProxiesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableProxiesRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableProxiesRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{25}
}

func (x *GetAvailableProxiesRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetAvailableProxiesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Proxies       []*Proxy               `protobuf:"bytes,1,rep,name=proxies,proto3" json:"proxies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableProxiesResponse) Reset() {
	*x = GetAvailableProxiesResponse{}
	mi := &file_api_protobuf_proxy_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableProxiesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableProxiesResponse) ProtoMessage() {}

func (x *GetAvailableProxiesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_proxy_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableProxiesResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableProxiesResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_proxy_proto_rawDescGZIP(), []int{26}
}

func (x *GetAvailableProxiesResponse) GetProxies() []*Proxy {
	if x != nil {
		return x.Proxies
	}
	return nil
}

var File_api_protobuf_proxy_proto protoreflect.FileDescriptor

const file_api_protobuf_proxy_proto_rawDesc = "" +
	"\n" +
	"\x18api/protobuf/proxy.proto\x12\bproxy.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xe0\x02\n" +
	"\x05Proxy\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04host\x18\x02 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x03 \x01(\x05R\x04port\x12\x1b\n" +
	"\tuser_name\x18\x04 \x01(\tR\buserName\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12\x1b\n" +
	"\tis_active\x18\x06 \x01(\bR\bisActive\x12\x15\n" +
	"\x06is_use\x18\a \x01(\bR\x05isUse\x12<\n" +
	"\flast_used_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastUsedAt\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xb1\x01\n" +
	"\x12CreateProxyRequest\x12\x12\n" +
	"\x04host\x18\x01 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x1b\n" +
	"\tuser_name\x18\x03 \x01(\tR\buserName\x12\x1a\n" +
	"\bpassword\x18\x04 \x01(\tR\bpassword\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x12\x1d\n" +
	"\n" +
	"created_by\x18\x06 \x01(\tR\tcreatedBy\"I\n" +
	"\x13CreateProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"p\n" +
	"\x17CreateProxyBatchRequest\x126\n" +
	"\aproxies\x18\x01 \x03(\v2\x1c.proxy.v1.CreateProxyRequestR\aproxies\x12\x1d\n" +
	"\n" +
	"created_by\x18\x02 \x01(\tR\tcreatedBy\"N\n" +
	"\x18CreateProxyBatchResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xa4\x01\n" +
	"\x12UpdateProxyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x12\n" +
	"\x04host\x18\x02 \x01(\tR\x04host\x12\x12\n" +
	"\x04port\x18\x03 \x01(\x05R\x04port\x12\x1b\n" +
	"\tuser_name\x18\x04 \x01(\tR\buserName\x12\x1a\n" +
	"\bpassword\x18\x05 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x06 \x01(\tR\tupdatedBy\"I\n" +
	"\x13UpdateProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"C\n" +
	"\x12DeleteProxyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\tR\tdeletedBy\"I\n" +
	"\x13DeleteProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"I\n" +
	"\x14ActivateProxyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\factivated_by\x18\x02 \x01(\tR\vactivatedBy\"K\n" +
	"\x15ActivateProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"O\n" +
	"\x16DeactivateProxyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12%\n" +
	"\x0edeactivated_by\x18\x02 \x01(\tR\rdeactivatedBy\"M\n" +
	"\x17DeactivateProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"?\n" +
	"\x14MarkProxyUsedRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x17\n" +
	"\aused_by\x18\x02 \x01(\tR\x06usedBy\"K\n" +
	"\x15MarkProxyUsedResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"C\n" +
	"\x16MarkProxiesUsedRequest\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x05R\x03ids\x12\x17\n" +
	"\aused_by\x18\x02 \x01(\tR\x06usedBy\"M\n" +
	"\x17MarkProxiesUsedResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"F\n" +
	"\x13ReleaseProxyRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1f\n" +
	"\vreleased_by\x18\x02 \x01(\tR\n" +
	"releasedBy\"J\n" +
	"\x14ReleaseProxyResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"N\n" +
	"\x19ReleaseManyProxiesRequest\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x05R\x03ids\x12\x1f\n" +
	"\vreleased_by\x18\x02 \x01(\tR\n" +
	"releasedBy\"P\n" +
	"\x1aReleaseManyProxiesResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"b\n" +
	"\x14GetAllProxiesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x19\n" +
	"\border_by\x18\x03 \x01(\tR\aorderBy\"\xaa\x01\n" +
	"\x15GetAllProxiesResponse\x12)\n" +
	"\aproxies\x18\x01 \x03(\v2\x0f.proxy.v1.ProxyR\aproxies\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_pages\x18\x05 \x01(\x05R\n" +
	"totalPages\"%\n" +
	"\x13GetProxyByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"=\n" +
	"\x14GetProxyByIdResponse\x12%\n" +
	"\x05proxy\x18\x01 \x01(\v2\x0f.proxy.v1.ProxyR\x05proxy\"2\n" +
	"\x1aGetAvailableProxiesRequest\x12\x14\n" +
	"\x05limit\x18\x01 \x01(\x05R\x05limit\"H\n" +
	"\x1bGetAvailableProxiesResponse\x12)\n" +
	"\aproxies\x18\x01 \x03(\v2\x0f.proxy.v1.ProxyR\aproxies2\xd6\b\n" +
	"\fProxyService\x12J\n" +
	"\vCreateProxy\x12\x1c.proxy.v1.CreateProxyRequest\x1a\x1d.proxy.v1.CreateProxyResponse\x12Y\n" +
	"\x10CreateProxyBatch\x12!.proxy.v1.CreateProxyBatchRequest\x1a\".proxy.v1.CreateProxyBatchResponse\x12J\n" +
	"\vUpdateProxy\x12\x1c.proxy.v1.UpdateProxyRequest\x1a\x1d.proxy.v1.UpdateProxyResponse\x12J\n" +
	"\vDeleteProxy\x12\x1c.proxy.v1.DeleteProxyRequest\x1a\x1d.proxy.v1.DeleteProxyResponse\x12P\n" +
	"\rActivateProxy\x12\x1e.proxy.v1.ActivateProxyRequest\x1a\x1f.proxy.v1.ActivateProxyResponse\x12V\n" +
	"\x0fDeactivateProxy\x12 .proxy.v1.DeactivateProxyRequest\x1a!.proxy.v1.DeactivateProxyResponse\x12P\n" +
	"\rMarkProxyUsed\x12\x1e.proxy.v1.MarkProxyUsedRequest\x1a\x1f.proxy.v1.MarkProxyUsedResponse\x12V\n" +
	"\x0fMarkProxiesUsed\x12 .proxy.v1.MarkProxiesUsedRequest\x1a!.proxy.v1.MarkProxiesUsedResponse\x12M\n" +
	"\fReleaseProxy\x12\x1d.proxy.v1.ReleaseProxyRequest\x1a\x1e.proxy.v1.ReleaseProxyResponse\x12_\n" +
	"\x12ReleaseManyProxies\x12#.proxy.v1.ReleaseManyProxiesRequest\x1a$.proxy.v1.ReleaseManyProxiesResponse\x12P\n" +
	"\rGetAllProxies\x12\x1e.proxy.v1.GetAllProxiesRequest\x1a\x1f.proxy.v1.GetAllProxiesResponse\x12M\n" +
	"\fGetProxyById\x12\x1d.proxy.v1.GetProxyByIdRequest\x1a\x1e.proxy.v1.GetProxyByIdResponse\x12b\n" +
	"\x13GetAvailableProxies\x12$.proxy.v1.GetAvailableProxiesRequest\x1a%.proxy.v1.GetAvailableProxiesResponseB\x1dZ\x1bgo_core_market/pkg/pb/proxyb\x06proto3"

var (
	file_api_protobuf_proxy_proto_rawDescOnce sync.Once
	file_api_protobuf_proxy_proto_rawDescData []byte
)

func file_api_protobuf_proxy_proto_rawDescGZIP() []byte {
	file_api_protobuf_proxy_proto_rawDescOnce.Do(func() {
		file_api_protobuf_proxy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protobuf_proxy_proto_rawDesc), len(file_api_protobuf_proxy_proto_rawDesc)))
	})
	return file_api_protobuf_proxy_proto_rawDescData
}

var file_api_protobuf_proxy_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_api_protobuf_proxy_proto_goTypes = []any{
	(*Proxy)(nil),                       // 0: proxy.v1.Proxy
	(*CreateProxyRequest)(nil),          // 1: proxy.v1.CreateProxyRequest
	(*CreateProxyResponse)(nil),         // 2: proxy.v1.CreateProxyResponse
	(*CreateProxyBatchRequest)(nil),     // 3: proxy.v1.CreateProxyBatchRequest
	(*CreateProxyBatchResponse)(nil),    // 4: proxy.v1.CreateProxyBatchResponse
	(*UpdateProxyRequest)(nil),          // 5: proxy.v1.UpdateProxyRequest
	(*UpdateProxyResponse)(nil),         // 6: proxy.v1.UpdateProxyResponse
	(*DeleteProxyRequest)(nil),          // 7: proxy.v1.DeleteProxyRequest
	(*DeleteProxyResponse)(nil),         // 8: proxy.v1.DeleteProxyResponse
	(*ActivateProxyRequest)(nil),        // 9: proxy.v1.ActivateProxyRequest
	(*ActivateProxyResponse)(nil),       // 10: proxy.v1.ActivateProxyResponse
	(*DeactivateProxyRequest)(nil),      // 11: proxy.v1.DeactivateProxyRequest
	(*DeactivateProxyResponse)(nil),     // 12: proxy.v1.DeactivateProxyResponse
	(*MarkProxyUsedRequest)(nil),        // 13: proxy.v1.MarkProxyUsedRequest
	(*MarkProxyUsedResponse)(nil),       // 14: proxy.v1.MarkProxyUsedResponse
	(*MarkProxiesUsedRequest)(nil),      // 15: proxy.v1.MarkProxiesUsedRequest
	(*MarkProxiesUsedResponse)(nil),     // 16: proxy.v1.MarkProxiesUsedResponse
	(*ReleaseProxyRequest)(nil),         // 17: proxy.v1.ReleaseProxyRequest
	(*ReleaseProxyResponse)(nil),        // 18: proxy.v1.ReleaseProxyResponse
	(*ReleaseManyProxiesRequest)(nil),   // 19: proxy.v1.ReleaseManyProxiesRequest
	(*ReleaseManyProxiesResponse)(nil),  // 20: proxy.v1.ReleaseManyProxiesResponse
	(*GetAllProxiesRequest)(nil),        // 21: proxy.v1.GetAllProxiesRequest
	(*GetAllProxiesResponse)(nil),       // 22: proxy.v1.GetAllProxiesResponse
	(*GetProxyByIdRequest)(nil),         // 23: proxy.v1.GetProxyByIdRequest
	(*GetProxyByIdResponse)(nil),        // 24: proxy.v1.GetProxyByIdResponse
	(*GetAvailableProxiesRequest)(nil),  // 25: proxy.v1.GetAvailableProxiesRequest
	(*GetAvailableProxiesResponse)(nil), // 26: proxy.v1.GetAvailableProxiesResponse
	(*timestamppb.Timestamp)(nil),       // 27: google.protobuf.Timestamp
}
var file_api_protobuf_proxy_proto_depIdxs = []int32{
	27, // 0: proxy.v1.Proxy.last_used_at:type_name -> google.protobuf.Timestamp
	27, // 1: proxy.v1.Proxy.created_at:type_name -> google.protobuf.Timestamp
	27, // 2: proxy.v1.Proxy.updated_at:type_name -> google.protobuf.Timestamp
	1,  // 3: proxy.v1.CreateProxyBatchRequest.proxies:type_name -> proxy.v1.CreateProxyRequest
	0,  // 4: proxy.v1.GetAllProxiesResponse.proxies:type_name -> proxy.v1.Proxy
	0,  // 5: proxy.v1.GetProxyByIdResponse.proxy:type_name -> proxy.v1.Proxy
	0,  // 6: proxy.v1.GetAvailableProxiesResponse.proxies:type_name -> proxy.v1.Proxy
	1,  // 7: proxy.v1.ProxyService.CreateProxy:input_type -> proxy.v1.CreateProxyRequest
	3,  // 8: proxy.v1.ProxyService.CreateProxyBatch:input_type -> proxy.v1.CreateProxyBatchRequest
	5,  // 9: proxy.v1.ProxyService.UpdateProxy:input_type -> proxy.v1.UpdateProxyRequest
	7,  // 10: proxy.v1.ProxyService.DeleteProxy:input_type -> proxy.v1.DeleteProxyRequest
	9,  // 11: proxy.v1.ProxyService.ActivateProxy:input_type -> proxy.v1.ActivateProxyRequest
	11, // 12: proxy.v1.ProxyService.DeactivateProxy:input_type -> proxy.v1.DeactivateProxyRequest
	13, // 13: proxy.v1.ProxyService.MarkProxyUsed:input_type -> proxy.v1.MarkProxyUsedRequest
	15, // 14: proxy.v1.ProxyService.MarkProxiesUsed:input_type -> proxy.v1.MarkProxiesUsedRequest
	17, // 15: proxy.v1.ProxyService.ReleaseProxy:input_type -> proxy.v1.ReleaseProxyRequest
	19, // 16: proxy.v1.ProxyService.ReleaseManyProxies:input_type -> proxy.v1.ReleaseManyProxiesRequest
	21, // 17: proxy.v1.ProxyService.GetAllProxies:input_type -> proxy.v1.GetAllProxiesRequest
	23, // 18: proxy.v1.ProxyService.GetProxyById:input_type -> proxy.v1.GetProxyByIdRequest
	25, // 19: proxy.v1.ProxyService.GetAvailableProxies:input_type -> proxy.v1.GetAvailableProxiesRequest
	2,  // 20: proxy.v1.ProxyService.CreateProxy:output_type -> proxy.v1.CreateProxyResponse
	4,  // 21: proxy.v1.ProxyService.CreateProxyBatch:output_type -> proxy.v1.CreateProxyBatchResponse
	6,  // 22: proxy.v1.ProxyService.UpdateProxy:output_type -> proxy.v1.UpdateProxyResponse
	8,  // 23: proxy.v1.ProxyService.DeleteProxy:output_type -> proxy.v1.DeleteProxyResponse
	10, // 24: proxy.v1.ProxyService.ActivateProxy:output_type -> proxy.v1.ActivateProxyResponse
	12, // 25: proxy.v1.ProxyService.DeactivateProxy:output_type -> proxy.v1.DeactivateProxyResponse
	14, // 26: proxy.v1.ProxyService.MarkProxyUsed:output_type -> proxy.v1.MarkProxyUsedResponse
	16, // 27: proxy.v1.ProxyService.MarkProxiesUsed:output_type -> proxy.v1.MarkProxiesUsedResponse
	18, // 28: proxy.v1.ProxyService.ReleaseProxy:output_type -> proxy.v1.ReleaseProxyResponse
	20, // 29: proxy.v1.ProxyService.ReleaseManyProxies:output_type -> proxy.v1.ReleaseManyProxiesResponse
	22, // 30: proxy.v1.ProxyService.GetAllProxies:output_type -> proxy.v1.GetAllProxiesResponse
	24, // 31: proxy.v1.ProxyService.GetProxyById:output_type -> proxy.v1.GetProxyByIdResponse
	26, // 32: proxy.v1.ProxyService.GetAvailableProxies:output_type -> proxy.v1.GetAvailableProxiesResponse
	20, // [20:33] is the sub-list for method output_type
	7,  // [7:20] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_api_protobuf_proxy_proto_init() }
func file_api_protobuf_proxy_proto_init() {
	if File_api_protobuf_proxy_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protobuf_proxy_proto_rawDesc), len(file_api_protobuf_proxy_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protobuf_proxy_proto_goTypes,
		DependencyIndexes: file_api_protobuf_proxy_proto_depIdxs,
		MessageInfos:      file_api_protobuf_proxy_proto_msgTypes,
	}.Build()
	File_api_protobuf_proxy_proto = out.File
	file_api_protobuf_proxy_proto_goTypes = nil
	file_api_protobuf_proxy_proto_depIdxs = nil
}
