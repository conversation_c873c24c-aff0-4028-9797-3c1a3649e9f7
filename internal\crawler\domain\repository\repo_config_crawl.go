package repository

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
)

type RepositoryConfigCrawl interface {
	Create(ctx context.Context, config *entity.CrawlConfig) error
	CreateMany(ctx context.Context, configs []*entity.CrawlConfig) error
	Update(
		ctx context.Context,
		id int,
		updateFn func(c *entity.CrawlConfig) (*entity.CrawlConfig, error),
	) error
	Delete(ctx context.Context, id int) error
	DeleteMany(ctx context.Context, ids []int) error
}
