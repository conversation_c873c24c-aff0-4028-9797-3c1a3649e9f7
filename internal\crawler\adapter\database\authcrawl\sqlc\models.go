// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package sqlc

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type AuthCrawl struct {
	ID         int                `db:"id" json:"id"`
	MarketID   int                `db:"market_id" json:"market_id"`
	AuthType   string             `db:"auth_type" json:"auth_type"`
	Value      string             `db:"value" json:"value"`
	IsActive   bool               `db:"is_active" json:"is_active"`
	IsUse      bool               `db:"is_use" json:"is_use"`
	LastUsedAt pgtype.Timestamptz `db:"last_used_at" json:"last_used_at"`
	ExpiredAt  pgtype.Timestamptz `db:"expired_at" json:"expired_at"`
	CreatedAt  pgtype.Timestamptz `db:"created_at" json:"created_at"`
	UpdatedAt  pgtype.Timestamptz `db:"updated_at" json:"updated_at"`
}
