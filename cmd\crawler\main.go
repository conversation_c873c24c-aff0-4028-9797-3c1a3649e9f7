package main

import (
	"context"
	grpc2 "go_core_market/internal/crawler/port/grpc/authcrawl"
	proxygrpc "go_core_market/internal/crawler/port/grpc/proxy"
	"go_core_market/pkg/pb/auth_crawl"
	proxypb "go_core_market/pkg/pb/proxy"
	"log"

	crawlerapp "go_core_market/internal/crawler/app"
	marketgrpc "go_core_market/internal/crawler/port/grpc/market"
	"go_core_market/internal/crawler/service"
	"go_core_market/pkg/config"
	"go_core_market/pkg/logger"
	marketpb "go_core_market/pkg/pb/market"
	"go_core_market/pkg/server"
	"google.golang.org/grpc"
)

type CrawlerServiceRegistrar struct {
	application *crawlerapp.Application
}

// NewCrawlerServiceRegistrar creates a new crawler service registrar
func NewCrawlerServiceRegistrar(app *crawlerapp.Application) *CrawlerServiceRegistrar {
	return &CrawlerServiceRegistrar{
		application: app,
	}
}

// RegisterServices registers all crawler gRPC services
func (r *CrawlerServiceRegistrar) RegisterServices(grpcServer *grpc.Server) {
	// Register Market service
	marketServer := marketgrpc.NewMarketGRPCServer(*r.application)
	marketpb.RegisterMarketServiceServer(grpcServer, marketServer)

	// Register Proxy service
	proxyServer := proxygrpc.NewProxyGRPCServer(*r.application)
	proxypb.RegisterProxyServiceServer(grpcServer, proxyServer)

	// Register AuthCrawl service
	authCrawlServer := grpc2.NewAuthCrawlGRPCServer(*r.application)
	auth_crawl.RegisterAuthCrawlServiceServer(grpcServer, authCrawlServer)
}

// GetServiceName returns the service name
func (r *CrawlerServiceRegistrar) GetServiceName() string {
	return "crawler"
}

func main() {
	ctx := context.Background()

	// Load configuration
	cfg, err := config.LoadCustomConfig[config.BaseServiceConfig]("cmd/crawler/config.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logger
	logger, err := logger.NewZapLoggerWithConfig(cfg.Logger)
	if err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger.Info("Starting Crawler Service...")
	logger.Info("Configuration loaded", "config", cfg)

	// Create and initialize application
	app := service.NewCrawlerApplicationBuilder(ctx, *cfg, logger).
		WithDatabase().
		WithBroker().
		WithMetrics().
		WithRepository().
		Build()

	// Create server configuration
	serverConfig := server.Config{
		Host:   cfg.Server.Host,
		Port:   cfg.Server.Port,
		APIKey: cfg.Server.APIKey,
	}

	// Create service registrar
	registrar := NewCrawlerServiceRegistrar(app)

	// Create register function for gRPC server
	registerServer := func(grpcServer *grpc.Server) {
		registrar.RegisterServices(grpcServer)
		logger.Info("Crawler services registered successfully")
	}

	// Start gRPC server
	if err := server.RunGRPCServer(registerServer, logger, serverConfig); err != nil {
		logger.Fatal("Failed to start gRPC server", "error", err)
		panic(err)
	}
}
