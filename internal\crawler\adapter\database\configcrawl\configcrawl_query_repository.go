package repository

import (
	"context"
	"fmt"
	"go_core_market/internal/crawler/adapter/database/configcrawl/sqlc"
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	"go_core_market/pkg/database"
)

type configCrawlQueryRepository struct {
	db      database.DBTX
	queries sqlc.Querier
}

// NewConfigCrawlQueryRepository creates a new crawl config query repository
func NewConfigCrawlQueryRepository(db database.DBTX) querycrawlconfig.CrawlConfigReadModel {
	return &configCrawlQueryRepository{
		db:      db,
		queries: sqlc.New(db),
	}
}

// GetAllCrawlConfigs retrieves all crawl configs with pagination and ordering
func (r *configCrawlQueryRepository) GetAllCrawlConfigs(ctx context.Context, q querycrawlconfig.GetAllCrawlConfigsQuery) ([]*querycrawlconfig.CrawlConfig, error) {
	// Calculate offset from page and page size
	offset := (q.Page - 1) * q.PageSize

	params := sqlc.GetAllCrawlConfigsWithPaginationParams{
		Limit:   int32(q.PageSize),
		Offset:  int32(offset),
		Column3: q.OrderBy,
	}

	sqlcConfigs, err := r.queries.GetAllCrawlConfigsWithPagination(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get all crawl configs: %w", err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// GetCrawlConfigById retrieves a single crawl config by ID
func (r *configCrawlQueryRepository) GetCrawlConfigById(ctx context.Context, id int) (*querycrawlconfig.CrawlConfig, error) {
	sqlcConfig, err := r.queries.GetCrawlConfigByIdQuery(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl config by id %d: %w", id, err)
	}

	config, err := FromSQLCModelToQueryModel(sqlcConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
	}

	return config, nil
}

// GetActiveCrawlConfigs retrieves all active crawl configs
func (r *configCrawlQueryRepository) GetActiveCrawlConfigs(ctx context.Context) ([]*querycrawlconfig.CrawlConfig, error) {
	sqlcConfigs, err := r.queries.GetActiveCrawlConfigsQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active crawl configs: %w", err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// GetCrawlConfigsByMarketId retrieves crawl configs by market ID
func (r *configCrawlQueryRepository) GetCrawlConfigsByMarketId(ctx context.Context, marketId int) ([]*querycrawlconfig.CrawlConfig, error) {
	sqlcConfigs, err := r.queries.GetCrawlConfigsByMarketIdQuery(ctx, marketId)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl configs by market id %d: %w", marketId, err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// GetActiveCrawlConfigsByMarket retrieves active crawl configs by market ID
func (r *configCrawlQueryRepository) GetActiveCrawlConfigsByMarket(ctx context.Context, marketId int) ([]*querycrawlconfig.CrawlConfig, error) {
	sqlcConfigs, err := r.queries.GetActiveCrawlConfigsByMarketQuery(ctx, marketId)
	if err != nil {
		return nil, fmt.Errorf("failed to get active crawl configs by market id %d: %w", marketId, err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// GetActiveCrawlConfigsByType retrieves active crawl configs by type
func (r *configCrawlQueryRepository) GetActiveCrawlConfigsByType(ctx context.Context, typeConfig string) ([]*querycrawlconfig.CrawlConfig, error) {
	sqlcConfigs, err := r.queries.GetActiveCrawlConfigsByTypeQuery(ctx, typeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to get active crawl configs by type %s: %w", typeConfig, err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// GetActiveCrawlConfigsByMarketAndType retrieves active crawl configs by market ID and type
func (r *configCrawlQueryRepository) GetActiveCrawlConfigsByMarketAndType(ctx context.Context, marketId int, typeConfig string) ([]*querycrawlconfig.CrawlConfig, error) {
	params := sqlc.GetActiveCrawlConfigsByMarketAndTypeQueryParams{
		MarketID:   marketId,
		TypeConfig: typeConfig,
	}

	sqlcConfigs, err := r.queries.GetActiveCrawlConfigsByMarketAndTypeQuery(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to get active crawl configs by market id %d and type %s: %w", marketId, typeConfig, err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}

// FilterCrawlConfigs retrieves crawl configs based on filter criteria
func (r *configCrawlQueryRepository) FilterCrawlConfigs(ctx context.Context, q querycrawlconfig.FilterCrawlConfigsQuery) ([]*querycrawlconfig.CrawlConfig, error) {
	panic("implement me")
}

// GetLeastUsedActiveConfig retrieves the least used active config
func (r *configCrawlQueryRepository) GetLeastUsedActiveConfig(ctx context.Context) (*querycrawlconfig.CrawlConfig, error) {
	sqlcConfig, err := r.queries.GetLeastUsedActiveConfigQuery(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get least used active config: %w", err)
	}

	config, err := FromSQLCModelToQueryModel(sqlcConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
	}

	return config, nil
}

// GetLeastUsedActiveConfigByMarket retrieves the least used active config by market
func (r *configCrawlQueryRepository) GetLeastUsedActiveConfigByMarket(ctx context.Context, marketId int) (*querycrawlconfig.CrawlConfig, error) {
	sqlcConfig, err := r.queries.GetLeastUsedActiveConfigByMarketQuery(ctx, marketId)
	if err != nil {
		return nil, fmt.Errorf("failed to get least used active config by market %d: %w", marketId, err)
	}

	config, err := FromSQLCModelToQueryModel(sqlcConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
	}

	return config, nil
}

// GetLeastUsedActiveConfigByType retrieves the least used active config by type
func (r *configCrawlQueryRepository) GetLeastUsedActiveConfigByType(ctx context.Context, typeConfig string) (*querycrawlconfig.CrawlConfig, error) {
	sqlcConfig, err := r.queries.GetLeastUsedActiveConfigByTypeQuery(ctx, typeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to get least used active config by type %s: %w", typeConfig, err)
	}

	config, err := FromSQLCModelToQueryModel(sqlcConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
	}

	return config, nil
}

// CountCrawlConfigs returns the total count of crawl configs
func (r *configCrawlQueryRepository) CountCrawlConfigs(ctx context.Context) (int64, error) {
	count, err := r.queries.CountCrawlConfigsQuery(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to count crawl configs: %w", err)
	}
	return count, nil
}

// CountActiveCrawlConfigs returns the count of active crawl configs
func (r *configCrawlQueryRepository) CountActiveCrawlConfigs(ctx context.Context) (int64, error) {
	count, err := r.queries.CountActiveCrawlConfigsQuery(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to count active crawl configs: %w", err)
	}
	return count, nil
}

// CountCrawlConfigsByMarket returns the count of crawl configs by market
func (r *configCrawlQueryRepository) CountCrawlConfigsByMarket(ctx context.Context, marketId int) (int64, error) {
	count, err := r.queries.CountCrawlConfigsByMarketQuery(ctx, marketId)
	if err != nil {
		return 0, fmt.Errorf("failed to count crawl configs by market %d: %w", marketId, err)
	}
	return count, nil
}

// GetCrawlConfigsByType retrieves crawl configs by type
func (r *configCrawlQueryRepository) GetCrawlConfigsByType(ctx context.Context, typeConfig string) ([]*querycrawlconfig.CrawlConfig, error) {
	sqlcConfigs, err := r.queries.GetCrawlConfigsByTypeQuery(ctx, typeConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl configs by type %s: %w", typeConfig, err)
	}

	configs := make([]*querycrawlconfig.CrawlConfig, len(sqlcConfigs))
	for i, sqlcConfig := range sqlcConfigs {
		config, err := FromSQLCModelToQueryModel(sqlcConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to convert crawl config model: %w", err)
		}
		configs[i] = config
	}

	return configs, nil
}
