package entity

import (
	"errors"
	"fmt"
	"strings"
	"time"
)

type AuthType string

const (
	AuthTypeAPIKey AuthType = "api_key"
	AuthTypeCookie AuthType = "cookie"
)

var (
	ErrAuthValueIsEmpty = errors.New("auth value is empty")
	ErrMarketInvalid    = errors.New("market is invalid")
	ErrAuthTypeNotMatch = errors.New("auth type not match")
	ErrAuthTypeInvalid  = errors.New("auth type invalid")
	ErrAuthNotActive    = errors.New("auth is not active")
	ErrAuthActive       = errors.New("auth is already active")
	ErrAuthIsUsed       = errors.New("auth is already used")
	ErrAuthNotUsed      = errors.New("auth is not used")
	ErrAuthIsExpired    = errors.New("auth has expired")
)

type AuthParams struct {
	MarketID  int
	AuthType  string
	Value     string
	IsActive  bool
	ExpiredAt *time.Time
}

type AuthCrawlRebuildParams struct {
	ID         int
	MarketID   int
	AuthType   AuthType
	Value      string
	IsActive   bool
	IsUsed     bool
	ExpiredAt  *time.Time
	LastUsedAt *time.Time
}

// Auth Aggregate Root
type Auth struct {
	id         int
	marketID   int
	authType   AuthType
	value      string
	isActive   bool
	isUse      bool
	lastUsedAt *time.Time
	expiredAt  *time.Time
}

// Constructor
func NewAuth(params AuthParams) (*Auth, error) {
	if params.MarketID <= 0 {
		return nil, ErrMarketInvalid
	}
	if params.AuthType == "" {
		return nil, errors.New("auth type cannot be empty")
	}
	authType := AuthType(strings.TrimSpace(params.AuthType))
	if authType != AuthTypeAPIKey && authType != AuthTypeCookie {
		return nil, ErrAuthTypeNotMatch
	}
	if strings.TrimSpace(params.Value) == "" {
		return nil, ErrAuthValueIsEmpty
	}
	return &Auth{
		marketID:  params.MarketID,
		authType:  authType,
		value:     strings.TrimSpace(params.Value),
		isActive:  params.IsActive,
		isUse:     false, // Mặc định chưa được sử dụng
		expiredAt: params.ExpiredAt,
	}, nil
}

func RebuildAuthCrawl(params AuthCrawlRebuildParams) *Auth {
	return &Auth{
		id:         params.ID,
		marketID:   params.MarketID,
		authType:   params.AuthType,
		value:      params.Value,
		isActive:   params.IsActive,
		isUse:      params.IsUsed,
		lastUsedAt: params.LastUsedAt,
		expiredAt:  params.ExpiredAt,
	}
}

// Getters
func (a *Auth) ID() int                { return a.id }
func (a *Auth) MarketID() int          { return a.marketID }
func (a *Auth) AuthType() AuthType     { return a.authType }
func (a *Auth) Value() string          { return a.value }
func (a *Auth) IsActive() bool         { return a.isActive }
func (a *Auth) IsUse() bool            { return a.isUse }
func (a *Auth) LastUsedAt() *time.Time { return a.lastUsedAt }
func (a *Auth) ExpiredAt() *time.Time  { return a.expiredAt }

// Domain Methods - Business Logic

// Status management
func (a *Auth) Activate() error {
	if a.isActive {
		return ErrAuthActive
	}
	a.isActive = true
	a.isUse = false
	return nil
}

func (a *Auth) Deactivate() error {
	if !a.isActive {
		return ErrAuthNotActive
	}
	a.isActive = false
	a.isUse = false // Khi deactivate thì cũng release luôn
	return nil

}

// Usage management
func (a *Auth) MarkUsed() error {
	if !a.isActive {
		return ErrAuthActive // Không thể sử dụng auth không active
	}
	if a.IsExpired() {
		return ErrAuthIsExpired // Không thể sử dụng auth đã hết hạn
	}
	a.isUse = true
	return nil
}

func (a *Auth) Release() error {
	if !a.isActive {
		return ErrAuthNotActive
	}
	if !a.isUse {
		return ErrAuthNotUsed
	}
	a.isUse = false
	return nil
}

// Update auth information
func (a *Auth) UpdateInfo(marketID int, authType string, value string, expiredAt *time.Time) error {
	if marketID <= 0 {
		return ErrMarketInvalid
	}
	if authType == "" {
		return ErrAuthTypeInvalid
	}
	at := AuthType(strings.TrimSpace(authType))
	if at != AuthTypeAPIKey && at != AuthTypeCookie {
		return ErrAuthTypeNotMatch
	}
	if strings.TrimSpace(value) == "" {
		return ErrAuthValueIsEmpty
	}

	a.marketID = marketID
	a.authType = at
	a.value = strings.TrimSpace(value)
	a.expiredAt = expiredAt
	return nil
}

func (a *Auth) IsExpired() bool {
	if a.expiredAt == nil {
		return false
	}
	return time.Now().After(*a.expiredAt)
}

func (a *Auth) IsAvailable() bool {
	return a.isActive && !a.isUse && !a.IsExpired()
}

func (a *Auth) GetUsageInfo() string {
	if a.lastUsedAt == nil {
		return "Never used"
	}
	return fmt.Sprintf("Last used: %s", a.lastUsedAt.Format("2006-01-02 15:04:05"))
}

func (a *Auth) CanBeUsed() error {
	if !a.isActive {
		return ErrAuthNotActive
	}
	if a.isUse {
		return ErrAuthIsUsed
	}
	if a.IsExpired() {
		return ErrAuthIsExpired
	}
	return nil
}

func (a *Auth) GetExpiryInfo() string {
	if a.expiredAt == nil {
		return "Never expires"
	}
	if a.IsExpired() {
		return fmt.Sprintf("Expired: %s", a.expiredAt.Format("2006-01-02 15:04:05"))
	}
	return fmt.Sprintf("Expires: %s", a.expiredAt.Format("2006-01-02 15:04:05"))
}
