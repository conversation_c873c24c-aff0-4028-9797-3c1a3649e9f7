package grpc

import (
	querymarket "go_core_market/internal/crawler/app/query/market"
	pb "go_core_market/pkg/pb/market"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func MarketToProto(market *querymarket.Market) *pb.Market {
	if market == nil {
		return nil
	}

	pbMarket := &pb.Market{
		Id:               int32(market.Id),
		Name:             market.Name,
		DisplayName:      market.DisplayName,
		Type:             market.Type,
		Status:           market.Status,
		BaseUrl:          market.BaseURL,
		Currency:         market.Currency,
		BuyerFeePercent:  market.BuyerFeePercent,
		SellerFeePercent: market.SellerFeePercent,
		CountryCode:      market.CountryCode,
		Language:         market.Language,
		Description:      market.Description,
		IsActive:         market.IsActive,
		CreatedAt:        timestamppb.New(market.CreatedAt),
		UpdatedAt:        timestamppb.New(market.UpdatedAt),
	}

	if market.LastCrawlAt != nil {
		pbMarket.LastCrawlAt = timestamppb.New(*market.LastCrawlAt)
	}

	return pbMarket
}
