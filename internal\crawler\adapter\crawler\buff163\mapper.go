package buff163

import (
	"go_core_market/internal/crawler/domain/value_object"
	"log"
	"strconv"
	"time"
)

func ItemToCrawledPrice(item PriceItem, marketId int) *value_object.CrawledPrice {
	sellPrice, err1 := strconv.ParseFloat(item.SellMinPrice, 64)
	buyPrice, err2 := strconv.ParseFloat(item.BuyMaxPrice, 64)
	if err1 != nil || err2 != nil {
		log.Printf("Error parsing price: %v, %v", err1, err2)
	}
	return &value_object.CrawledPrice{
		ItemName:   item.Name,
		MarketID:   marketId,
		SellPrice:  sellPrice,
		BuyPrice:   buyPrice,
		NumSell:    item.SellNum,
		NumBuy:     item.BuyNum,
		Condition:  nil,
		RecordedAt: time.Now(),
	}
}

func BuffItemToCrawledItem(item PriceItem, marketId int) *value_object.CrawledItem {
	tags := extractTags(item.GoodsInfo)
	description := ""
	if item.Description != nil {
		description = *item.Description
	}
	return &value_object.CrawledItem{
		Name:            item.Name,
		ShortName:       item.ShortName,
		IconURL:         item.GoodsInfo.IconURL,
		OriginalIconURL: item.GoodsInfo.OriginalIconURL,
		Description:     description,
		SteamMarketURL:  item.SteamMarketURL,
		Game:            item.Game,
		Tags:            tags,
		MarketID:        marketId,
	}
}

func extractTags(info GoodsInfo) []value_object.Tag {
	tags := []value_object.Tag{}
	appendTag := func(tag TagDetail, tagType string) {
		if tag.LocalizedName != "" {
			tags = append(tags, value_object.Tag{
				InternalName:  tag.InternalName,
				LocalizedName: tag.LocalizedName,
				TagType:       tagType,
			})
		}
	}
	appendTag(info.Info.Tags.Exterior, "exterior")
	appendTag(info.Info.Tags.Quality, "quality")
	appendTag(info.Info.Tags.Rarity, "rarity")
	appendTag(info.Info.Tags.Type, "type")
	appendTag(info.Info.Tags.Category, "category")
	return tags
}

func PriceDopplerToCrawledPrices(
	grouped map[string]struct {
		MinSell float64
		MaxBuy  float64
		NumSell int
		NumBuy  int
	},
	validPhases map[string]*value_object.ConditionDoppler,
	itemName string,
	marketID int,
) []*value_object.CrawledPrice {
	var result []*value_object.CrawledPrice
	now := time.Now()
	for phase, cond := range validPhases {
		if data, ok := grouped[phase]; ok {
			result = append(result, &value_object.CrawledPrice{
				ItemName:   itemName,
				MarketID:   marketID,
				SellPrice:  data.MinSell,
				BuyPrice:   data.MaxBuy,
				NumSell:    data.NumSell,
				NumBuy:     data.NumBuy,
				Condition:  cond,
				RecordedAt: now,
			})
		}
	}
	return result
}
