package querycrawlconfig

import (
	"context"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type GetConfigIDQuery struct {
	ConfigID int
}

type GetConfigIDQueryHandler decorator.QueryHandler[GetConfigIDQuery, *CrawlConfig]

type getConfigIDQueryHandler struct {
	repo CrawlConfigReadModel
}

func NewGetConfigIDQueryHandler(
	repo CrawlConfigReadModel,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) GetConfigIDQueryHandler {

	return decorator.ApplyQueryDecorators[GetConfigIDQuery, *CrawlConfig](
		getConfigIDQueryHandler{repo: repo},
		logger,
		metricsClient)
}

func (h getConfigIDQueryHandler) Handle(ctx context.Context, query GetConfigIDQuery) (*CrawlConfig, error) {
	return h.repo.GetCrawlConfigById(ctx, query.ConfigID)
}
