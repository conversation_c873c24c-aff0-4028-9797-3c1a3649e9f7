// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v4.25.1
// source: api/protobuf/auth_crawl.proto

package auth_crawl

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AuthCrawl message
type AuthCrawl struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MarketId      int32                  `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AuthType      string                 `protobuf:"bytes,3,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	Value         string                 `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	IsActive      bool                   `protobuf:"varint,5,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	IsUse         bool                   `protobuf:"varint,6,opt,name=is_use,json=isUse,proto3" json:"is_use,omitempty"`
	LastUsedAt    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=last_used_at,json=lastUsedAt,proto3" json:"last_used_at,omitempty"`
	ExpiredAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthCrawl) Reset() {
	*x = AuthCrawl{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthCrawl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthCrawl) ProtoMessage() {}

func (x *AuthCrawl) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthCrawl.ProtoReflect.Descriptor instead.
func (*AuthCrawl) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{0}
}

func (x *AuthCrawl) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AuthCrawl) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *AuthCrawl) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *AuthCrawl) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *AuthCrawl) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *AuthCrawl) GetIsUse() bool {
	if x != nil {
		return x.IsUse
	}
	return false
}

func (x *AuthCrawl) GetLastUsedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.LastUsedAt
	}
	return nil
}

func (x *AuthCrawl) GetExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiredAt
	}
	return nil
}

func (x *AuthCrawl) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *AuthCrawl) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

// Request/Response messages for Commands
type CreateAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AuthType      string                 `protobuf:"bytes,2,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	Value         string                 `protobuf:"bytes,3,opt,name=value,proto3" json:"value,omitempty"`
	IsActive      bool                   `protobuf:"varint,4,opt,name=is_active,json=isActive,proto3" json:"is_active,omitempty"`
	ExpiredAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,6,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAuthRequest) Reset() {
	*x = CreateAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAuthRequest) ProtoMessage() {}

func (x *CreateAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAuthRequest.ProtoReflect.Descriptor instead.
func (*CreateAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{1}
}

func (x *CreateAuthRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *CreateAuthRequest) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *CreateAuthRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *CreateAuthRequest) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *CreateAuthRequest) GetExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiredAt
	}
	return nil
}

func (x *CreateAuthRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAuthResponse) Reset() {
	*x = CreateAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAuthResponse) ProtoMessage() {}

func (x *CreateAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAuthResponse.ProtoReflect.Descriptor instead.
func (*CreateAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{2}
}

func (x *CreateAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CreateAuthBatchRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auths         []*CreateAuthRequest   `protobuf:"bytes,1,rep,name=auths,proto3" json:"auths,omitempty"`
	CreatedBy     string                 `protobuf:"bytes,2,opt,name=created_by,json=createdBy,proto3" json:"created_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAuthBatchRequest) Reset() {
	*x = CreateAuthBatchRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAuthBatchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAuthBatchRequest) ProtoMessage() {}

func (x *CreateAuthBatchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAuthBatchRequest.ProtoReflect.Descriptor instead.
func (*CreateAuthBatchRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAuthBatchRequest) GetAuths() []*CreateAuthRequest {
	if x != nil {
		return x.Auths
	}
	return nil
}

func (x *CreateAuthBatchRequest) GetCreatedBy() string {
	if x != nil {
		return x.CreatedBy
	}
	return ""
}

type CreateAuthBatchResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAuthBatchResponse) Reset() {
	*x = CreateAuthBatchResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAuthBatchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAuthBatchResponse) ProtoMessage() {}

func (x *CreateAuthBatchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAuthBatchResponse.ProtoReflect.Descriptor instead.
func (*CreateAuthBatchResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{4}
}

func (x *CreateAuthBatchResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateAuthBatchResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UpdateAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	MarketId      int32                  `protobuf:"varint,2,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	AuthType      string                 `protobuf:"bytes,3,opt,name=auth_type,json=authType,proto3" json:"auth_type,omitempty"`
	Value         string                 `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	ExpiredAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=expired_at,json=expiredAt,proto3" json:"expired_at,omitempty"`
	UpdatedBy     string                 `protobuf:"bytes,6,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAuthRequest) Reset() {
	*x = UpdateAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAuthRequest) ProtoMessage() {}

func (x *UpdateAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAuthRequest.ProtoReflect.Descriptor instead.
func (*UpdateAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{5}
}

func (x *UpdateAuthRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAuthRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *UpdateAuthRequest) GetAuthType() string {
	if x != nil {
		return x.AuthType
	}
	return ""
}

func (x *UpdateAuthRequest) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *UpdateAuthRequest) GetExpiredAt() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiredAt
	}
	return nil
}

func (x *UpdateAuthRequest) GetUpdatedBy() string {
	if x != nil {
		return x.UpdatedBy
	}
	return ""
}

type UpdateAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAuthResponse) Reset() {
	*x = UpdateAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAuthResponse) ProtoMessage() {}

func (x *UpdateAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAuthResponse.ProtoReflect.Descriptor instead.
func (*UpdateAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeleteAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeletedBy     string                 `protobuf:"bytes,2,opt,name=deleted_by,json=deletedBy,proto3" json:"deleted_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAuthRequest) Reset() {
	*x = DeleteAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAuthRequest) ProtoMessage() {}

func (x *DeleteAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAuthRequest.ProtoReflect.Descriptor instead.
func (*DeleteAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteAuthRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteAuthRequest) GetDeletedBy() string {
	if x != nil {
		return x.DeletedBy
	}
	return ""
}

type DeleteAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAuthResponse) Reset() {
	*x = DeleteAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAuthResponse) ProtoMessage() {}

func (x *DeleteAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAuthResponse.ProtoReflect.Descriptor instead.
func (*DeleteAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ActivateAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ActivatedBy   string                 `protobuf:"bytes,2,opt,name=activated_by,json=activatedBy,proto3" json:"activated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateAuthRequest) Reset() {
	*x = ActivateAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateAuthRequest) ProtoMessage() {}

func (x *ActivateAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateAuthRequest.ProtoReflect.Descriptor instead.
func (*ActivateAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{9}
}

func (x *ActivateAuthRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ActivateAuthRequest) GetActivatedBy() string {
	if x != nil {
		return x.ActivatedBy
	}
	return ""
}

type ActivateAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActivateAuthResponse) Reset() {
	*x = ActivateAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActivateAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActivateAuthResponse) ProtoMessage() {}

func (x *ActivateAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActivateAuthResponse.ProtoReflect.Descriptor instead.
func (*ActivateAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{10}
}

func (x *ActivateAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ActivateAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type DeactivateAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DeactivatedBy string                 `protobuf:"bytes,2,opt,name=deactivated_by,json=deactivatedBy,proto3" json:"deactivated_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateAuthRequest) Reset() {
	*x = DeactivateAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateAuthRequest) ProtoMessage() {}

func (x *DeactivateAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateAuthRequest.ProtoReflect.Descriptor instead.
func (*DeactivateAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{11}
}

func (x *DeactivateAuthRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeactivateAuthRequest) GetDeactivatedBy() string {
	if x != nil {
		return x.DeactivatedBy
	}
	return ""
}

type DeactivateAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeactivateAuthResponse) Reset() {
	*x = DeactivateAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeactivateAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeactivateAuthResponse) ProtoMessage() {}

func (x *DeactivateAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeactivateAuthResponse.ProtoReflect.Descriptor instead.
func (*DeactivateAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{12}
}

func (x *DeactivateAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeactivateAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MarkAuthUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	UsedBy        string                 `protobuf:"bytes,2,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAuthUsedRequest) Reset() {
	*x = MarkAuthUsedRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAuthUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAuthUsedRequest) ProtoMessage() {}

func (x *MarkAuthUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAuthUsedRequest.ProtoReflect.Descriptor instead.
func (*MarkAuthUsedRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{13}
}

func (x *MarkAuthUsedRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *MarkAuthUsedRequest) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

type MarkAuthUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAuthUsedResponse) Reset() {
	*x = MarkAuthUsedResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAuthUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAuthUsedResponse) ProtoMessage() {}

func (x *MarkAuthUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAuthUsedResponse.ProtoReflect.Descriptor instead.
func (*MarkAuthUsedResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{14}
}

func (x *MarkAuthUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkAuthUsedResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type MarkAuthsUsedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []int32                `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	UsedBy        string                 `protobuf:"bytes,2,opt,name=used_by,json=usedBy,proto3" json:"used_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAuthsUsedRequest) Reset() {
	*x = MarkAuthsUsedRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAuthsUsedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAuthsUsedRequest) ProtoMessage() {}

func (x *MarkAuthsUsedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAuthsUsedRequest.ProtoReflect.Descriptor instead.
func (*MarkAuthsUsedRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{15}
}

func (x *MarkAuthsUsedRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *MarkAuthsUsedRequest) GetUsedBy() string {
	if x != nil {
		return x.UsedBy
	}
	return ""
}

type MarkAuthsUsedResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MarkAuthsUsedResponse) Reset() {
	*x = MarkAuthsUsedResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MarkAuthsUsedResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MarkAuthsUsedResponse) ProtoMessage() {}

func (x *MarkAuthsUsedResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MarkAuthsUsedResponse.ProtoReflect.Descriptor instead.
func (*MarkAuthsUsedResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{16}
}

func (x *MarkAuthsUsedResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *MarkAuthsUsedResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReleaseAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ReleasedBy    string                 `protobuf:"bytes,2,opt,name=released_by,json=releasedBy,proto3" json:"released_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseAuthRequest) Reset() {
	*x = ReleaseAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseAuthRequest) ProtoMessage() {}

func (x *ReleaseAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseAuthRequest.ProtoReflect.Descriptor instead.
func (*ReleaseAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{17}
}

func (x *ReleaseAuthRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ReleaseAuthRequest) GetReleasedBy() string {
	if x != nil {
		return x.ReleasedBy
	}
	return ""
}

type ReleaseAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseAuthResponse) Reset() {
	*x = ReleaseAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseAuthResponse) ProtoMessage() {}

func (x *ReleaseAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseAuthResponse.ProtoReflect.Descriptor instead.
func (*ReleaseAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{18}
}

func (x *ReleaseAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReleaseAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type ReleaseManyAuthRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ids           []int32                `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	ReleasedBy    string                 `protobuf:"bytes,2,opt,name=released_by,json=releasedBy,proto3" json:"released_by,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseManyAuthRequest) Reset() {
	*x = ReleaseManyAuthRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseManyAuthRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseManyAuthRequest) ProtoMessage() {}

func (x *ReleaseManyAuthRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseManyAuthRequest.ProtoReflect.Descriptor instead.
func (*ReleaseManyAuthRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{19}
}

func (x *ReleaseManyAuthRequest) GetIds() []int32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReleaseManyAuthRequest) GetReleasedBy() string {
	if x != nil {
		return x.ReleasedBy
	}
	return ""
}

type ReleaseManyAuthResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReleaseManyAuthResponse) Reset() {
	*x = ReleaseManyAuthResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReleaseManyAuthResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseManyAuthResponse) ProtoMessage() {}

func (x *ReleaseManyAuthResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseManyAuthResponse.ProtoReflect.Descriptor instead.
func (*ReleaseManyAuthResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{20}
}

func (x *ReleaseManyAuthResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *ReleaseManyAuthResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Request/Response messages for Queries
type GetAllAuthCrawlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Order         string                 `protobuf:"bytes,3,opt,name=order,proto3" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllAuthCrawlRequest) Reset() {
	*x = GetAllAuthCrawlRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAuthCrawlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAuthCrawlRequest) ProtoMessage() {}

func (x *GetAllAuthCrawlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAuthCrawlRequest.ProtoReflect.Descriptor instead.
func (*GetAllAuthCrawlRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{21}
}

func (x *GetAllAuthCrawlRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllAuthCrawlRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllAuthCrawlRequest) GetOrder() string {
	if x != nil {
		return x.Order
	}
	return ""
}

type GetAllAuthCrawlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auths         []*AuthCrawl           `protobuf:"bytes,1,rep,name=auths,proto3" json:"auths,omitempty"`
	Total         int32                  `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	TotalPages    int32                  `protobuf:"varint,5,opt,name=total_pages,json=totalPages,proto3" json:"total_pages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllAuthCrawlResponse) Reset() {
	*x = GetAllAuthCrawlResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllAuthCrawlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllAuthCrawlResponse) ProtoMessage() {}

func (x *GetAllAuthCrawlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllAuthCrawlResponse.ProtoReflect.Descriptor instead.
func (*GetAllAuthCrawlResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{22}
}

func (x *GetAllAuthCrawlResponse) GetAuths() []*AuthCrawl {
	if x != nil {
		return x.Auths
	}
	return nil
}

func (x *GetAllAuthCrawlResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetAllAuthCrawlResponse) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetAllAuthCrawlResponse) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *GetAllAuthCrawlResponse) GetTotalPages() int32 {
	if x != nil {
		return x.TotalPages
	}
	return 0
}

type GetAuthCrawlByIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthCrawlByIdRequest) Reset() {
	*x = GetAuthCrawlByIdRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthCrawlByIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCrawlByIdRequest) ProtoMessage() {}

func (x *GetAuthCrawlByIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCrawlByIdRequest.ProtoReflect.Descriptor instead.
func (*GetAuthCrawlByIdRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{23}
}

func (x *GetAuthCrawlByIdRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAuthCrawlByIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auth          *AuthCrawl             `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthCrawlByIdResponse) Reset() {
	*x = GetAuthCrawlByIdResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthCrawlByIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCrawlByIdResponse) ProtoMessage() {}

func (x *GetAuthCrawlByIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCrawlByIdResponse.ProtoReflect.Descriptor instead.
func (*GetAuthCrawlByIdResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{24}
}

func (x *GetAuthCrawlByIdResponse) GetAuth() *AuthCrawl {
	if x != nil {
		return x.Auth
	}
	return nil
}

type GetAuthCrawlByMarketIdRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthCrawlByMarketIdRequest) Reset() {
	*x = GetAuthCrawlByMarketIdRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthCrawlByMarketIdRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCrawlByMarketIdRequest) ProtoMessage() {}

func (x *GetAuthCrawlByMarketIdRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCrawlByMarketIdRequest.ProtoReflect.Descriptor instead.
func (*GetAuthCrawlByMarketIdRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{25}
}

func (x *GetAuthCrawlByMarketIdRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

type GetAuthCrawlByMarketIdResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auth          *AuthCrawl             `protobuf:"bytes,1,opt,name=auth,proto3" json:"auth,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAuthCrawlByMarketIdResponse) Reset() {
	*x = GetAuthCrawlByMarketIdResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAuthCrawlByMarketIdResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAuthCrawlByMarketIdResponse) ProtoMessage() {}

func (x *GetAuthCrawlByMarketIdResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAuthCrawlByMarketIdResponse.ProtoReflect.Descriptor instead.
func (*GetAuthCrawlByMarketIdResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{26}
}

func (x *GetAuthCrawlByMarketIdResponse) GetAuth() *AuthCrawl {
	if x != nil {
		return x.Auth
	}
	return nil
}

type GetAvailableAuthCrawlByMarketRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MarketId      int32                  `protobuf:"varint,1,opt,name=market_id,json=marketId,proto3" json:"market_id,omitempty"`
	Limit         int32                  `protobuf:"varint,2,opt,name=limit,proto3" json:"limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableAuthCrawlByMarketRequest) Reset() {
	*x = GetAvailableAuthCrawlByMarketRequest{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableAuthCrawlByMarketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableAuthCrawlByMarketRequest) ProtoMessage() {}

func (x *GetAvailableAuthCrawlByMarketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableAuthCrawlByMarketRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableAuthCrawlByMarketRequest) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{27}
}

func (x *GetAvailableAuthCrawlByMarketRequest) GetMarketId() int32 {
	if x != nil {
		return x.MarketId
	}
	return 0
}

func (x *GetAvailableAuthCrawlByMarketRequest) GetLimit() int32 {
	if x != nil {
		return x.Limit
	}
	return 0
}

type GetAvailableAuthCrawlByMarketResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Auths         []*AuthCrawl           `protobuf:"bytes,1,rep,name=auths,proto3" json:"auths,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAvailableAuthCrawlByMarketResponse) Reset() {
	*x = GetAvailableAuthCrawlByMarketResponse{}
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAvailableAuthCrawlByMarketResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableAuthCrawlByMarketResponse) ProtoMessage() {}

func (x *GetAvailableAuthCrawlByMarketResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_protobuf_auth_crawl_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableAuthCrawlByMarketResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableAuthCrawlByMarketResponse) Descriptor() ([]byte, []int) {
	return file_api_protobuf_auth_crawl_proto_rawDescGZIP(), []int{28}
}

func (x *GetAvailableAuthCrawlByMarketResponse) GetAuths() []*AuthCrawl {
	if x != nil {
		return x.Auths
	}
	return nil
}

var File_api_protobuf_auth_crawl_proto protoreflect.FileDescriptor

const file_api_protobuf_auth_crawl_proto_rawDesc = "" +
	"\n" +
	"\x1dapi/protobuf/auth_crawl.proto\x12\rauth_crawl.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\x8e\x03\n" +
	"\tAuthCrawl\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\x05R\bmarketId\x12\x1b\n" +
	"\tauth_type\x18\x03 \x01(\tR\bauthType\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\x12\x1b\n" +
	"\tis_active\x18\x05 \x01(\bR\bisActive\x12\x15\n" +
	"\x06is_use\x18\x06 \x01(\bR\x05isUse\x12<\n" +
	"\flast_used_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"lastUsedAt\x129\n" +
	"\n" +
	"expired_at\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\texpiredAt\x129\n" +
	"\n" +
	"created_at\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\"\xda\x01\n" +
	"\x11CreateAuthRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12\x1b\n" +
	"\tauth_type\x18\x02 \x01(\tR\bauthType\x12\x14\n" +
	"\x05value\x18\x03 \x01(\tR\x05value\x12\x1b\n" +
	"\tis_active\x18\x04 \x01(\bR\bisActive\x129\n" +
	"\n" +
	"expired_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\texpiredAt\x12\x1d\n" +
	"\n" +
	"created_by\x18\x06 \x01(\tR\tcreatedBy\"H\n" +
	"\x12CreateAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"o\n" +
	"\x16CreateAuthBatchRequest\x126\n" +
	"\x05auths\x18\x01 \x03(\v2 .auth_crawl.v1.CreateAuthRequestR\x05auths\x12\x1d\n" +
	"\n" +
	"created_by\x18\x02 \x01(\tR\tcreatedBy\"M\n" +
	"\x17CreateAuthBatchResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xcd\x01\n" +
	"\x11UpdateAuthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1b\n" +
	"\tmarket_id\x18\x02 \x01(\x05R\bmarketId\x12\x1b\n" +
	"\tauth_type\x18\x03 \x01(\tR\bauthType\x12\x14\n" +
	"\x05value\x18\x04 \x01(\tR\x05value\x129\n" +
	"\n" +
	"expired_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\texpiredAt\x12\x1d\n" +
	"\n" +
	"updated_by\x18\x06 \x01(\tR\tupdatedBy\"H\n" +
	"\x12UpdateAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"B\n" +
	"\x11DeleteAuthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1d\n" +
	"\n" +
	"deleted_by\x18\x02 \x01(\tR\tdeletedBy\"H\n" +
	"\x12DeleteAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"H\n" +
	"\x13ActivateAuthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12!\n" +
	"\factivated_by\x18\x02 \x01(\tR\vactivatedBy\"J\n" +
	"\x14ActivateAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"N\n" +
	"\x15DeactivateAuthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12%\n" +
	"\x0edeactivated_by\x18\x02 \x01(\tR\rdeactivatedBy\"L\n" +
	"\x16DeactivateAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\">\n" +
	"\x13MarkAuthUsedRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x17\n" +
	"\aused_by\x18\x02 \x01(\tR\x06usedBy\"J\n" +
	"\x14MarkAuthUsedResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"A\n" +
	"\x14MarkAuthsUsedRequest\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x05R\x03ids\x12\x17\n" +
	"\aused_by\x18\x02 \x01(\tR\x06usedBy\"K\n" +
	"\x15MarkAuthsUsedResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"E\n" +
	"\x12ReleaseAuthRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x1f\n" +
	"\vreleased_by\x18\x02 \x01(\tR\n" +
	"releasedBy\"I\n" +
	"\x13ReleaseAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"K\n" +
	"\x16ReleaseManyAuthRequest\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x05R\x03ids\x12\x1f\n" +
	"\vreleased_by\x18\x02 \x01(\tR\n" +
	"releasedBy\"M\n" +
	"\x17ReleaseManyAuthResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"_\n" +
	"\x16GetAllAuthCrawlRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x14\n" +
	"\x05order\x18\x03 \x01(\tR\x05order\"\xb1\x01\n" +
	"\x17GetAllAuthCrawlResponse\x12.\n" +
	"\x05auths\x18\x01 \x03(\v2\x18.auth_crawl.v1.AuthCrawlR\x05auths\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x04 \x01(\x05R\bpageSize\x12\x1f\n" +
	"\vtotal_pages\x18\x05 \x01(\x05R\n" +
	"totalPages\")\n" +
	"\x17GetAuthCrawlByIdRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"H\n" +
	"\x18GetAuthCrawlByIdResponse\x12,\n" +
	"\x04auth\x18\x01 \x01(\v2\x18.auth_crawl.v1.AuthCrawlR\x04auth\"<\n" +
	"\x1dGetAuthCrawlByMarketIdRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\"N\n" +
	"\x1eGetAuthCrawlByMarketIdResponse\x12,\n" +
	"\x04auth\x18\x01 \x01(\v2\x18.auth_crawl.v1.AuthCrawlR\x04auth\"Y\n" +
	"$GetAvailableAuthCrawlByMarketRequest\x12\x1b\n" +
	"\tmarket_id\x18\x01 \x01(\x05R\bmarketId\x12\x14\n" +
	"\x05limit\x18\x02 \x01(\x05R\x05limit\"W\n" +
	"%GetAvailableAuthCrawlByMarketResponse\x12.\n" +
	"\x05auths\x18\x01 \x03(\v2\x18.auth_crawl.v1.AuthCrawlR\x05auths2\xdd\n" +
	"\n" +
	"\x10AuthCrawlService\x12Q\n" +
	"\n" +
	"CreateAuth\x12 .auth_crawl.v1.CreateAuthRequest\x1a!.auth_crawl.v1.CreateAuthResponse\x12`\n" +
	"\x0fCreateAuthBatch\x12%.auth_crawl.v1.CreateAuthBatchRequest\x1a&.auth_crawl.v1.CreateAuthBatchResponse\x12Q\n" +
	"\n" +
	"UpdateAuth\x12 .auth_crawl.v1.UpdateAuthRequest\x1a!.auth_crawl.v1.UpdateAuthResponse\x12Q\n" +
	"\n" +
	"DeleteAuth\x12 .auth_crawl.v1.DeleteAuthRequest\x1a!.auth_crawl.v1.DeleteAuthResponse\x12W\n" +
	"\fActivateAuth\x12\".auth_crawl.v1.ActivateAuthRequest\x1a#.auth_crawl.v1.ActivateAuthResponse\x12]\n" +
	"\x0eDeactivateAuth\x12$.auth_crawl.v1.DeactivateAuthRequest\x1a%.auth_crawl.v1.DeactivateAuthResponse\x12W\n" +
	"\fMarkAuthUsed\x12\".auth_crawl.v1.MarkAuthUsedRequest\x1a#.auth_crawl.v1.MarkAuthUsedResponse\x12Z\n" +
	"\rMarkAuthsUsed\x12#.auth_crawl.v1.MarkAuthsUsedRequest\x1a$.auth_crawl.v1.MarkAuthsUsedResponse\x12T\n" +
	"\vReleaseAuth\x12!.auth_crawl.v1.ReleaseAuthRequest\x1a\".auth_crawl.v1.ReleaseAuthResponse\x12`\n" +
	"\x0fReleaseManyAuth\x12%.auth_crawl.v1.ReleaseManyAuthRequest\x1a&.auth_crawl.v1.ReleaseManyAuthResponse\x12`\n" +
	"\x0fGetAllAuthCrawl\x12%.auth_crawl.v1.GetAllAuthCrawlRequest\x1a&.auth_crawl.v1.GetAllAuthCrawlResponse\x12c\n" +
	"\x10GetAuthCrawlById\x12&.auth_crawl.v1.GetAuthCrawlByIdRequest\x1a'.auth_crawl.v1.GetAuthCrawlByIdResponse\x12u\n" +
	"\x16GetAuthCrawlByMarketId\x12,.auth_crawl.v1.GetAuthCrawlByMarketIdRequest\x1a-.auth_crawl.v1.GetAuthCrawlByMarketIdResponse\x12\x8a\x01\n" +
	"\x1dGetAvailableAuthCrawlByMarket\x123.auth_crawl.v1.GetAvailableAuthCrawlByMarketRequest\x1a4.auth_crawl.v1.GetAvailableAuthCrawlByMarketResponseB\"Z go_core_market/pkg/pb/auth_crawlb\x06proto3"

var (
	file_api_protobuf_auth_crawl_proto_rawDescOnce sync.Once
	file_api_protobuf_auth_crawl_proto_rawDescData []byte
)

func file_api_protobuf_auth_crawl_proto_rawDescGZIP() []byte {
	file_api_protobuf_auth_crawl_proto_rawDescOnce.Do(func() {
		file_api_protobuf_auth_crawl_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_protobuf_auth_crawl_proto_rawDesc), len(file_api_protobuf_auth_crawl_proto_rawDesc)))
	})
	return file_api_protobuf_auth_crawl_proto_rawDescData
}

var file_api_protobuf_auth_crawl_proto_msgTypes = make([]protoimpl.MessageInfo, 29)
var file_api_protobuf_auth_crawl_proto_goTypes = []any{
	(*AuthCrawl)(nil),                             // 0: auth_crawl.v1.AuthCrawl
	(*CreateAuthRequest)(nil),                     // 1: auth_crawl.v1.CreateAuthRequest
	(*CreateAuthResponse)(nil),                    // 2: auth_crawl.v1.CreateAuthResponse
	(*CreateAuthBatchRequest)(nil),                // 3: auth_crawl.v1.CreateAuthBatchRequest
	(*CreateAuthBatchResponse)(nil),               // 4: auth_crawl.v1.CreateAuthBatchResponse
	(*UpdateAuthRequest)(nil),                     // 5: auth_crawl.v1.UpdateAuthRequest
	(*UpdateAuthResponse)(nil),                    // 6: auth_crawl.v1.UpdateAuthResponse
	(*DeleteAuthRequest)(nil),                     // 7: auth_crawl.v1.DeleteAuthRequest
	(*DeleteAuthResponse)(nil),                    // 8: auth_crawl.v1.DeleteAuthResponse
	(*ActivateAuthRequest)(nil),                   // 9: auth_crawl.v1.ActivateAuthRequest
	(*ActivateAuthResponse)(nil),                  // 10: auth_crawl.v1.ActivateAuthResponse
	(*DeactivateAuthRequest)(nil),                 // 11: auth_crawl.v1.DeactivateAuthRequest
	(*DeactivateAuthResponse)(nil),                // 12: auth_crawl.v1.DeactivateAuthResponse
	(*MarkAuthUsedRequest)(nil),                   // 13: auth_crawl.v1.MarkAuthUsedRequest
	(*MarkAuthUsedResponse)(nil),                  // 14: auth_crawl.v1.MarkAuthUsedResponse
	(*MarkAuthsUsedRequest)(nil),                  // 15: auth_crawl.v1.MarkAuthsUsedRequest
	(*MarkAuthsUsedResponse)(nil),                 // 16: auth_crawl.v1.MarkAuthsUsedResponse
	(*ReleaseAuthRequest)(nil),                    // 17: auth_crawl.v1.ReleaseAuthRequest
	(*ReleaseAuthResponse)(nil),                   // 18: auth_crawl.v1.ReleaseAuthResponse
	(*ReleaseManyAuthRequest)(nil),                // 19: auth_crawl.v1.ReleaseManyAuthRequest
	(*ReleaseManyAuthResponse)(nil),               // 20: auth_crawl.v1.ReleaseManyAuthResponse
	(*GetAllAuthCrawlRequest)(nil),                // 21: auth_crawl.v1.GetAllAuthCrawlRequest
	(*GetAllAuthCrawlResponse)(nil),               // 22: auth_crawl.v1.GetAllAuthCrawlResponse
	(*GetAuthCrawlByIdRequest)(nil),               // 23: auth_crawl.v1.GetAuthCrawlByIdRequest
	(*GetAuthCrawlByIdResponse)(nil),              // 24: auth_crawl.v1.GetAuthCrawlByIdResponse
	(*GetAuthCrawlByMarketIdRequest)(nil),         // 25: auth_crawl.v1.GetAuthCrawlByMarketIdRequest
	(*GetAuthCrawlByMarketIdResponse)(nil),        // 26: auth_crawl.v1.GetAuthCrawlByMarketIdResponse
	(*GetAvailableAuthCrawlByMarketRequest)(nil),  // 27: auth_crawl.v1.GetAvailableAuthCrawlByMarketRequest
	(*GetAvailableAuthCrawlByMarketResponse)(nil), // 28: auth_crawl.v1.GetAvailableAuthCrawlByMarketResponse
	(*timestamppb.Timestamp)(nil),                 // 29: google.protobuf.Timestamp
}
var file_api_protobuf_auth_crawl_proto_depIdxs = []int32{
	29, // 0: auth_crawl.v1.AuthCrawl.last_used_at:type_name -> google.protobuf.Timestamp
	29, // 1: auth_crawl.v1.AuthCrawl.expired_at:type_name -> google.protobuf.Timestamp
	29, // 2: auth_crawl.v1.AuthCrawl.created_at:type_name -> google.protobuf.Timestamp
	29, // 3: auth_crawl.v1.AuthCrawl.updated_at:type_name -> google.protobuf.Timestamp
	29, // 4: auth_crawl.v1.CreateAuthRequest.expired_at:type_name -> google.protobuf.Timestamp
	1,  // 5: auth_crawl.v1.CreateAuthBatchRequest.auths:type_name -> auth_crawl.v1.CreateAuthRequest
	29, // 6: auth_crawl.v1.UpdateAuthRequest.expired_at:type_name -> google.protobuf.Timestamp
	0,  // 7: auth_crawl.v1.GetAllAuthCrawlResponse.auths:type_name -> auth_crawl.v1.AuthCrawl
	0,  // 8: auth_crawl.v1.GetAuthCrawlByIdResponse.auth:type_name -> auth_crawl.v1.AuthCrawl
	0,  // 9: auth_crawl.v1.GetAuthCrawlByMarketIdResponse.auth:type_name -> auth_crawl.v1.AuthCrawl
	0,  // 10: auth_crawl.v1.GetAvailableAuthCrawlByMarketResponse.auths:type_name -> auth_crawl.v1.AuthCrawl
	1,  // 11: auth_crawl.v1.AuthCrawlService.CreateAuth:input_type -> auth_crawl.v1.CreateAuthRequest
	3,  // 12: auth_crawl.v1.AuthCrawlService.CreateAuthBatch:input_type -> auth_crawl.v1.CreateAuthBatchRequest
	5,  // 13: auth_crawl.v1.AuthCrawlService.UpdateAuth:input_type -> auth_crawl.v1.UpdateAuthRequest
	7,  // 14: auth_crawl.v1.AuthCrawlService.DeleteAuth:input_type -> auth_crawl.v1.DeleteAuthRequest
	9,  // 15: auth_crawl.v1.AuthCrawlService.ActivateAuth:input_type -> auth_crawl.v1.ActivateAuthRequest
	11, // 16: auth_crawl.v1.AuthCrawlService.DeactivateAuth:input_type -> auth_crawl.v1.DeactivateAuthRequest
	13, // 17: auth_crawl.v1.AuthCrawlService.MarkAuthUsed:input_type -> auth_crawl.v1.MarkAuthUsedRequest
	15, // 18: auth_crawl.v1.AuthCrawlService.MarkAuthsUsed:input_type -> auth_crawl.v1.MarkAuthsUsedRequest
	17, // 19: auth_crawl.v1.AuthCrawlService.ReleaseAuth:input_type -> auth_crawl.v1.ReleaseAuthRequest
	19, // 20: auth_crawl.v1.AuthCrawlService.ReleaseManyAuth:input_type -> auth_crawl.v1.ReleaseManyAuthRequest
	21, // 21: auth_crawl.v1.AuthCrawlService.GetAllAuthCrawl:input_type -> auth_crawl.v1.GetAllAuthCrawlRequest
	23, // 22: auth_crawl.v1.AuthCrawlService.GetAuthCrawlById:input_type -> auth_crawl.v1.GetAuthCrawlByIdRequest
	25, // 23: auth_crawl.v1.AuthCrawlService.GetAuthCrawlByMarketId:input_type -> auth_crawl.v1.GetAuthCrawlByMarketIdRequest
	27, // 24: auth_crawl.v1.AuthCrawlService.GetAvailableAuthCrawlByMarket:input_type -> auth_crawl.v1.GetAvailableAuthCrawlByMarketRequest
	2,  // 25: auth_crawl.v1.AuthCrawlService.CreateAuth:output_type -> auth_crawl.v1.CreateAuthResponse
	4,  // 26: auth_crawl.v1.AuthCrawlService.CreateAuthBatch:output_type -> auth_crawl.v1.CreateAuthBatchResponse
	6,  // 27: auth_crawl.v1.AuthCrawlService.UpdateAuth:output_type -> auth_crawl.v1.UpdateAuthResponse
	8,  // 28: auth_crawl.v1.AuthCrawlService.DeleteAuth:output_type -> auth_crawl.v1.DeleteAuthResponse
	10, // 29: auth_crawl.v1.AuthCrawlService.ActivateAuth:output_type -> auth_crawl.v1.ActivateAuthResponse
	12, // 30: auth_crawl.v1.AuthCrawlService.DeactivateAuth:output_type -> auth_crawl.v1.DeactivateAuthResponse
	14, // 31: auth_crawl.v1.AuthCrawlService.MarkAuthUsed:output_type -> auth_crawl.v1.MarkAuthUsedResponse
	16, // 32: auth_crawl.v1.AuthCrawlService.MarkAuthsUsed:output_type -> auth_crawl.v1.MarkAuthsUsedResponse
	18, // 33: auth_crawl.v1.AuthCrawlService.ReleaseAuth:output_type -> auth_crawl.v1.ReleaseAuthResponse
	20, // 34: auth_crawl.v1.AuthCrawlService.ReleaseManyAuth:output_type -> auth_crawl.v1.ReleaseManyAuthResponse
	22, // 35: auth_crawl.v1.AuthCrawlService.GetAllAuthCrawl:output_type -> auth_crawl.v1.GetAllAuthCrawlResponse
	24, // 36: auth_crawl.v1.AuthCrawlService.GetAuthCrawlById:output_type -> auth_crawl.v1.GetAuthCrawlByIdResponse
	26, // 37: auth_crawl.v1.AuthCrawlService.GetAuthCrawlByMarketId:output_type -> auth_crawl.v1.GetAuthCrawlByMarketIdResponse
	28, // 38: auth_crawl.v1.AuthCrawlService.GetAvailableAuthCrawlByMarket:output_type -> auth_crawl.v1.GetAvailableAuthCrawlByMarketResponse
	25, // [25:39] is the sub-list for method output_type
	11, // [11:25] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_api_protobuf_auth_crawl_proto_init() }
func file_api_protobuf_auth_crawl_proto_init() {
	if File_api_protobuf_auth_crawl_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_protobuf_auth_crawl_proto_rawDesc), len(file_api_protobuf_auth_crawl_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   29,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_protobuf_auth_crawl_proto_goTypes,
		DependencyIndexes: file_api_protobuf_auth_crawl_proto_depIdxs,
		MessageInfos:      file_api_protobuf_auth_crawl_proto_msgTypes,
	}.Build()
	File_api_protobuf_auth_crawl_proto = out.File
	file_api_protobuf_auth_crawl_proto_goTypes = nil
	file_api_protobuf_auth_crawl_proto_depIdxs = nil
}
