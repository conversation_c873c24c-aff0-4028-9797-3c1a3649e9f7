package commandproxy

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CreateProxy struct {
	Host      string
	Port      int
	UserName  string
	Password  string
	IsActive  bool
	CreatedBy string
}

type CreateProxyHandler decorator.CommandHandler[CreateProxy]

type createProxyHandler struct {
	repo   repository.RepositoryProxy
	broker message.Broker
}

func NewCreateProxyHandler(
	repo repository.RepositoryProxy,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateProxyHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateProxy](
		createProxyHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createProxyHandler) Handle(ctx context.Context, cmd CreateProxy) error {
	// Create domain proxy
	proxyParams := entity.ProxyParams{
		Host:     cmd.Host,
		Port:     cmd.Port,
		UserName: cmd.UserName,
		Password: cmd.Password,
		IsActive: cmd.IsActive,
	}

	proxy, err := entity.NewProxy(proxyParams)
	if err != nil {
		return err
	}

	err = h.repo.Create(ctx, proxy)
	if err != nil {
		return err
	}
	return nil
}
