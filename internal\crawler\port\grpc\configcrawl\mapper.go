package grpc

import (
	querycrawlconfig "go_core_market/internal/crawler/app/query/crawlconfig"
	pb "go_core_market/pkg/pb/configcrawl"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func CrawlConfigToProto(config *querycrawlconfig.CrawlConfig) *pb.CrawlConfig {
	if config == nil {
		return nil
	}

	pbConfig := &pb.CrawlConfig{
		Id:                     int32(config.Id),
		MarketId:               int32(config.MarketId),
		Description:            config.Description,
		TypeConfig:             config.TypeConfig,
		RequestsPerMinute:      int32(config.RequestsPerMinute),
		RequireAuth:            config.RequireAuth,
		MaxNumberAuth:          int32(config.MaxNumberAuth),
		MaxNumberProxy:         int32(config.MaxNumberProxy),
		PerRequestDelaySeconds: int32(config.PerRequestDelaySeconds),
		TimeoutSeconds:         int32(config.TimeoutSeconds),
		MaxRetries:             int32(config.MaxRetries),
		RetryDelaySeconds:      int32(config.RetryDelaySeconds),
		MaxConcurrent:          int32(config.MaxConcurrent),
		IsActive:               config.IsActive,
		CreatedAt:              timestamppb.New(config.CreatedAt),
		UpdatedAt:              timestamppb.New(config.UpdatedAt),
	}

	// Handle optional fields
	if config.LastUsedAt != nil {
		pbConfig.LastUsedAt = timestamppb.New(*config.LastUsedAt)
	}

	return pbConfig
}

func CrawlConfigsToProto(configs []*querycrawlconfig.CrawlConfig) []*pb.CrawlConfig {
	pbConfigs := make([]*pb.CrawlConfig, len(configs))
	for i, config := range configs {
		pbConfigs[i] = CrawlConfigToProto(config)
	}
	return pbConfigs
}
