package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeactivateMarket struct {
	MarketId      int
	DeactivatedBy string
}

type DeactivateMarket<PERSON>andler decorator.CommandHandler[DeactivateMarket]

type deactivate<PERSON>arket<PERSON><PERSON>ler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewDeactivateMarketHandler(
	repo repository.RepositoryMarket,
	log logger.Logger,
	client decorator.MetricsClient,
	broker message.Broker,
) DeactivateMarketHandler {
	return decorator.ApplyCommandDecorators[DeactivateMarket](
		deactivateMarketHandler{
			repo:   repo,
			broker: broker,
		},
		log,
		client,
	)
}

func (h deactivateMarketHandler) Handle(ctx context.Context, cmd DeactivateMarket) error {
	return h.repo.Update(ctx, cmd.MarketId, func(m *entity.Market) (*entity.Market, error) {
		err := m.Deactivate()
		if err != nil {
			return nil, err
		}
		return m, nil
	})
}
