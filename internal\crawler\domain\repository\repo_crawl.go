package repository

import (
	"context"
	"go_core_market/internal/crawler/domain/value_object"
)

type Crawler interface {
	SetConfig(config *value_object.Config) error
	CrawlNormal(ctx context.Context, page int) ([]*value_object.CrawledPrice, int, error)
	CrawlItems(ctx context.Context, page int) ([]*value_object.CrawledItem, error)
	CrawlDoppler(ctx context.Context, id int, typeDoppler []value_object.ConditionDoppler) ([]*value_object.CrawledPrice, error)
	CrawlFloat(ctx context.Context, id int, float []value_object.ConditionFloat) ([]*value_object.CrawledPrice, error)
}
