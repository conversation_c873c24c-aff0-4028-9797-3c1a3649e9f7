syntax = "proto3";

package auth_crawl.v1;

option go_package = "go_core_market/pkg/pb/auth_crawl";

import "google/protobuf/timestamp.proto";

// AuthCrawl message
message AuthCrawl {
  int32 id = 1;
  int32 market_id = 2;
  string auth_type = 3;
  string value = 4;
  bool is_active = 5;
  bool is_use = 6;
  google.protobuf.Timestamp last_used_at = 7;
  google.protobuf.Timestamp expired_at = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
}

// Request/Response messages for Commands
message CreateAuthRequest {
  int32 market_id = 1;
  string auth_type = 2;
  string value = 3;
  bool is_active = 4;
  google.protobuf.Timestamp expired_at = 5;
  string created_by = 6;
}

message CreateAuthResponse {
  bool success = 1;
  string message = 2;
}

message CreateAuthBatchRequest {
  repeated CreateAuthRequest auths = 1;
  string created_by = 2;
}

message CreateAuthBatchResponse {
  bool success = 1;
  string message = 2;
}

message UpdateAuthRequest {
  int32 id = 1;
  int32 market_id = 2;
  string auth_type = 3;
  string value = 4;
  google.protobuf.Timestamp expired_at = 5;
  string updated_by = 6;
}

message UpdateAuthResponse {
  bool success = 1;
  string message = 2;
}

message DeleteAuthRequest {
  int32 id = 1;
  string deleted_by = 2;
}

message DeleteAuthResponse {
  bool success = 1;
  string message = 2;
}

message ActivateAuthRequest {
  int32 id = 1;
  string activated_by = 2;
}

message ActivateAuthResponse {
  bool success = 1;
  string message = 2;
}

message DeactivateAuthRequest {
  int32 id = 1;
  string deactivated_by = 2;
}

message DeactivateAuthResponse {
  bool success = 1;
  string message = 2;
}

message MarkAuthUsedRequest {
  int32 id = 1;
  string used_by = 2;
}

message MarkAuthUsedResponse {
  bool success = 1;
  string message = 2;
}

message MarkAuthsUsedRequest {
  repeated int32 ids = 1;
  string used_by = 2;
}

message MarkAuthsUsedResponse {
  bool success = 1;
  string message = 2;
}

message ReleaseAuthRequest {
  int32 id = 1;
  string released_by = 2;
}

message ReleaseAuthResponse {
  bool success = 1;
  string message = 2;
}

message ReleaseManyAuthRequest {
  repeated int32 ids = 1;
  string released_by = 2;
}

message ReleaseManyAuthResponse {
  bool success = 1;
  string message = 2;
}

// Request/Response messages for Queries
message GetAllAuthCrawlRequest {
  int32 page = 1;
  int32 page_size = 2;
  string order = 3;
}

message GetAllAuthCrawlResponse {
  repeated AuthCrawl auths = 1;
  int32 total = 2;
  int32 page = 3;
  int32 page_size = 4;
  int32 total_pages = 5;
}

message GetAuthCrawlByIdRequest {
  int32 id = 1;
}

message GetAuthCrawlByIdResponse {
  AuthCrawl auth = 1;
}

message GetAuthCrawlByMarketIdRequest {
  int32 market_id = 1;
}

message GetAuthCrawlByMarketIdResponse {
  AuthCrawl auth = 1;
}

message GetAvailableAuthCrawlByMarketRequest {
  int32 market_id = 1;
  int32 limit = 2;
}

message GetAvailableAuthCrawlByMarketResponse {
  repeated AuthCrawl auths = 1;
}

// gRPC Service
service AuthCrawlService {
  // Commands
  rpc CreateAuth(CreateAuthRequest) returns (CreateAuthResponse);
  rpc CreateAuthBatch(CreateAuthBatchRequest) returns (CreateAuthBatchResponse);
  rpc UpdateAuth(UpdateAuthRequest) returns (UpdateAuthResponse);
  rpc DeleteAuth(DeleteAuthRequest) returns (DeleteAuthResponse);
  rpc ActivateAuth(ActivateAuthRequest) returns (ActivateAuthResponse);
  rpc DeactivateAuth(DeactivateAuthRequest) returns (DeactivateAuthResponse);
  rpc MarkAuthUsed(MarkAuthUsedRequest) returns (MarkAuthUsedResponse);
  rpc MarkAuthsUsed(MarkAuthsUsedRequest) returns (MarkAuthsUsedResponse);
  rpc ReleaseAuth(ReleaseAuthRequest) returns (ReleaseAuthResponse);
  rpc ReleaseManyAuth(ReleaseManyAuthRequest) returns (ReleaseManyAuthResponse);

  // Queries
  rpc GetAllAuthCrawl(GetAllAuthCrawlRequest) returns (GetAllAuthCrawlResponse);
  rpc GetAuthCrawlById(GetAuthCrawlByIdRequest) returns (GetAuthCrawlByIdResponse);
  rpc GetAuthCrawlByMarketId(GetAuthCrawlByMarketIdRequest) returns (GetAuthCrawlByMarketIdResponse);
  rpc GetAvailableAuthCrawlByMarket(GetAvailableAuthCrawlByMarketRequest) returns (GetAvailableAuthCrawlByMarketResponse);
}
