package commandmarket

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type CreateMarket struct {
	Name             string
	DisplayName      string
	Type             string
	BaseURL          string
	Currency         string
	BuyerFeePercent  float64
	SellerFeePercent float64
	CountryCode      string
	Language         string
	Description      string
	CreatedBy        string
}

type CreateMarketHandler decorator.CommandHandler[CreateMarket]

type createMarketHandler struct {
	repo   repository.RepositoryMarket
	broker message.Broker
}

func NewCreateMarketHandler(
	repo repository.RepositoryMarket,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateMarketHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateMarket](
		createMarketHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createMarketHandler) Handle(ctx context.Context, cmd CreateMarket) error {
	// Convert SupportedGame to domain.SupportedGame

	// Create new market
	market, err := entity.NewMarket(entity.MarketParams{
		Name:             cmd.Name,
		DisplayName:      cmd.DisplayName,
		Type:             cmd.Type,
		BaseURL:          cmd.BaseURL,
		Currency:         cmd.Currency,
		BuyerFeePercent:  cmd.BuyerFeePercent,
		SellerFeePercent: cmd.SellerFeePercent,
		CountryCode:      cmd.CountryCode,
		Language:         cmd.Language,
		Description:      cmd.Description,
	})
	if err != nil {
		return err
	}

	// Save to repository
	err = h.repo.Create(ctx, market)
	if err != nil {
		return err
	}

	return nil
}
