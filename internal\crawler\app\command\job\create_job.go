package commandjob

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
	"time"
)

type CreateJob struct {
	JobType        string
	Name           string
	Description    *string
	CrawlConfigId  *int
	ScheduledAt    *time.Time
	MaxRetries     int
	TimeoutSeconds int
	CreatedBy      string
}

type CreateJob<PERSON>andler decorator.CommandHandler[CreateJob]

type createJobHandler struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewCreateJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) CreateJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[CreateJob](
		createJobHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h createJobHandler) Handle(ctx context.Context, cmd CreateJob) error {
	// Create domain job
	jobParams := entity.JobParams{
		Type:          cmd.JobType,
		Name:          cmd.Name,
		Description:   cmd.Description,
		ScheduledAt:   cmd.ScheduledAt,
		MaxRetries:    cmd.MaxRetries,
		Timeout:       cmd.TimeoutSeconds,
		CreatedBy:     cmd.CreatedBy,
		CrawlConfigId: cmd.CrawlConfigId,
	}

	job, err := entity.NewJob(jobParams)
	if err != nil {
		return err
	}

	err = h.repo.Create(ctx, job)
	if err != nil {
		return err
	}

	return nil
}
