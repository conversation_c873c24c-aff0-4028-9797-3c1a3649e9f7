package main

import (
	"context"
	"go_core_market/internal/crawler/adapter/clienthttp"
	"go_core_market/internal/crawler/adapter/crawler/youpin"
	"go_core_market/internal/crawler/domain/value_object"
	"log"
)

// ExampleUsage demonstrates how to use the Buff163Crawler
func main() {
	// 1. Create HTTP client
	client := clienthttp.NewRestyClient()
	client.SetBaseURL("https://api.youpin898.com")
	// 2. Create Buff163Crawler
	crawler := youpin.NewYoupinCrawler(client)
	sdt383282653 := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJhZDA0N2U5M2U1ZDc0ZTYyYWNjOTJjYzYxMTkwZjhjYSIsIm5hbWVpZCI6IjEyNDMzNDczIiwiSWQiOiIxMjQzMzQ3MyIsInVuaXF1ZV9uYW1lIjoiWVAwMDEyNDMzNDczIiwiTmFtZSI6IllQMDAxMjQzMzQ3MyIsInZlcnNpb24iOiJKeGsiLCJuYmYiOjE3NTMxODEyNTUsImV4cCI6MTc1NDA0NTI1NSwiaXNzIjoieW91cGluODk4LmNvbSIsImRldmljZUlkIjoiODVmMWIxOWItZmRiYy00Njg2LWE1NzMtODY1OTVhNDQyN2MxIiwiYXVkIjoidXNlciJ9.0F7DJLg3k9vIzB-YPk1kEraMLbZIFg5chAT27ubyC08"
	sdt384634549 := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI5MTU5MDliYWVjNzM0ZTdiYjQ4M2U1NDNkNTljYWVmNiIsIm5hbWVpZCI6IjEyNDMzNTA2IiwiSWQiOiIxMjQzMzUwNiIsInVuaXF1ZV9uYW1lIjoiWVAwMDEyNDMzNTA2IiwiTmFtZSI6IllQMDAxMjQzMzUwNiIsInZlcnNpb24iOiJoZmUiLCJuYmYiOjE3NTMxODE0ODUsImV4cCI6MTc1NDA0NTQ4NSwiaXNzIjoieW91cGluODk4LmNvbSIsImRldmljZUlkIjoiOWRhMzUzZjAtMWQ1Ny00MDU4LWIxZTEtOTAzNjY3Mjk1YTYwIiwiYXVkIjoidXNlciJ9.IONjx_eO4hwyPQNfDRIjuT_H2ReyBySOr7-_xcfc3RY"
	sdt377443278 := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJqdGkiOiJkZDk1YWU4NzY5OWM0NmY4OGYxYTA5YmQ2ODQ0YTc4ZiIsIm5hbWVpZCI6IjEyNDMzNTI4IiwiSWQiOiIxMjQzMzUyOCIsInVuaXF1ZV9uYW1lIjoiWVAwMDEyNDMzNTI4IiwiTmFtZSI6IllQMDAxMjQzMzUyOCIsInZlcnNpb24iOiJXVjYiLCJuYmYiOjE3NTMxODE2MjQsImV4cCI6MTc1NDA0NTYyNCwiaXNzIjoieW91cGluODk4LmNvbSIsImRldmljZUlkIjoiYmMxY2JjNDEtNzczMi00NzVkLWExMjMtZTM4MGJlMTQ0NWMwIiwiYXVkIjoidXNlciJ9.njXRlUWIBO7B3cwq_hPYsZ_lBmDYDo3UNEhpO-SH8lg"
	sdt369039657 := "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI0MzI0NmJmMmNiMzE0ZDZmOTgzZjIzNGFjNzUyZWNhMSIsIm5hbWVpZCI6Ijk5NDc3MTgiLCJJZCI6Ijk5NDc3MTgiLCJ1bmlxdWVfbmFtZSI6IllQMDAwOTk0NzcxOCIsIk5hbWUiOiJZUDAwMDk5NDc3MTgiLCJ2ZXJzaW9uIjoiRk9JIiwibmJmIjoxNzUzMTgyNzA5LCJleHAiOjE3NTQwNDY3MDksImlzcyI6InlvdXBpbjg5OC5jb20iLCJkZXZpY2VJZCI6Ijc0MTUyYjEwLWM5OGUtNDY5MS05ZmMyLTg4OGNjNDBhMDhmMiIsImF1ZCI6InVzZXIifQ.cOfrj9imA4uHfkXOfExQprjbtQ-TJFsUXWW_ZlFsZ4Q"
	proxies := make([]string, 0, 1)
	cookies := make([]string, 0, 1)
	cookies = append(cookies, sdt383282653)
	cookies = append(cookies, sdt384634549)
	cookies = append(cookies, sdt377443278)
	cookies = append(cookies, sdt369039657)

	//proxies = append(proxies, "************************************************")
	//proxies = append(proxies, "*********************************************")
	proxies = append(proxies, "************************************************")
	//proxies = append(proxies, "*************************************************")
	proxies = append(proxies, "**************************************************")
	proxies = append(proxies, "*********************************************")
	//proxies = append(proxies, "*********************************************")
	//proxies = append(proxies, "********************************************")
	//proxies = append(proxies, "*************************************************")
	//proxies = append(proxies, "********************************************")
	//proxies = append(proxies, "*******************************************")
	//proxies = append(proxies, "http://i27g7pjz:ZnQpPYQqCL2w@************:3139")
	proxies = append(proxies, "**********************************************")
	//cookies = append(cookies, cookieStr)
	config := value_object.Config{
		Cookies:           nil,
		APIKeys:           cookies,
		Proxies:           proxies,
		MarketID:          1,
		RequestsPerMinute: 60,
		PerRequestDelay:   1,
		Timeout:           3,
		MaxRetries:        1,
		RetryDelay:        1,
	}
	// Set the config to crawler
	err := crawler.SetConfig(&config)
	if err != nil {
		log.Fatalf("Failed to set config: %v", err)
	}
	ctx := context.Background()

	for i := 1; i < 200; i++ {
		price, total, err := crawler.CrawlNormal(ctx, i)
		if i == 1 {
			log.Println(total)
		}
		if err != nil {
			log.Println("Failed to crawl: %v", err)
			continue
		}
		if len(price) > 0 {
			log.Println(price[0].ItemName)
		} else {
			log.Printf("No items on page %d", i)
		}
	}

	/*typeFloat := make([]value_object.ConditionFloat, 0)
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.15-0.18",
		FloatMin: 0.15,
		FloatMax: 0.18,
	})
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.18-0.22",
		FloatMin: 0.18,
		FloatMax: 0.22,
	})
	typeFloat = append(typeFloat, value_object.ConditionFloat{
		Name:     "0.22-0.25",
		FloatMin: 0.22,
		FloatMax: 0.25,
	})
	prices, err := crawler.CrawlFloat(ctx, 45508, "★ Specialist Gloves | Fade (Field-Tested)", typeFloat)
	if err != nil {
		log.Fatalf("Failed to crawl: %v", err)
	}
	for _, price := range prices {
		log.Println(price.SellPrice, price.BuyPrice, price.Condition.GetCondition(), price.NumSell, price.NumBuy)
	}*/

}
