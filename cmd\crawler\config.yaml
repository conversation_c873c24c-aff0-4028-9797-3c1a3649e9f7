# Crawler Service Configuration
# This configuration file contains all settings for the crawler service

# Database Configuration
database:
  driver: "postgres"
  host: "localhost"
  port: 5432
  database: "market"
  username: "postgres"
  password: "secret"
  max_open_conns: 25
  max_idle_conns: 10
  conn_max_lifetime: "1h"
  ssl_mode: "disable"

# Server Configuration
server:
  host: "0.0.0.0"
  port: 8888  # Different port for crawler service
  api_key: "crawler-service-api-key-2024"

# Kafka Configuration
kafka:
  brokers:
    - "localhost:9092"
  group_id: "crawler-service-group"
  batch_size: 100
  batch_timeout: "5s"
  read_batch_size: 50
  commit_interval: "1s"
  start_offset: -1  # Latest offset

# Logger Configuration
logger:
  level: "info"
  format: "json"
  output:
    - "stdout"
    - "file"
  service_name: "crawler-service"
  environment: "development"
  file:
    enabled: true
    path: "logs/crawler-service.log"
    max_size: 100  # MB
    max_backups: 5
    max_age: 30    # days
    compress: true