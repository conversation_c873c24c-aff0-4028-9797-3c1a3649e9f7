package commandcrawlconfig

import (
	"context"
	"go_core_market/internal/crawler/domain/entity"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

// ActivateCrawlConfig command
type ActivateCrawlConfig struct {
	ID          int
	ActivatedBy string
}

type ActivateCrawlConfigHandler decorator.CommandHandler[ActivateCrawlConfig]

type activateCrawlConfigHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewActivateCrawlConfigHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) ActivateCrawlConfigHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[ActivateCrawlConfig](
		activateCrawlConfigHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h activateCrawlConfigHandler) Handle(ctx context.Context, cmd ActivateCrawlConfig) error {
	return h.repo.Update(ctx, cmd.ID, func(c *entity.CrawlConfig) (*entity.CrawlConfig, error) {
		err := c.Activate()
		if err != nil {
			return nil, err
		}
		return c, nil
	})
}

// DeactivateCrawlConfig command
type DeactivateCrawlConfig struct {
	ID            int
	DeactivatedBy string
}

type DeactivateCrawlConfigHandler decorator.CommandHandler[DeactivateCrawlConfig]

type deactivateCrawlConfigHandler struct {
	repo   repository.RepositoryConfigCrawl
	broker message.Broker
}

func NewDeactivateCrawlConfigHandler(
	repo repository.RepositoryConfigCrawl,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeactivateCrawlConfigHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[DeactivateCrawlConfig](
		deactivateCrawlConfigHandler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deactivateCrawlConfigHandler) Handle(ctx context.Context, cmd DeactivateCrawlConfig) error {
	return h.repo.Update(ctx, cmd.ID, func(c *entity.CrawlConfig) (*entity.CrawlConfig, error) {
		err := c.Deactivate()
		if err != nil {
			return nil, err
		}
		return c, nil
	})
}
