package queryauthcral

import (
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
)

type AuthCrawlQueries struct {
	GetAllAuthCrawl               GetAllAuthCrawlQueryHandler
	GetAuthCrawlById              GetAuthCrawlByIdQueryHandler
	GetAuthCrawlByMarketId        GetAuthCrawlByMarketIdQueryHandler
	GetAvailableAuthCrawlByMarket GetAvailableAuthCrawlByMarketQueryHandler
}

func NewAuthCrawlQueries(
	repo RepositoryQueryAuth,
	logger logger.Logger,
	metricsClient decorator.MetricsClient) *AuthCrawlQueries {
	return &AuthCrawlQueries{
		GetAuthCrawlById:              NewGetAuthCrawlByIdQueryHandler(repo, logger, metricsClient),
		GetAuthCrawlByMarketId:        NewGetAuthCrawlByMarketIdQueryHandler(repo, logger, metricsClient),
		GetAvailableAuthCrawlByMarket: NewGetAvailableAuthCrawlByMarketQueryHandler(repo, logger, metricsClient),
		GetAllAuthCrawl:               NewGetAllAuthCrawlQueryHandler(repo, logger, metricsClient),
	}
}
