package commandjob

import (
	"context"
	"go_core_market/internal/crawler/domain/repository"
	"go_core_market/pkg/decorator"
	"go_core_market/pkg/logger"
	"go_core_market/pkg/message"
)

type DeleteJob struct {
	ID        int
	DeletedBy string
}

type Delete<PERSON><PERSON><PERSON><PERSON><PERSON> decorator.CommandHandler[Delete<PERSON><PERSON>]

type delete<PERSON><PERSON><PERSON><PERSON><PERSON> struct {
	repo   repository.RepositoryJob
	broker message.Broker
}

func NewDeleteJobHandler(
	repo repository.RepositoryJob,
	logger logger.Logger,
	metricsClient decorator.MetricsClient,
	broker message.Broker,
) DeleteJobHandler {
	if repo == nil {
		panic("nil repo")
	}

	return decorator.ApplyCommandDecorators[Delete<PERSON><PERSON>](
		deleteJob<PERSON>andler{repo: repo, broker: broker},
		logger,
		metricsClient,
	)
}

func (h deleteJobHandler) Handle(ctx context.Context, cmd DeleteJob) error {
	return h.repo.Delete(ctx, cmd.ID)
}
